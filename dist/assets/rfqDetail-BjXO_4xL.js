import{r as q,p as me,c as B,m as ye,b as v,g as c,d as t,w as a,h as w,n as k,u as fe,o as s,f,k as r,j as n,t as u,l as z,D as _e,G as ke,F as _}from"./index-CAFrc3c2.js";import{S as ge}from"./supplierModal-DywXeZKi.js";import{_ as xe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const he={class:"rfq-detail-container"},we={class:"info-item"},Se={class:"value"},Ie={class:"info-item"},qe={class:"value"},be={class:"info-item"},Ce={class:"value"},Pe={class:"info-item"},Me={class:"value"},Te={class:"info-item"},$e={class:"value"},Be={class:"info-item"},De={class:"value"},Ae={class:"info-item"},Oe={class:"value"},Ne={class:"info-item"},je={class:"value"},Fe={class:"info-item"},Qe={class:"value"},Ee={class:"info-item"},Ve={class:"value important"},Ke={class:"table-operations"},Le={class:"selection-summary"},ze={style:{color:"#666"}},Ge={class:"summary-content"},Ue=["onClick"],He=["onClick"],Je=["onClick"],We=["onClick"],Xe=["onClick"],Ye=["onClick"],Ze={style:{margin:"12px"}},Re={class:"bottom-actions"},et={__name:"rfqDetail",setup(tt){const G=me(),U=fe(),K=q(G.params.rfqNo),p=q({id:"",rfqNo:"RFQ-2023-0001",status:"inProgress",createTime:"2023-10-15 09:30:00",rfqTime:"2023-10-15 10:00:00",deadline:"2023-10-25 18:00:00",endTime:"2023-10-23 16:30:00",creator:"张三",contactPhone:"13800138000",materialModelCount:4,materials:[]}),D=q([]),S=q([]),P=q(!1),H=q(!1),A=q(null),J=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100},{title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120},{title:"单价 (¥)",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:100},{title:"交期",dataIndex:"delivery",key:"delivery",width:100},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:150},{title:"结束时间",dataIndex:"endTime",key:"endTime",width:150},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:150},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:275}],W=l=>{const e=[];return e.push({title:"供应商",dataIndex:"supplierName",key:"supplierName",width:150,customRender:({text:d,record:m})=>d||m.name||"-"}),l&&e.push({title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120}),e.push({title:"报价 (¥)",dataIndex:"price",key:"price",width:100},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:150},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:120},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:120},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150},{title:"报价状态",dataIndex:"status",key:"status",width:100},{title:"报价类型",dataIndex:"quoteType",key:"quoteType",width:100,customRender:({text:d})=>d==="platform"?"平台报价":"外部报价"},{title:"操作",dataIndex:"action",key:"action",width:100}),e},E=B(()=>p.value.status==="notStarted"),O=B(()=>p.value.status==="notStarted"),N=B(()=>p.value.status==="accepted"),V=B(()=>["accepted","expired","invalid","cancelled"].includes(p.value.status)),j=B(()=>p.value.status==="inProgress"),X=()=>{U.go(-1)},I=l=>l||"",Y=l=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[l]||"default",Z=l=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[l]||"未知",R=()=>p.value.materials.reduce((l,e)=>l+e.quantity,0),L=()=>`¥${p.value.materials.filter(d=>S.value.includes(d.id)).reduce((d,m)=>{const b=m.selectedSupplier?m.selectedSupplier.totalPrice:0;return d+b},0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})}`,ee=l=>{S.value=l},te=(l,e)=>{if(l)D.value.push(e.id);else{const d=D.value.indexOf(e.id);d>-1&&D.value.splice(d,1)}},ae=(l,e)=>{if(e){e.suppliers.forEach(m=>{m.isSelected=!1});const d=e.suppliers.find(m=>m.id===l.id);d&&(d.isSelected=!0,e.selectedSupplier=d,e.acceptAlternative&&d.alternativeBrand&&d.alternativeModel&&(e.alternativeBrand=d.alternativeBrand,e.alternativeModel=d.alternativeModel),k.success(`已选择供应商：${d.name}`))}},ne=l=>{A.value=l,P.value=!0},le=l=>{k.info(`启动询价：${l.name}`)},ie=l=>{k.info(`转采购单：${l.name}`)},se=l=>{k.info(`再次询价：${l.name}`)},de=l=>{k.info(`取消询价：${l.name}`)},oe=l=>{k.info(`查看询价历史：${l.name}`)},re=({key:l})=>{if(S.value.length===0){k.warning("请先选择要操作的物料");return}const e=p.value.materials.filter(d=>S.value.includes(d.id));switch(l){case"configSupplier":k.info(`批量配置供应商：${e.length}个物料`);break;case"startInquiry":k.info(`批量启动询价：${e.length}个物料`);break;case"toPurchaseOrder":k.info(`批量转采购单：${e.length}个物料`);break;case"cancel":k.info(`批量取消：${e.length}个物料`);break}},ue=()=>{if(S.value.length===0){k.warning("请先选择要分配的物料");return}k.info("智能分配供应商")},ce=l=>{A.value&&l&&l.length>0&&(l.forEach(e=>{if(A.value.suppliers.findIndex(m=>m.id===e.id)===-1){const m={id:e.id,name:e.name,supplierName:e.name,price:null,totalPrice:null,minOrderQuantity:1,promisedDelivery:"",validityPeriod:"",quoteTime:"",status:"pending",isSelected:!1,quoteType:"platform"};A.value.suppliers.push(m)}}),P.value=!1,k.success(`已成功添加${l.length}个供应商`))},pe=()=>{P.value=!1},ve=async()=>{try{console.log("获取询价单详情，询价单号：",K.value),await new Promise(l=>setTimeout(l,500)),p.value.rfqNo=K.value,p.value.materials=Array.from({length:4}).map((l,e)=>{const d=Math.floor(Math.random()*100+10),m=e%4===0?"notStarted":e%4===1?"inProgress":e%4===2?"accepted":"expired",b=Array.from({length:3}).map((h,g)=>{const T=g%4===0?"pending":g%4===1?"quoted":g%4===2?"rejected":"expired",Q=Math.floor(Math.random()*1e3+100),C=g===1,$=["兼容品牌A","兼容品牌B","通用品牌C"],y=["ALT-001","COMP-002","GEN-003"],o=g%2===0?"platform":"external",i={platform:["严选供应商","深圳市电子科技有限公司","北京智能制造有限公司"],external:["上海精密器件有限公司","广州电子元件供应商","天津工业设备公司"]};return{id:`supplier-${e}-${g}`,name:`供应商 ${g+1}`,supplierName:i[o][g%i[o].length],price:Q,totalPrice:Q*d,minOrderQuantity:Math.floor(Math.random()*100+10),promisedDelivery:`${Math.floor(Math.random()*30+15)}天`,validityPeriod:"2023-11-05",quoteTime:"2023-10-16 10:30:00",status:T,isSelected:!1,quoteType:o,alternativeBrand:C&&T==="quoted"?$[g%$.length]:null,alternativeModel:C&&T==="quoted"?y[g%y.length]:null}});let x=null,F=null,M=null;if(m==="accepted"){const h=b.find(g=>g.status==="quoted");h&&(h.isSelected=!0,x=h,e%3===0&&h.alternativeBrand&&h.alternativeModel&&(F=h.alternativeBrand,M=h.alternativeModel))}return{id:`material-${e}`,name:`测试物料 ${e+1}`,model:`MODEL-${100+e}`,brand:e%3===0?"A":e%3===1?"B":"C",quantity:d,expectedDelivery:"30天",rfqTime:m==="notStarted"?"":"2023-10-15 10:00:00",endTime:"2023-10-23 16:30:00",deadline:"2023-10-25 18:00:00",status:m,suppliers:b,selectedSupplier:x,acceptAlternative:e%3===0,alternativeBrand:F,alternativeModel:M}})}catch{k.error("获取询价单详情失败")}};return ye(()=>{ve()}),(l,e)=>{const d=w("a-button"),m=w("a-space"),b=w("a-page-header"),x=w("a-col"),F=w("a-row"),M=w("a-card"),h=w("a-menu-item"),g=w("a-menu"),T=w("a-dropdown"),Q=w("a-tooltip"),C=w("a-tag"),$=w("a-table");return s(),v("div",he,[c(b,{title:"询价单详情 - "+p.value.rfqNo,onBack:X},{extra:a(()=>[c(m,null,{default:a(()=>[O.value?(s(),f(d,{key:0,type:"primary"},{default:a(()=>e[1]||(e[1]=[n("启动询价")])),_:1})):r("",!0),N.value?(s(),f(d,{key:1,type:"primary"},{default:a(()=>e[2]||(e[2]=[n("转采购单")])),_:1})):r("",!0),V.value?(s(),f(d,{key:2},{default:a(()=>e[3]||(e[3]=[n("再次询价")])),_:1})):r("",!0),j.value?(s(),f(d,{key:3,danger:""},{default:a(()=>e[4]||(e[4]=[n("取消询价")])),_:1})):r("",!0)]),_:1})]),_:1},8,["title"]),c(M,{title:"基本信息",class:"detail-card"},{default:a(()=>[c(F,{gutter:24},{default:a(()=>[c(x,{span:8},{default:a(()=>[t("div",we,[e[5]||(e[5]=t("span",{class:"label"},"询价单号：",-1)),t("span",Se,u(p.value.rfqNo),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Ie,[e[6]||(e[6]=t("span",{class:"label"},"创建时间：",-1)),t("span",qe,u(I(p.value.createTime)),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",be,[e[7]||(e[7]=t("span",{class:"label"},"询价时间：",-1)),t("span",Ce,u(p.value.status==="notStarted"?"-":I(p.value.rfqTime)),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Pe,[e[8]||(e[8]=t("span",{class:"label"},"截止时间：",-1)),t("span",Me,u(I(p.value.deadline)),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Te,[e[9]||(e[9]=t("span",{class:"label"},"结束时间：",-1)),t("span",$e,u(p.value.status==="inProgress"?"-":I(p.value.endTime)),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Be,[e[10]||(e[10]=t("span",{class:"label"},"创建人：",-1)),t("span",De,u(p.value.creator),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Ae,[e[11]||(e[11]=t("span",{class:"label"},"联系电话：",-1)),t("span",Oe,u(p.value.contactPhone),1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Ne,[e[12]||(e[12]=t("span",{class:"label"},"物料型号数：",-1)),t("span",je,u(p.value.materialModelCount)+" 种",1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Fe,[e[13]||(e[13]=t("span",{class:"label"},"物料总数：",-1)),t("span",Qe,u(R())+" 件",1)])]),_:1}),c(x,{span:8},{default:a(()=>[t("div",Ee,[e[14]||(e[14]=t("span",{class:"label"},"物料总价：",-1)),t("span",Ve,u(L()),1)])]),_:1})]),_:1})]),_:1}),c(M,{title:"物料信息",class:"detail-card"},{default:a(()=>[t("div",Ke,[c(m,null,{default:a(()=>[c(T,null,{overlay:a(()=>[c(g,{onClick:re},{default:a(()=>[E.value?(s(),f(h,{key:"configSupplier"},{default:a(()=>e[16]||(e[16]=[n("配置供应商")])),_:1})):r("",!0),O.value?(s(),f(h,{key:"startInquiry"},{default:a(()=>e[17]||(e[17]=[n("启动询价")])),_:1})):r("",!0),N.value?(s(),f(h,{key:"toPurchaseOrder"},{default:a(()=>e[18]||(e[18]=[n("转采购单")])),_:1})):r("",!0),j.value?(s(),f(h,{key:"cancel"},{default:a(()=>e[19]||(e[19]=[n("取消")])),_:1})):r("",!0)]),_:1})]),default:a(()=>[c(d,{type:"primary"},{default:a(()=>[e[15]||(e[15]=n(" 批量操作 ")),c(z(_e))]),_:1})]),_:1}),E.value?(s(),f(d,{key:0,type:"primary",onClick:ue},{default:a(()=>e[20]||(e[20]=[n("智能分配")])),_:1})):r("",!0)]),_:1})]),t("div",Le,[t("div",null,[c(Q,{placement:"top"},{title:a(()=>e[21]||(e[21]=[t("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),t("div",null,[n("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),t("br"),n("订单总金额¥0.00 - ¥499.99，运费¥15.00"),t("br"),n("订单总金额¥500.00 - ¥999.99，运费¥8.00"),t("br"),n("订单总金额¥1000以上，免运费")],-1)])),default:a(()=>[t("span",ze,[c(z(ke),{style:{"margin-right":"4px"}}),e[22]||(e[22]=n("价格与运费说明"))])]),_:1})]),t("div",Ge,[t("span",null,[e[23]||(e[23]=n("已选择：")),c(C,{color:"blue"},{default:a(()=>[n(u(S.value.length),1)]),_:1}),e[24]||(e[24]=n(" 个物料"))]),t("span",null,[e[25]||(e[25]=n("总金额：")),c(C,{color:"red"},{default:a(()=>[n(u(L()),1)]),_:1})])])]),c($,{columns:J,"data-source":p.value.materials,size:"middle",pagination:{pageSize:20},"row-key":"id","row-selection":{selectedRowKeys:S.value,onChange:ee},bordered:"",scroll:{x:1500},expandedRowKeys:D.value,onExpand:te},{bodyCell:a(({column:y,record:o})=>[y.dataIndex==="acceptAlternative"?(s(),v(_,{key:0},[n(u(o.acceptAlternative?"是":"否"),1)],64)):r("",!0),y.dataIndex==="alternativeBrand"?(s(),v(_,{key:1},[n(u(o.acceptAlternative&&o.alternativeBrand?o.alternativeBrand:"-"),1)],64)):r("",!0),y.dataIndex==="alternativeModel"?(s(),v(_,{key:2},[n(u(o.acceptAlternative&&o.alternativeModel?o.alternativeModel:"-"),1)],64)):r("",!0),y.dataIndex==="unitPrice"?(s(),v(_,{key:3},[n(u(o.selectedSupplier?`¥${o.selectedSupplier.price.toFixed(2)}`:"-"),1)],64)):r("",!0),y.dataIndex==="totalPrice"?(s(),v(_,{key:4},[n(u(o.selectedSupplier?`¥${o.selectedSupplier.totalPrice.toFixed(2)}`:"-"),1)],64)):r("",!0),y.dataIndex==="minOrderQuantity"?(s(),v(_,{key:5},[n(u(o.selectedSupplier?o.selectedSupplier.minOrderQuantity:"-"),1)],64)):r("",!0),y.dataIndex==="delivery"?(s(),v(_,{key:6},[n(u(o.selectedSupplier?o.selectedSupplier.promisedDelivery:"-"),1)],64)):r("",!0),y.dataIndex==="rfqTime"?(s(),v(_,{key:7},[n(u(p.value.status==="notStarted"?"-":I(o.rfqTime)),1)],64)):r("",!0),y.dataIndex==="endTime"?(s(),v(_,{key:8},[n(u(p.value.status==="inProgress"?"-":I(o.endTime)),1)],64)):r("",!0),y.dataIndex==="action"?(s(),f(m,{key:9},{default:a(()=>[E.value?(s(),v("a",{key:0,onClick:i=>ne(o)},"配置供应商",8,Ue)):r("",!0),O.value?(s(),v("a",{key:1,onClick:i=>le(o)},"启动询价",8,He)):r("",!0),N.value?(s(),v("a",{key:2,onClick:i=>ie(o)},"转采购单",8,Je)):r("",!0),V.value?(s(),v("a",{key:3,onClick:i=>se(o)},"再次询价",8,We)):r("",!0),j.value?(s(),v("a",{key:4,onClick:i=>de(o),class:"danger-link"},"取消",8,Xe)):r("",!0),t("a",{onClick:i=>oe(o)},"询价历史",8,Ye)]),_:2},1024)):r("",!0)]),expandedRowRender:a(({record:y})=>[t("div",Ze,[c($,{size:"small",columns:W(y.acceptAlternative),"data-source":y.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:a(({column:o,record:i})=>[o.dataIndex==="alternativeBrand"?(s(),v(_,{key:0},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.alternativeBrand||"-"),1)],64)):r("",!0),o.dataIndex==="alternativeModel"?(s(),v(_,{key:1},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.alternativeModel||"-"),1)],64)):r("",!0),o.dataIndex==="price"?(s(),v(_,{key:2},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.price?`¥${i.price.toFixed(2)}`:""),1)],64)):r("",!0),o.dataIndex==="totalPrice"?(s(),v(_,{key:3},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.totalPrice?`¥${i.totalPrice.toFixed(2)}`:""),1)],64)):r("",!0),o.dataIndex==="promisedDelivery"?(s(),v(_,{key:4},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.promisedDelivery),1)],64)):r("",!0),o.dataIndex==="validityPeriod"?(s(),v(_,{key:5},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.validityPeriod),1)],64)):r("",!0),o.dataIndex==="quoteTime"?(s(),v(_,{key:6},[n(u(i.status==="pending"||i.status==="rejected"?"-":I(i.quoteTime)),1)],64)):r("",!0),o.dataIndex==="status"?(s(),f(C,{key:7,color:Y(i.status)},{default:a(()=>[n(u(Z(i.status)),1)]),_:2},1032,["color"])):r("",!0),o.dataIndex==="action"?(s(),f(d,{key:8,type:"link",disabled:i.isSelected||i.status==="pending"||i.status==="rejected"||i.quoteType==="external",onClick:at=>ae(i,y)},{default:a(()=>[n(u(i.isSelected?"已选择":"选择"),1)]),_:2},1032,["type","disabled","onClick"])):r("",!0)]),_:2},1032,["columns","data-source"])])]),_:1},8,["data-source","row-selection","expandedRowKeys"])]),_:1}),t("div",Re,[c(m,null,{default:a(()=>[O.value?(s(),f(d,{key:0,type:"primary"},{default:a(()=>e[26]||(e[26]=[n("启动询价")])),_:1})):r("",!0),N.value?(s(),f(d,{key:1,type:"primary"},{default:a(()=>e[27]||(e[27]=[n("转采购单")])),_:1})):r("",!0),V.value?(s(),f(d,{key:2},{default:a(()=>e[28]||(e[28]=[n("再次询价")])),_:1})):r("",!0),j.value?(s(),f(d,{key:3,danger:""},{default:a(()=>e[29]||(e[29]=[n("取消询价")])),_:1})):r("",!0)]),_:1})]),c(ge,{title:"配置供应商",visible:P.value,"confirm-loading":H.value,"onUpdate:visible":e[0]||(e[0]=y=>P.value=y),onOk:ce,onCancel:pe},null,8,["visible","confirm-loading"])])}}},st=xe(et,[["__scopeId","data-v-aac56166"]]);export{st as default};
