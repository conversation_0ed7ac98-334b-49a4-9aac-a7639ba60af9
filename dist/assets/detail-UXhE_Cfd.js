import{r as q,f as R,w as e,d as a,g as t,h as o,j as s,t as c,b,k as P,M as G,o as r,a as ve,F as H,y as Y,n as B,z as V,c as ke}from"./index-CAFrc3c2.js";import{_ as A}from"./_plugin-vue_export-helper-DlAUqK2U.js";const _e={class:"material-info-section"},be={class:"transaction-tabs"},ge={key:0},xe={key:1},he={key:0},Ce={key:1},Pe={__name:"transactionHistory",props:{visible:{type:Boolean,required:!0},material:{type:Object,required:!0},inquiryHistoryData:{type:Array,default:()=>[]},purchaseHistoryData:{type:Array,default:()=>[]}},emits:["update:visible","reinquiry","repurchase"],setup(l,{emit:n}){const D=l,N=n,u=q("inquiry"),y=k=>{N("reinquiry",{material:D.material,record:k})},_=k=>{G.confirm({title:"确认创建采购单",content:`确定要从 ${k.supplier} 再次采购 ${D.material.name} (${D.material.model}) 吗？`,okText:"确认",cancelText:"取消",onOk:()=>{N("repurchase",{material:D.material,record:k})}})},d=[{title:"询价单号",dataIndex:"inquiryNo",key:"inquiryNo",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:70},{title:"询价时间",dataIndex:"date",key:"date",width:100},{title:"供应商",dataIndex:"supplier",key:"supplier",width:200},{title:"报价单价",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价",dataIndex:"totalPrice",key:"totalPrice",width:100},{title:"状态",dataIndex:"status",key:"status",width:80},{title:"响应时间",dataIndex:"responseTime",key:"responseTime",width:100},{title:"交期(天)",dataIndex:"deliveryDays",key:"deliveryDays",width:90},{title:"付款条件",dataIndex:"paymentTerms",key:"paymentTerms",width:150},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:80},{title:"操作",key:"operation",fixed:"right",width:100}],O=[{title:"采购单号",dataIndex:"purchaseNo",key:"purchaseNo",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:70},{title:"采购时间",dataIndex:"date",key:"date",width:100},{title:"供应商",dataIndex:"supplier",key:"supplier",width:200},{title:"采购单价",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价",dataIndex:"totalPrice",key:"totalPrice",width:100},{title:"状态",dataIndex:"status",key:"status",width:80},{title:"收货日期",dataIndex:"receiptDate",key:"receiptDate",width:120},{title:"付款状态",dataIndex:"paymentStatus",key:"paymentStatus",width:100},{title:"付款日期",dataIndex:"paymentDate",key:"paymentDate",width:100},{title:"质保期",dataIndex:"warranty",key:"warranty",width:80},{title:"质检结果",dataIndex:"qualityCheck",key:"qualityCheck",width:90},{title:"操作",key:"operation",fixed:"right",width:100}];return(k,x)=>{const g=o("a-descriptions-item"),w=o("a-descriptions"),h=o("a-tag"),M=o("a-button"),K=o("a-table"),S=o("a-tab-pane"),Q=o("a-tabs"),I=o("a-modal");return r(),R(I,{visible:l.visible,"onUpdate:visible":x[1]||(x[1]=f=>N("update:visible",f)),title:"交易历史",width:"1000px",footer:null},{default:e(()=>[a("div",_e,[x[2]||(x[2]=a("h3",null,"物料基本信息",-1)),t(w,{bordered:"",size:"small",column:3},{default:e(()=>[t(g,{label:"物料名称"},{default:e(()=>[s(c(l.material.name),1)]),_:1}),t(g,{label:"型号"},{default:e(()=>[s(c(l.material.model),1)]),_:1}),t(g,{label:"品牌"},{default:e(()=>[s(c(l.material.brand),1)]),_:1}),t(g,{label:"产品分类"},{default:e(()=>[s(c(l.material.category),1)]),_:1}),t(g,{label:"规格参数"},{default:e(()=>[s(c(l.material.specs||"-"),1)]),_:1}),t(g,{label:"备注"},{default:e(()=>[s(c(l.material.remark||"-"),1)]),_:1})]),_:1})]),a("div",be,[t(Q,{activeKey:u.value,"onUpdate:activeKey":x[0]||(x[0]=f=>u.value=f)},{default:e(()=>[t(S,{key:"inquiry",tab:"询价历史"},{default:e(()=>[t(K,{dataSource:l.inquiryHistoryData,columns:d,size:"small",pagination:{pageSize:5},"row-key":f=>f.id,scroll:{x:1300}},{bodyCell:e(({column:f,record:p})=>[f.key==="unitPrice"?(r(),b("span",ge,"¥"+c(p.unitPrice.toFixed(2)),1)):f.key==="totalPrice"?(r(),b("span",xe,"¥"+c(p.totalPrice.toFixed(2)),1)):f.key==="status"?(r(),R(h,{key:2,color:p.status==="已报价"?"green":p.status==="询价中"?"blue":p.status==="已取消"?"red":p.status==="已过期"?"orange":"default"},{default:e(()=>[s(c(p.status),1)]),_:2},1032,["color"])):f.key==="operation"?(r(),R(M,{key:3,type:"primary",size:"small",onClick:U=>y(p)},{default:e(()=>x[3]||(x[3]=[s(" 再次询价 ")])),_:2},1032,["onClick"])):P("",!0)]),_:1},8,["dataSource","row-key"])]),_:1}),t(S,{key:"purchase",tab:"采购历史"},{default:e(()=>[t(K,{dataSource:l.purchaseHistoryData,columns:O,size:"small",pagination:{pageSize:5},"row-key":f=>f.id,scroll:{x:1300}},{bodyCell:e(({column:f,record:p})=>[f.key==="unitPrice"?(r(),b("span",he,"¥"+c(p.unitPrice.toFixed(2)),1)):f.key==="totalPrice"?(r(),b("span",Ce,"¥"+c(p.totalPrice.toFixed(2)),1)):f.key==="status"?(r(),R(h,{key:2,color:p.status==="已完成"?"green":p.status==="进行中"?"blue":p.status==="待发货"?"orange":p.status==="已取消"?"red":"default"},{default:e(()=>[s(c(p.status),1)]),_:2},1032,["color"])):f.key==="paymentStatus"?(r(),R(h,{key:3,color:p.paymentStatus==="已付款"?"green":p.paymentStatus==="预付款已付"?"blue":p.paymentStatus==="待付款"?"orange":"default"},{default:e(()=>[s(c(p.paymentStatus),1)]),_:2},1032,["color"])):f.key==="qualityCheck"?(r(),R(h,{key:4,color:p.qualityCheck==="合格"?"green":p.qualityCheck==="待检"?"blue":p.qualityCheck==="不合格"?"red":"default"},{default:e(()=>[s(c(p.qualityCheck),1)]),_:2},1032,["color"])):f.key==="operation"?(r(),R(M,{key:5,type:"primary",size:"small",onClick:U=>_(p)},{default:e(()=>x[4]||(x[4]=[s(" 再次购买 ")])),_:2},1032,["onClick"])):P("",!0)]),_:1},8,["dataSource","row-key"])]),_:1})]),_:1},8,["activeKey"])])]),_:1},8,["visible"])}}},we=A(Pe,[["__scopeId","data-v-1673cf7f"]]),Ie={class:"action-bar"},qe={class:"button-group"},Te={class:"filter-row"},Re={style:{"font-weight":"bold"}},$e={style:{"font-size":"12px",color:"#888"}},Se={key:1},De={key:2},Me=["onClick"],Qe={style:{"margin-bottom":"20px"}},Ne={style:{display:"flex","justify-content":"space-between","margin-bottom":"10px"}},Oe={style:{margin:"0"}},Fe=["onClick"],Ke={__name:"index",setup(l){const n=q(""),D=q(""),N=q(""),u=q(""),y=q(!1),_=q([]),d=q(!1);q("inquiry");const O=q({}),k=ve({name:"",deadline:null,description:""}),x=q([{id:"INQ001",inquiryNo:"RFQ-2024-001",quantity:2,date:"2024-03-15",supplier:"上海电气有限公司",unitPrice:1280,totalPrice:2560,status:"已报价",responseTime:"2024-03-18",deliveryDays:30,paymentTerms:"款到发货",validityPeriod:"30天"},{id:"INQ002",inquiryNo:"RFQ-2024-001",quantity:2,date:"2024-03-15",supplier:"欧姆龙自动化（中国）有限公司",unitPrice:1350,totalPrice:2700,status:"已报价",responseTime:"2024-03-17",deliveryDays:25,paymentTerms:"30%预付，发货前付清",validityPeriod:"45天"},{id:"INQ003",inquiryNo:"RFQ-2024-002",quantity:1,date:"2024-04-20",supplier:"上海电气有限公司",unitPrice:1260,totalPrice:1260,status:"已报价",responseTime:"2024-04-23",deliveryDays:30,paymentTerms:"款到发货",validityPeriod:"30天"},{id:"INQ004",inquiryNo:"RFQ-2024-002",quantity:1,date:"2024-04-20",supplier:"深圳市创新科技有限公司",unitPrice:1180,totalPrice:1180,status:"已报价",responseTime:"2024-04-22",deliveryDays:35,paymentTerms:"款到发货",validityPeriod:"60天"},{id:"INQ005",inquiryNo:"RFQ-2024-003",quantity:3,date:"2024-05-10",supplier:"上海电气有限公司",unitPrice:1250,totalPrice:3750,status:"询价中",responseTime:"-",deliveryDays:null,paymentTerms:"-",validityPeriod:"-"}]),g=q([{id:"PO001",purchaseNo:"PO-2024-0125",quantity:2,date:"2024-03-25",supplier:"上海电气有限公司",unitPrice:1280,totalPrice:2560,status:"已完成",receiptDate:"2024-04-22",paymentStatus:"已付款",paymentDate:"2024-03-25",warranty:"12个月",qualityCheck:"合格"},{id:"PO002",purchaseNo:"PO-2024-0252",quantity:1,date:"2024-04-30",supplier:"深圳市创新科技有限公司",unitPrice:1180,totalPrice:1180,status:"已完成",receiptDate:"2024-05-25",paymentStatus:"已付款",paymentDate:"2024-04-30",warranty:"12个月",qualityCheck:"合格"},{id:"PO003",purchaseNo:"PO-2024-0389",quantity:3,date:"2024-05-15",supplier:"上海电气有限公司",unitPrice:1250,totalPrice:3750,status:"进行中",receiptDate:"预计2024-06-15",paymentStatus:"预付款已付",paymentDate:"2024-05-15",warranty:"12个月",qualityCheck:"待检"},{id:"PO004",purchaseNo:"PO-2024-0423",quantity:5,date:"2024-05-28",supplier:"欧姆龙自动化（中国）有限公司",unitPrice:1300,totalPrice:6500,status:"待发货",receiptDate:"预计2024-06-25",paymentStatus:"待付款",paymentDate:"-",warranty:"12个月",qualityCheck:"待检"}]),w=q([{id:"1",name:"PLC控制器",model:"CP1E-N40DR-A",brand:"欧姆龙",category:"电子元器件",quantity:2,deliveryDays:30,remark:""},{id:"2",name:"触摸屏",model:"NB10W-TW01B",brand:"欧姆龙",category:"电子元器件",quantity:1,deliveryDays:25,remark:""},{id:"3",name:"钣金外壳",model:"CNC-SHT-001",brand:"定制",category:"机械部件",quantity:1,deliveryDays:45,remark:""}]),h=q([{id:"BOM-001",name:"主控制系统",standardCount:30,customCount:10,assemblyCount:5,totalCount:45,matchRate:98,matchIssueCount:0,updateTime:"2024-05-12",version:"V1.0",isActiveVersion:!0,activeVersion:"V1.0",materials:[{id:"1",name:"PLC控制器",model:"CP1E-N40DR-A",brand:"欧姆龙",category:"电子元器件",quantity:2,remark:"标准型号，带RS232通信",inquiryNo:"RFQ-2024-001",specs:"标准PLC控制器，40点I/O，继电器输出"},{id:"2",name:"交流伺服电机",model:"R88M-K2K030H",brand:"欧姆龙",category:"电气设备",quantity:4,remark:"3000rpm，200V",inquiryNo:"无",specs:"交流伺服电机，2kW功率，3000rpm"},{id:"3",name:"触摸屏",model:"NB10W-TW01B",brand:"欧姆龙",category:"电子元器件",quantity:1,remark:"10英寸宽屏",inquiryNo:"RFQ-2024-001",specs:"10英寸宽屏TFT触摸屏，800x480分辨率"}]},{id:"BOM-002",name:"传动系统",standardCount:18,customCount:8,assemblyCount:2,totalCount:28,matchRate:85,matchIssueCount:4,updateTime:"2024-05-15",version:"V1.2",isActiveVersion:!1,activeVersion:"V1.5",materials:[{id:"4",name:"伺服驱动器",model:"MR-J4-200A",brand:"三菱",category:"电气设备",quantity:2,remark:"200V，2kW",inquiryNo:"无",specs:"三菱伺服驱动器，2kW功率，SSCNET III/H通信"},{id:"5",name:"齿轮箱",model:"GR63SMT16",brand:"SEW",category:"机械部件",quantity:2,remark:"高精度，减速比1:16",inquiryNo:"无",specs:"高精度行星齿轮箱，减速比1:16，最大扭矩63Nm"}]},{id:"BOM-003",name:"机床外壳",standardCount:5,customCount:7,assemblyCount:0,totalCount:12,matchRate:100,matchIssueCount:0,updateTime:"2024-05-18",version:"V2.0",isActiveVersion:!0,activeVersion:"V2.0",materials:[{id:"6",name:"钣金外壳",model:"CNC-SHT-001",brand:"定制",category:"机械部件",quantity:1,remark:"304不锈钢，定制加工",inquiryNo:"RFQ-2024-003",specs:"304不锈钢，2mm厚度，CNC加工，喷砂处理"},{id:"7",name:"观察窗",model:"GL-500x300",brand:"安全玻璃",category:"结构件",quantity:2,remark:"钢化玻璃，500x300mm",inquiryNo:"RFQ-2024-003",specs:"10mm厚度钢化玻璃，耐冲击，500x300mm"}]}]),M=[{title:"BOM名称/编号",key:"name",dataIndex:"name"},{title:"版本",key:"version",dataIndex:"version"},{title:"更新时间",key:"updateTime",dataIndex:"updateTime"},{title:"操作",key:"operation"}],K=[{title:"产品名称",dataIndex:"name",key:"name"},{title:"型号",dataIndex:"model",key:"model"},{title:"品牌",dataIndex:"brand",key:"brand"},{title:"产品分类",dataIndex:"category",key:"category"},{title:"数量",dataIndex:"quantity",key:"quantity"},{title:"期望交期(天)",key:"deliveryDays",dataIndex:"deliveryDays"},{title:"备注",key:"remark",dataIndex:"remark"},{title:"操作",key:"operation"}],S=[{title:"",dataIndex:"checkbox",key:"checkbox",width:50,customRender:()=>V("a-checkbox")},{title:"产品名称",dataIndex:"name",key:"name"},{title:"型号",dataIndex:"model",key:"model"},{title:"品牌",dataIndex:"brand",key:"brand"},{title:"产品分类",dataIndex:"category",key:"category"},{title:"数量",dataIndex:"quantity",key:"quantity"},{title:"备注",dataIndex:"remark",key:"remark"},{title:"操作",key:"materialOperation"}],Q={expandIcon:({expanded:m,record:i,onExpand:C})=>V("div",{class:"expand-icon-container",onClick:j=>{C(i,j)}},[V("i",{class:m?"fas fa-chevron-down":"fas fa-chevron-right"})]),expandRowByClick:!0,indentSize:0},I=m=>({class:"clickable-row",onClick:()=>{p(m)}}),f=(m,i)=>{m?_.value=[..._.value,i.id]:_.value=_.value.filter(C=>C!==i.id)},p=m=>{const i=m.id;_.value.includes(i)?_.value=_.value.filter(j=>j!==i):_.value=[..._.value,i]},U=m=>{G.confirm({title:"确认移除",content:`确定要移除 ${m.name} (${m.id}) 吗？`,okText:"确认",cancelText:"取消",onOk:()=>{const i=h.value.findIndex(C=>C.id===m.id);i!==-1&&(h.value.splice(i,1),B.success(`已成功移除 ${m.name}`))}})},Z=()=>{B.info("智能询价单创建功能将在后续版本开发")},ee=()=>{y.value=!0},te=()=>{if(!k.name){B.error("请输入询价单名称");return}if(!k.deadline){B.error("请选择询价截止日期");return}B.success("询价单创建成功"),y.value=!1,Object.assign(k,{name:"",deadline:null,description:""})},ae=()=>{y.value=!1},ie=m=>{const i=w.value.findIndex(C=>C.id===m.id);i!==-1&&w.value.splice(i,1)},ne=m=>{G.confirm({title:"确认同步",content:`确定要将 ${m.name} (${m.id}) 同步到最新版本吗？`,okText:"确认",cancelText:"取消",onOk:()=>{B.success(`已成功将 ${m.name} 同步到最新版本`)}})},se=m=>{O.value={...m,specs:"标准PLC控制器，40点I/O，继电器输出"},d.value=!0},le=({material:m,record:i})=>{d.value=!1,y.value=!0,k.name=`${m.name} 询价单`;const C=w.value.findIndex(j=>j.id===m.id&&j.model===m.model);C===-1?w.value.push({id:m.id,name:m.name,model:m.model,brand:m.brand,category:m.category,quantity:i.quantity,deliveryDays:i.deliveryDays||30,remark:`优先询价供应商: ${i.supplier}`}):(w.value[C].quantity=i.quantity,w.value[C].deliveryDays=i.deliveryDays||30,w.value[C].remark=`优先询价供应商: ${i.supplier}`),B.success(`已创建询价单，询价物料：${m.name}，供应商：${i.supplier}`)},oe=({material:m,record:i})=>{B.success(`已创建采购单，采购物料：${m.name}，供应商：${i.supplier}，数量：${i.quantity}，单价：¥${i.unitPrice.toFixed(2)}`),d.value=!1};return(m,i)=>{const C=o("a-button"),j=o("a-menu-item"),de=o("a-menu"),re=o("a-dropdown"),z=o("a-input"),$=o("a-select-option"),W=o("a-select"),ue=o("a-tooltip"),E=o("a-table"),L=o("a-form-item"),J=o("a-col"),ce=o("a-date-picker"),ye=o("a-row"),pe=o("a-textarea"),me=o("a-input-number"),fe=o("a-modal");return r(),b("div",null,[a("div",Ie,[a("div",qe,[t(C,{type:"primary"},{icon:e(()=>i[9]||(i[9]=[a("i",{class:"fas fa-plus"},null,-1)])),default:e(()=>[i[10]||(i[10]=s(" 添加BOM "))]),_:1}),t(re,null,{overlay:e(()=>[t(de,null,{default:e(()=>[t(j,{key:"1",onClick:Z},{default:e(()=>i[14]||(i[14]=[a("i",{class:"fas fa-robot"},null,-1),s(" 智能创建 ")])),_:1}),t(j,{key:"2",onClick:ee},{default:e(()=>i[15]||(i[15]=[a("i",{class:"fas fa-edit"},null,-1),s(" 手动创建 ")])),_:1})]),_:1})]),default:e(()=>[t(C,{type:"primary"},{icon:e(()=>i[11]||(i[11]=[a("i",{class:"fas fa-file-invoice"},null,-1)])),default:e(()=>[i[12]||(i[12]=s(" 创建询价单 ")),i[13]||(i[13]=a("i",{class:"fas fa-caret-down",style:{"margin-left":"5px"}},null,-1))]),_:1})]),_:1})]),a("div",Te,[t(z,{placeholder:"搜索BOM...",style:{width:"200px"},value:n.value,"onUpdate:value":i[0]||(i[0]=v=>n.value=v),"allow-clear":""},{prefix:e(()=>i[16]||(i[16]=[a("i",{class:"fas fa-search"},null,-1)])),_:1},8,["value"]),t(W,{placeholder:"全部品牌",style:{width:"120px"},value:D.value,"onUpdate:value":i[1]||(i[1]=v=>D.value=v),"allow-clear":""},{default:e(()=>[t($,{value:""},{default:e(()=>i[17]||(i[17]=[s("全部品牌")])),_:1}),t($,{value:"欧姆龙"},{default:e(()=>i[18]||(i[18]=[s("欧姆龙")])),_:1}),t($,{value:"三菱"},{default:e(()=>i[19]||(i[19]=[s("三菱")])),_:1}),t($,{value:"SEW"},{default:e(()=>i[20]||(i[20]=[s("SEW")])),_:1}),t($,{value:"定制"},{default:e(()=>i[21]||(i[21]=[s("定制")])),_:1})]),_:1},8,["value"]),t(W,{placeholder:"全部分类",style:{width:"120px"},value:N.value,"onUpdate:value":i[2]||(i[2]=v=>N.value=v),"allow-clear":""},{default:e(()=>[t($,{value:""},{default:e(()=>i[22]||(i[22]=[s("全部分类")])),_:1}),t($,{value:"电子元器件"},{default:e(()=>i[23]||(i[23]=[s("电子元器件")])),_:1}),t($,{value:"电气设备"},{default:e(()=>i[24]||(i[24]=[s("电气设备")])),_:1}),t($,{value:"机械部件"},{default:e(()=>i[25]||(i[25]=[s("机械部件")])),_:1}),t($,{value:"结构件"},{default:e(()=>i[26]||(i[26]=[s("结构件")])),_:1})]),_:1},8,["value"]),t(W,{placeholder:"全部询价单",style:{width:"150px"},value:u.value,"onUpdate:value":i[3]||(i[3]=v=>u.value=v),"allow-clear":""},{default:e(()=>[t($,{value:""},{default:e(()=>i[27]||(i[27]=[s("全部询价单")])),_:1}),t($,{value:"RFQ-2024-001"},{default:e(()=>i[28]||(i[28]=[s("RFQ-2024-001")])),_:1}),t($,{value:"RFQ-2024-002"},{default:e(()=>i[29]||(i[29]=[s("RFQ-2024-002")])),_:1}),t($,{value:"RFQ-2024-003"},{default:e(()=>i[30]||(i[30]=[s("RFQ-2024-003")])),_:1})]),_:1},8,["value"])])]),t(E,{dataSource:h.value,columns:M,pagination:!1,"row-key":v=>v.id,expandable:Q,"expanded-row-keys":_.value,onExpand:f,customRow:I},{bodyCell:e(({column:v,text:F,record:T})=>[v.key==="name"?(r(),b(H,{key:0},[a("div",Re,c(T.name),1),a("div",$e,c(T.id),1)],64)):v.key==="version"?(r(),b("div",Se,[s(c(T.version)+" ",1),T.isActiveVersion?P("",!0):(r(),R(ue,{key:0,title:`当前活跃版本: ${T.activeVersion}`},{default:e(()=>i[31]||(i[31]=[a("i",{class:"fas fa-info-circle",style:{color:"#faad14","margin-left":"5px"}},null,-1)])),_:2},1032,["title"]))])):v.key==="updateTime"?(r(),b("div",De,c(T.updateTime),1)):v.key==="operation"?(r(),b(H,{key:3},[a("a",{href:"#",style:{color:"#f94c30","margin-right":"8px"},onClick:Y(X=>U(T),["prevent"])},"移除",8,Me),T.isActiveVersion?P("",!0):(r(),R(C,{key:0,type:"link",size:"small",onClick:X=>ne(T)},{default:e(()=>i[32]||(i[32]=[a("i",{class:"fas fa-sync-alt"},null,-1),s(" 同步物料 ")])),_:2},1032,["onClick"]))],64)):P("",!0)]),expandedRowRender:e(({record:v})=>[t(E,{columns:S,dataSource:v.materials,pagination:!1,size:"small","row-key":F=>F.id},{bodyCell:e(({column:F,record:T})=>[F.key==="materialOperation"?(r(),R(C,{key:0,type:"link",size:"small",onClick:X=>se(T)},{default:e(()=>i[33]||(i[33]=[a("i",{class:"fas fa-history"},null,-1),s(" 交易历史 ")])),_:2},1032,["onClick"])):P("",!0)]),_:2},1032,["dataSource","row-key"])]),_:1},8,["dataSource","row-key","expanded-row-keys"]),t(fe,{visible:y.value,"onUpdate:visible":i[7]||(i[7]=v=>y.value=v),title:"创建询价单",width:"1000px",onOk:te,onCancel:ae},{default:e(()=>[a("div",Qe,[t(ye,{gutter:15},{default:e(()=>[t(J,{span:12},{default:e(()=>[t(L,{label:"询价单名称"},{default:e(()=>[t(z,{value:k.name,"onUpdate:value":i[4]||(i[4]=v=>k.name=v),placeholder:"请输入询价单名称"},null,8,["value"])]),_:1})]),_:1}),t(J,{span:12},{default:e(()=>[t(L,{label:"询价截止日期"},{default:e(()=>[t(ce,{value:k.deadline,"onUpdate:value":i[5]||(i[5]=v=>k.deadline=v),style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(L,{label:"询价说明"},{default:e(()=>[t(pe,{value:k.description,"onUpdate:value":i[6]||(i[6]=v=>k.description=v),placeholder:"请输入询价说明...",rows:4},null,8,["value"])]),_:1})]),a("div",null,[a("div",Ne,[a("h4",Oe,"已选物料 ("+c(w.value.length)+")",1)]),t(E,{dataSource:w.value,columns:K,size:"small",pagination:!1,"row-key":v=>v.id},{bodyCell:e(({column:v,record:F})=>[v.key==="deliveryDays"?(r(),R(me,{key:0,value:F.deliveryDays,"onUpdate:value":T=>F.deliveryDays=T,min:1,max:365},null,8,["value","onUpdate:value"])):v.key==="remark"?(r(),R(z,{key:1,value:F.remark,"onUpdate:value":T=>F.remark=T},null,8,["value","onUpdate:value"])):v.key==="operation"?(r(),b("a",{key:2,href:"#",style:{color:"#f94c30"},onClick:Y(T=>ie(F),["prevent"])},"移除",8,Fe)):P("",!0)]),_:1},8,["dataSource","row-key"])])]),_:1},8,["visible"]),t(we,{visible:d.value,"onUpdate:visible":i[8]||(i[8]=v=>d.value=v),material:O.value,"inquiry-history-data":x.value,"purchase-history-data":g.value,onReinquiry:le,onRepurchase:oe},null,8,["visible","material","inquiry-history-data","purchase-history-data"])])}}},je=A(Ke,[["__scopeId","data-v-990c2e98"]]),Ve={name:"RfqManagement",data(){return{selectedRowKeys:[],selectedQuoteKeys:[],quoteDrawerVisible:!1,drawerTitle:"物料报价详情",selectedMaterial:{},riskWarning:"风险检查：传动系统存在独家供应风险，请重点关注",rfqColumns:[{title:"询价单号",dataIndex:"id",key:"id"},{title:"商品数",dataIndex:"itemCount",key:"itemCount"},{title:"状态",dataIndex:"status",key:"status"},{title:"截止日期",dataIndex:"deadline",key:"deadline"},{title:"操作",key:"action"}],materialColumns:[{title:"产品名称",dataIndex:"name",key:"name"},{title:"型号",dataIndex:"model",key:"model"},{title:"品牌",dataIndex:"brand",key:"brand"},{title:"产品分类",dataIndex:"category",key:"category"},{title:"数量",dataIndex:"quantity",key:"quantity"},{title:"期望交期(天)",dataIndex:"leadTime",key:"leadTime"},{title:"供应商数量",dataIndex:"supplierCount",key:"supplierCount"},{title:"备注",dataIndex:"note",key:"note"},{title:"操作",key:"action"}],quoteColumns:[{title:"供应商名称",dataIndex:"supplier",key:"supplier"},{title:"评分",dataIndex:"rating",key:"rating"},{title:"单价(元)",dataIndex:"unitPrice",key:"unitPrice"},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice"},{title:"交期(天)",dataIndex:"leadTime",key:"leadTime"},{title:"供应商备注",dataIndex:"note",key:"note"},{title:"操作",key:"action"}],quoteColumnsInDrawer:[{title:"供应商名称",dataIndex:"supplier",key:"supplier"},{title:"评分",dataIndex:"rating",key:"rating"},{title:"单价(元)",dataIndex:"unitPrice",key:"unitPrice"},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice"},{title:"交期(天)",dataIndex:"leadTime",key:"leadTime"},{title:"供应商备注",dataIndex:"note",key:"note"},{title:"操作",key:"action"}],rfqData:[{id:"RFQ-2024-001",itemCount:5,status:"已报价",deadline:"2024-05-25",materials:[{id:"MAT-001",name:"伺服电机",model:"SM-2000",brand:"西门子",category:"驱动系统",quantity:10,leadTime:30,supplierCount:2,note:"高精度",quotes:[{id:"Q-001-1",supplier:"博世自动化",rating:4.8,unitPrice:4500,totalPrice:45e3,leadTime:30,note:"现货可发"},{id:"Q-001-2",supplier:"安川电机",rating:4.5,unitPrice:4200,totalPrice:42e3,leadTime:35,note:"送货上门"}]},{id:"MAT-002",name:"控制器",model:"PLC-5000",brand:"三菱",category:"控制系统",quantity:5,leadTime:45,supplierCount:3,note:"带显示屏",quotes:[{id:"Q-002-1",supplier:"三菱电机",rating:4.7,unitPrice:8500,totalPrice:42500,leadTime:40,note:"含安装调试"},{id:"Q-002-2",supplier:"ABB",rating:4.6,unitPrice:8900,totalPrice:44500,leadTime:35,note:"2年质保"},{id:"Q-002-3",supplier:"施耐德",rating:4.4,unitPrice:7800,totalPrice:39e3,leadTime:45,note:"可定制化"}]}]},{id:"RFQ-2024-002",itemCount:3,status:"部分报价",deadline:"2024-05-28",materials:[]},{id:"RFQ-2024-003",itemCount:2,status:"待报价",deadline:"2024-06-05",materials:[]}]}},computed:{hasSelected(){return this.selectedRowKeys.length>0}},methods:{onSelectChange(l){this.selectedRowKeys=l},onQuoteSelectChange(l){this.selectedQuoteKeys=l},getStatusColor(l){return{已报价:"purple",部分报价:"orange",待报价:"blue",已转采购:"green"}[l]||"blue"},toggleQuotes(l){this.selectedMaterial=l,this.drawerTitle=`${l.name} 报价详情`,this.quoteDrawerVisible=!0},closeQuoteDrawer(){this.quoteDrawerVisible=!1,this.selectedQuoteKeys=[]},viewRfq(l){console.log("查看询价单",l)},editRfq(l){console.log("编辑询价单",l)},deleteRfq(l){this.$confirm({title:"确认删除",content:`确定要删除询价单 ${l.id} 吗？`,okText:"确认",cancelText:"取消",onOk:()=>{console.log("删除询价单",l),this.$message.success(`询价单 ${l.id} 已删除`)}})},assignSupplier(l){console.log("分配供应商",l),this.$message.info("打开供应商分配界面")},removeMaterial(l){console.log("移除物料",l)},selectSupplier(l,n){console.log("选择供应商",l,n),this.$message.success(`已选择 ${l.supplier} 作为 ${n.name} 的供应商`)},showSupplierDetail(l){console.log("查看供应商详情",l)},selectBestQuote(){if(this.selectedQuoteKeys.length>0){const l=this.selectedMaterial.quotes.find(n=>n.id===this.selectedQuoteKeys[0]);l&&(this.$message.success(`已选择 ${l.supplier} 作为 ${this.selectedMaterial.name} 的供应商`),this.closeQuoteDrawer())}else this.$message.warning("请先选择一个报价")},generatePurchaseOrder(){console.log("生成采购订单")},handleSmartAssign({key:l}){const n={score:"评分优先",price:"价格优先",delivery:"交期优先"};this.$message.info(`使用${n[l]}策略进行智能分配`)},batchCompare(){console.log("批量比价",this.selectedRowKeys)},batchCreateOrder(){console.log("批量生成订单",this.selectedRowKeys)},exportRfq(){console.log("导出询价单",this.selectedRowKeys)}}},Be={class:"rfq-management"},Ue={class:"action-bar"},He={class:"filter-row"},Ae={class:"button-group"},ze={class:"search-filters"},We={key:0,style:{color:"#e60000","font-weight":"bold"}},Ee=["onClick"],Le=["onClick"],Ge=["onClick"],Je=["onClick"],Xe=["onClick"],Ye=["onClick"],Ze=["onClick"],et=["onClick"],tt={key:0,class:"risk-warning"},at={class:"batch-actions"},it={class:"material-info-card"},nt={class:"info-grid"},st={class:"info-item"},lt={class:"info-value"},ot={class:"info-item"},dt={class:"info-value"},rt={class:"info-item"},ut={class:"info-value"},ct={class:"info-item"},yt={class:"info-value"},pt={class:"info-item"},mt={class:"info-value"},ft={class:"info-item"},vt={class:"info-value"},kt={key:0,style:{color:"#e60000","font-weight":"bold"}},_t=["onClick"];function bt(l,n,D,N,u,y){const _=o("a-button"),d=o("a-menu-item"),O=o("a-menu"),k=o("a-dropdown"),x=o("a-input"),g=o("a-select-option"),w=o("a-select"),h=o("a-divider"),M=o("a-table"),K=o("a-tag"),S=o("a-drawer");return r(),b("div",Be,[a("div",Ue,[a("div",He,[a("div",Ae,[t(_,{type:"primary",onClick:y.generatePurchaseOrder},{icon:e(()=>n[0]||(n[0]=[a("i",{class:"fas fa-plus"},null,-1)])),default:e(()=>[n[1]||(n[1]=s(" 生成采购订单 "))]),_:1},8,["onClick"]),t(k,null,{overlay:e(()=>[t(O,{onClick:y.handleSmartAssign},{default:e(()=>[t(d,{key:"score"},{default:e(()=>n[2]||(n[2]=[s("评分优先")])),_:1}),t(d,{key:"price"},{default:e(()=>n[3]||(n[3]=[s("价格优先")])),_:1}),t(d,{key:"delivery"},{default:e(()=>n[4]||(n[4]=[s("交期优先")])),_:1})]),_:1},8,["onClick"])]),default:e(()=>[t(_,{type:"primary"},{icon:e(()=>n[5]||(n[5]=[a("i",{class:"fas fa-robot"},null,-1)])),default:e(()=>[n[6]||(n[6]=s(" 智能分配 ")),n[7]||(n[7]=a("i",{class:"fas fa-caret-down"},null,-1))]),_:1})]),_:1})]),a("div",ze,[t(x,{placeholder:"搜索询价单...",style:{width:"200px"}}),t(w,{style:{width:"150px"},placeholder:"全部状态",allowClear:""},{default:e(()=>[t(g,{value:"pending"},{default:e(()=>n[8]||(n[8]=[s("待发送")])),_:1}),t(g,{value:"sent"},{default:e(()=>n[9]||(n[9]=[s("已发送")])),_:1}),t(g,{value:"quoted"},{default:e(()=>n[10]||(n[10]=[s("已报价")])),_:1}),t(g,{value:"purchased"},{default:e(()=>n[11]||(n[11]=[s("已转采购")])),_:1})]),_:1})])])]),t(M,{columns:u.rfqColumns,"data-source":u.rfqData,rowKey:"id","row-selection":{selectedRowKeys:u.selectedRowKeys,onChange:y.onSelectChange},pagination:!1},{expandedRowRender:e(({record:Q})=>[t(M,{columns:u.materialColumns,"data-source":Q.materials,rowKey:"id",pagination:!1,class:"nested-table"},{expandedRowRender:e(({record:I})=>[t(M,{columns:u.quoteColumns,"data-source":I.quotes,rowKey:"id",pagination:!1,class:"nested-quotes-table"},{bodyCell:e(({column:f,record:p})=>[f.key==="rating"?(r(),b("span",We,c(p.rating),1)):f.key==="action"?(r(),b(H,{key:1},[a("a",{onClick:U=>y.selectSupplier(p,I)},"选择",8,Ee),t(h,{type:"vertical"}),a("a",{onClick:U=>y.showSupplierDetail(p)},"详情",8,Le)],64)):P("",!0)]),_:2},1032,["columns","data-source"])]),bodyCell:e(({column:I,record:f})=>[I.key==="action"?(r(),b(H,{key:0},[a("a",{onClick:p=>y.toggleQuotes(f)},"查看报价",8,Ge),t(h,{type:"vertical"}),a("a",{onClick:p=>y.assignSupplier(f)},"分配供应商",8,Je),t(h,{type:"vertical"}),a("a",{onClick:p=>y.removeMaterial(f)},"移除",8,Xe)],64)):P("",!0)]),_:2},1032,["columns","data-source"])]),bodyCell:e(({column:Q,record:I})=>[Q.key==="status"?(r(),R(K,{key:0,color:y.getStatusColor(I.status)},{default:e(()=>[s(c(I.status),1)]),_:2},1032,["color"])):Q.key==="action"?(r(),b(H,{key:1},[a("a",{onClick:f=>y.viewRfq(I)},"查看",8,Ye),t(h,{type:"vertical"}),a("a",{onClick:f=>y.editRfq(I)},"编辑",8,Ze),t(h,{type:"vertical"}),a("a",{onClick:f=>y.deleteRfq(I)},"删除",8,et)],64)):P("",!0)]),_:1},8,["columns","data-source","row-selection"]),u.riskWarning?(r(),b("div",tt,[n[12]||(n[12]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),s(" "+c(u.riskWarning),1)])):P("",!0),a("div",at,[t(_,{type:"primary",onClick:y.batchCompare,disabled:!y.hasSelected},{default:e(()=>n[13]||(n[13]=[s("批量比价")])),_:1},8,["onClick","disabled"]),t(_,{type:"primary",onClick:y.batchCreateOrder,disabled:!y.hasSelected},{default:e(()=>n[14]||(n[14]=[s("批量生成订单")])),_:1},8,["onClick","disabled"]),t(_,{onClick:y.exportRfq,disabled:!y.hasSelected},{default:e(()=>n[15]||(n[15]=[s("导出询价单")])),_:1},8,["onClick","disabled"])]),t(S,{visible:u.quoteDrawerVisible,title:u.drawerTitle,placement:"right",width:"50%",onClose:y.closeQuoteDrawer},{footer:e(()=>[t(_,{type:"primary",onClick:y.selectBestQuote},{default:e(()=>n[23]||(n[23]=[s("选择最优报价")])),_:1},8,["onClick"]),t(_,{style:{"margin-left":"8px"},onClick:y.closeQuoteDrawer},{default:e(()=>n[24]||(n[24]=[s("关闭")])),_:1},8,["onClick"])]),default:e(()=>[a("div",it,[n[22]||(n[22]=a("h4",null,"物料基本信息",-1)),a("div",nt,[a("div",st,[n[16]||(n[16]=a("div",{class:"info-label"},"产品名称",-1)),a("div",lt,c(u.selectedMaterial.name||"-"),1)]),a("div",ot,[n[17]||(n[17]=a("div",{class:"info-label"},"型号",-1)),a("div",dt,c(u.selectedMaterial.model||"-"),1)]),a("div",rt,[n[18]||(n[18]=a("div",{class:"info-label"},"品牌",-1)),a("div",ut,c(u.selectedMaterial.brand||"-"),1)]),a("div",ct,[n[19]||(n[19]=a("div",{class:"info-label"},"产品分类",-1)),a("div",yt,c(u.selectedMaterial.category||"-"),1)]),a("div",pt,[n[20]||(n[20]=a("div",{class:"info-label"},"数量",-1)),a("div",mt,c(u.selectedMaterial.quantity||"-"),1)]),a("div",ft,[n[21]||(n[21]=a("div",{class:"info-label"},"期望交期(天)",-1)),a("div",vt,c(u.selectedMaterial.leadTime||"-"),1)])])]),t(M,{columns:u.quoteColumnsInDrawer,"data-source":u.selectedMaterial.quotes||[],rowKey:"id","row-selection":{type:"radio",selectedRowKeys:u.selectedQuoteKeys,onChange:y.onQuoteSelectChange}},{bodyCell:e(({column:Q,record:I})=>[Q.key==="rating"?(r(),b("span",kt,c(I.rating),1)):Q.key==="action"?(r(),b("a",{key:1,onClick:f=>y.showSupplierDetail(I)},"详情",8,_t)):P("",!0)]),_:1},8,["columns","data-source","row-selection"])]),_:1},8,["visible","title","onClose"])])}const gt=A(Ve,[["render",bt],["__scopeId","data-v-05ccca5b"]]),xt={class:"purchase-order-management"},ht={class:"action-bar"},Ct={class:"filter-row"},Pt={class:"button-group"},wt={key:0,href:"#",style:{color:"#f94c30"}},It={key:1,href:"#",style:{color:"#f94c30"}},qt={key:2,href:"#",style:{color:"#f94c30"}},Tt={key:3,href:"#",style:{color:"#f94c30"}},Rt={class:"button-group-bottom"},$t={__name:"po",setup(l){const n=q([{title:"",dataIndex:"checkbox",width:40,customRender:()=>V("a-checkbox")},{title:"采购单号",dataIndex:"poNumber",key:"poNumber"},{title:"供应商",dataIndex:"supplier",key:"supplier"},{title:"金额",dataIndex:"amount",key:"amount"},{title:"状态",dataIndex:"status",key:"status"},{title:"操作",key:"action"}]),D=q([{title:"产品名称",dataIndex:"name"},{title:"型号",dataIndex:"model"},{title:"品牌",dataIndex:"brand"},{title:"产品分类",dataIndex:"category"},{title:"数量",dataIndex:"quantity"},{title:"单价",dataIndex:"unitPrice"},{title:"总价",dataIndex:"totalPrice"},{title:"备注",dataIndex:"note"},{title:"操作",key:"action",customRender:()=>V("a-space",{},[V("a",{href:"#",style:"color: #f94c30"},"编辑"),V("a",{href:"#",style:"color: #f94c30"},"删除")])}]),N=q([{id:1,poNumber:"PO-2024-001",supplier:"上海精密机械有限公司",amount:"¥320,000",status:"待收货",items:[{id:1,name:"数控系统",model:"SINUMERIK 840D",brand:"西门子",category:"自动化控制系统",quantity:1,unitPrice:"¥180,000",totalPrice:"¥180,000",note:"进口设备"},{id:2,name:"伺服电机",model:"1FK7080",brand:"西门子",category:"驱动设备",quantity:4,unitPrice:"¥35,000",totalPrice:"¥140,000",note:"标准配置"}]},{id:2,poNumber:"PO-2024-002",supplier:"德国西门子自动化（中国）有限公司",amount:"¥580,000",status:"待发货",items:[{id:1,name:"PLC控制器",model:"S7-1500",brand:"西门子",category:"自动化控制系统",quantity:2,unitPrice:"¥85,000",totalPrice:"¥170,000",note:"高性能型"},{id:2,name:"触摸屏",model:"TP1500",brand:"西门子",category:"人机界面",quantity:3,unitPrice:"¥45,000",totalPrice:"¥135,000",note:"15英寸彩色"},{id:3,name:"通讯模块",model:"CP1543-1",brand:"西门子",category:"通信设备",quantity:5,unitPrice:"¥55,000",totalPrice:"¥275,000",note:"工业以太网"}]},{id:3,poNumber:"PO-2024-003",supplier:"广州工业配件有限公司",amount:"¥80,000",status:"待支付",items:[{id:1,name:"机床防护罩",model:"MG-2000",brand:"国产",category:"机床配件",quantity:2,unitPrice:"¥15,000",totalPrice:"¥30,000",note:"定制尺寸"},{id:2,name:"冷却液系统",model:"CL-500",brand:"国产",category:"辅助系统",quantity:1,unitPrice:"¥25,000",totalPrice:"¥25,000",note:"5L容量"},{id:3,name:"电柜配件",model:"EC-series",brand:"国产",category:"电气配件",quantity:1,unitPrice:"¥25,000",totalPrice:"¥25,000",note:"含开关、继电器等"}]}]),u=ke(()=>({expandedRowRender:_=>V("div",{class:"nested-table-container"},[V("a-table",{dataSource:_.items,columns:D.value,pagination:!1,rowKey:"id",class:"nested-table"})])})),y=_=>({待支付:"orange",待发货:"blue",部分发货:"purple",待收货:"green",已完成:"success",已取消:"default"})[_]||"blue";return(_,d)=>{const O=o("a-input"),k=o("a-select-option"),x=o("a-select"),g=o("a-button"),w=o("a-tag"),h=o("a-space"),M=o("a-table");return r(),b("div",xt,[a("div",ht,[a("div",Ct,[t(O,{placeholder:"搜索采购单...",style:{width:"200px","margin-right":"10px"}}),t(x,{placeholder:"全部状态",style:{width:"150px"}},{default:e(()=>[t(k,{value:""},{default:e(()=>d[0]||(d[0]=[s("全部状态")])),_:1}),t(k,{value:"pending_payment"},{default:e(()=>d[1]||(d[1]=[s("待支付")])),_:1}),t(k,{value:"pending_shipment"},{default:e(()=>d[2]||(d[2]=[s("待发货")])),_:1}),t(k,{value:"partial_shipment"},{default:e(()=>d[3]||(d[3]=[s("部分发货")])),_:1}),t(k,{value:"pending_receipt"},{default:e(()=>d[4]||(d[4]=[s("待收货")])),_:1}),t(k,{value:"completed"},{default:e(()=>d[5]||(d[5]=[s("已完成")])),_:1}),t(k,{value:"cancelled"},{default:e(()=>d[6]||(d[6]=[s("已取消")])),_:1})]),_:1})]),a("div",Pt,[t(g,{type:"primary"},{default:e(()=>d[7]||(d[7]=[a("i",{class:"fas fa-plus"},null,-1),s(" 创建采购单")])),_:1})])]),t(M,{dataSource:N.value,columns:n.value,rowKey:"id",expandable:u.value,class:"po-table"},{bodyCell:e(({column:K,record:S})=>[K.key==="status"?(r(),R(w,{key:0,color:y(S.status)},{default:e(()=>[s(c(S.status),1)]),_:2},1032,["color"])):P("",!0),K.key==="action"?(r(),R(h,{key:1},{default:e(()=>[d[8]||(d[8]=a("a",{href:"#",style:{color:"#f94c30"}},"查看",-1)),d[9]||(d[9]=a("a",{href:"#",style:{color:"#f94c30"}},"跟踪",-1)),S.status==="待收货"?(r(),b("a",wt,"收货")):P("",!0),S.status==="待发货"?(r(),b("a",It,"取消")):P("",!0),S.status==="待支付"?(r(),b("a",qt,"审批")):P("",!0),S.status==="待支付"?(r(),b("a",Tt,"编辑")):P("",!0)]),_:2},1024)):P("",!0)]),_:1},8,["dataSource","columns","expandable"]),d[13]||(d[13]=a("div",{class:"risk-warning"},[a("i",{class:"fas fa-exclamation-triangle"}),s(" 风险检查：PO-2024-001预付款比例超过50%，请关注风险控制 ")],-1)),a("div",Rt,[t(g,{type:"primary"},{default:e(()=>d[10]||(d[10]=[s("批量跟踪")])),_:1}),t(g,null,{default:e(()=>d[11]||(d[11]=[s("导出采购单")])),_:1}),t(g,null,{default:e(()=>d[12]||(d[12]=[s("生成报表")])),_:1})])])}}},St=A($t,[["__scopeId","data-v-ef08c436"]]),Dt={name:"ProjectDetail",components:{BomManagement:je,RfqManagement:gt,PoManagement:St},data(){return{activeTabKey:"bom",projectInfo:{id:"PRJ-2024-001",name:"高精度数控机床升级",createTime:"2024-05-10",manager:"张工",deadline:"2024-09-30",budget:"¥1,500,000",spent:"¥980,000",spentPercentage:65,currentPhase:"进行中"},currentPhaseIndex:1}},computed:{isFirstPhase(){return this.currentPhaseIndex===0},isLastPhase(){return this.currentPhaseIndex===this.projectPhases.length-1},progressWidth(){return this.currentPhaseIndex/(this.projectPhases.length-1)*100}},methods:{getPhaseColor(l){return{未开始:"blue",进行中:"green",已关闭:"red"}[l]||"blue"},advancePhase(){this.isLastPhase||this.$confirm({title:"确定要推进项目阶段吗?",content:`确定要将项目推进到 "${this.projectPhases[this.currentPhaseIndex+1]}" 吗？`,okText:"确认",cancelText:"取消",onOk:()=>{this.currentPhaseIndex++,this.projectInfo.currentPhase=this.projectPhases[this.currentPhaseIndex],this.$message.success(`项目已成功推进到 "${this.projectInfo.currentPhase}" 阶段`)}})},revertPhase(){this.isFirstPhase||this.$confirm({title:"确定要回退项目阶段吗?",content:`确定要将项目回退到 "${this.projectPhases[this.currentPhaseIndex-1]}" 吗？这可能会影响已创建的订单状态。`,okText:"确认",cancelText:"取消",onOk:()=>{this.currentPhaseIndex--,this.projectInfo.currentPhase=this.projectPhases[this.currentPhaseIndex],this.$message.success(`项目已回退到 "${this.projectInfo.currentPhase}" 阶段`)}})},fetchProjectDetail(){const l=this.$route.params.id||this.$route.query.id;l&&console.log("加载项目数据: "+l)}},mounted(){this.fetchProjectDetail()}},Mt={class:"container"},Qt={class:"project-header"},Nt={class:"header-top"},Ot={class:"button-group"},Ft={class:"project-info"},Kt={class:"info-item"},jt={class:"info-value"},Vt={class:"info-item"},Bt={class:"info-value"},Ut={class:"info-item"},Ht={class:"info-value"},At={class:"info-item"},zt={class:"info-value"},Wt={class:"info-item"},Et={class:"info-value"},Lt={class:"info-item"},Gt={class:"info-value"},Jt={class:"tabs-container"};function Xt(l,n,D,N,u,y){const _=o("a-button"),d=o("a-tag"),O=o("BomManagement"),k=o("a-tab-pane"),x=o("RfqManagement"),g=o("PoManagement"),w=o("a-tabs");return r(),b("div",Mt,[a("div",Qt,[a("div",Nt,[n[3]||(n[3]=a("h2",null,[s(" 高精度数控机床升级 "),a("span",{class:"project-code"},"PRJ-2024-001")],-1)),a("div",Ot,[t(_,{type:"primary"},{default:e(()=>n[1]||(n[1]=[a("i",{class:"fas fa-edit"},null,-1),s(" 编辑")])),_:1}),t(_,{type:"primary"},{default:e(()=>n[2]||(n[2]=[a("i",{class:"fas fa-chart-line"},null,-1),s(" 项目报表")])),_:1})])]),n[10]||(n[10]=a("div",{class:"risk-warning"},[a("i",{class:"fas fa-exclamation-triangle"}),s(" 风险预警：交期延误，独家供应")],-1)),a("div",Ft,[a("div",Kt,[n[4]||(n[4]=a("div",{class:"info-label"},"创建时间",-1)),a("div",jt,c(u.projectInfo.createTime),1)]),a("div",Vt,[n[5]||(n[5]=a("div",{class:"info-label"},"负责人",-1)),a("div",Bt,c(u.projectInfo.manager),1)]),a("div",Ut,[n[6]||(n[6]=a("div",{class:"info-label"},"截止日期",-1)),a("div",Ht,c(u.projectInfo.deadline),1)]),a("div",At,[n[7]||(n[7]=a("div",{class:"info-label"},"预算",-1)),a("div",zt,c(u.projectInfo.budget),1)]),a("div",Wt,[n[8]||(n[8]=a("div",{class:"info-label"},"已支出",-1)),a("div",Et,c(u.projectInfo.spent)+" ("+c(u.projectInfo.spentPercentage)+"%)",1)]),a("div",Lt,[n[9]||(n[9]=a("div",{class:"info-label"},"当前阶段",-1)),a("div",Gt,[t(d,{color:y.getPhaseColor(u.projectInfo.currentPhase)},{default:e(()=>[s(c(u.projectInfo.currentPhase),1)]),_:1},8,["color"])])])])]),a("div",Jt,[t(w,{activeKey:u.activeTabKey,"onUpdate:activeKey":n[0]||(n[0]=h=>u.activeTabKey=h)},{default:e(()=>[t(k,{key:"bom",tab:"物料管理"},{default:e(()=>[t(O)]),_:1}),t(k,{key:"inquiry",tab:"询价单管理"},{default:e(()=>[t(x)]),_:1}),t(k,{key:"purchase",tab:"采购订单管理"},{default:e(()=>[t(g)]),_:1}),t(k,{key:"history",tab:"操作历史"})]),_:1},8,["activeKey"])])])}const ea=A(Dt,[["render",Xt],["__scopeId","data-v-09290633"]]);export{ea as default};
