import{r as D,c as Z,b as g,d as p,g as e,w as t,h as s,j as r,l as P,G as be,t as _,k as b,f as T,F as E,i as X,u as Ie,o as d,D as Te,a as ge,m as Ne,n as R,M as he}from"./index-CAFrc3c2.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{E as ke,P as we}from"./PrinterOutlined-Bd0vDe_s.js";import{S as fe}from"./SettingOutlined-CbvaVSGf.js";import{O as Re,A as Ae}from"./OrderedListOutlined-DO5arWvD.js";const Fe={class:"table-area"},Pe={class:"table-operations"},Me={class:"selection-summary"},Oe={style:{color:"#666"}},Ee={class:"summary-content"},Ue=["onClick"],Ke=["onClick"],je=["onClick"],qe=["onClick"],Be=["onClick"],Le={__name:"applyMaterialTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0})},selectedRowKeys:{type:Array,default:()=>[]}},emits:["tableChange","selectChange","export","print","edit","delete","submit","review","columnsChange"],setup($,{emit:ee}){const J=Ie(),B=D(!1),U=()=>{B.value=!B.value},L=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:150,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:150},{title:"申请单号",dataIndex:"soNo",key:"soNo",width:180},{title:"审核状态",dataIndex:"approvalStatus",key:"approvalStatus",width:100},{title:"创建人",dataIndex:"creator",key:"creator",width:100},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:150},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作",dataIndex:"action",key:"action",width:220,fixed:"right"}],G=D(["soNo","name","model","quantity","unitPrice","totalPrice","approvalStatus","creator","createTime","action"]),C=Z(()=>L.filter(l=>G.value.includes(l.dataIndex)||l.fixed)),z=l=>{G.value=l,k("columnsChange",l)},ne=$,k=ee,H=Z(()=>parseFloat(ne.tableData.filter(l=>ne.selectedRowKeys.includes(l.id)).reduce((l,v)=>l+(v.totalPrice||0),0).toFixed(2))),A=l=>{k("selectChange",l)},f=l=>{k("tableChange",l)},S=l=>({pending:"default",reviewing:"processing",approved:"success",rejected:"error"})[l]||"default",K=l=>({pending:"未审核",reviewing:"审核中",approved:"审核通过",rejected:"审核不通过"})[l]||"未知",te=l=>["pending","rejected"].includes(l),w=l=>["pending","rejected"].includes(l),ue=l=>["pending","rejected"].includes(l),M=l=>l==="reviewing",de=l=>{J.push({path:"/workspace/purchase/applyDetail",query:{id:l.id,soNo:l.soNo}})},oe=l=>{k("edit",l)},le=l=>{k("delete",l)},ie=l=>{k("submit",l)},ce=l=>{k("review",l)},pe=()=>{k("export")},me=()=>{k("print")};return(l,v)=>{const ae=s("a-button"),se=s("a-space"),ve=s("a-tooltip"),a=s("a-tag"),i=s("a-table"),O=s("a-checkbox"),W=s("a-col"),re=s("a-row"),o=s("a-checkbox-group"),n=s("a-drawer");return d(),g("div",Fe,[p("div",Pe,[e(se,null,{default:t(()=>[e(ae,{type:"primary",onClick:pe},{default:t(()=>[e(P(ke)),v[1]||(v[1]=r(" 导出 "))]),_:1}),e(ae,{type:"primary",onClick:me},{default:t(()=>[e(P(we)),v[2]||(v[2]=r(" 打印 "))]),_:1})]),_:1}),e(ae,{onClick:U},{default:t(()=>[e(P(fe)),v[3]||(v[3]=r(" 列设置 "))]),_:1})]),p("div",Me,[p("div",null,[e(ve,{placement:"top"},{title:t(()=>v[4]||(v[4]=[p("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),p("div",null,"2. 草稿状态的物料申请可以编辑、删除或提交审批。",-1),p("div",null,"3. 提交后的物料申请将进入审批流程，无法再次编辑。",-1)])),default:t(()=>[p("span",Oe,[e(P(be),{style:{"margin-right":"4px"}}),v[5]||(v[5]=r("物料申请说明"))])]),_:1})]),p("div",Ee,[p("span",null,[v[6]||(v[6]=r("已选择：")),e(a,{color:"red"},{default:t(()=>[r(_($.selectedRowKeys.length),1)]),_:1}),v[7]||(v[7]=r(" 个物料"))]),p("span",null,[v[8]||(v[8]=r("总金额：")),e(a,{color:"red"},{default:t(()=>[r("¥"+_(H.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),e(i,{columns:C.value,"data-source":$.tableData,loading:$.loading,pagination:$.pagination,onChange:f,"row-key":"id","row-selection":{selectedRowKeys:$.selectedRowKeys,onChange:A},bordered:"",scroll:{x:1800}},{bodyCell:t(({column:m,record:c})=>[m.dataIndex==="unitPrice"||m.dataIndex==="totalPrice"?(d(),g(E,{key:0},[r(_(parseFloat(c[m.dataIndex]).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):b("",!0),m.dataIndex==="approvalStatus"?(d(),T(a,{key:1,color:S(c.approvalStatus)},{default:t(()=>[r(_(K(c.approvalStatus)),1)]),_:2},1032,["color"])):b("",!0),m.dataIndex==="action"?(d(),T(se,{key:2},{default:t(()=>[p("a",{onClick:I=>de(c)},"申请详情",8,Ue),te(c.approvalStatus)?(d(),g("a",{key:0,onClick:I=>oe(c)},"编辑",8,Ke)):b("",!0),w(c.approvalStatus)?(d(),g("a",{key:1,onClick:I=>le(c),class:"danger-link"},"删除",8,je)):b("",!0),ue(c.approvalStatus)?(d(),g("a",{key:2,onClick:I=>ie(c),class:"submit-link"},"提交",8,qe)):b("",!0),M(c.approvalStatus)?(d(),g("a",{key:3,onClick:I=>ce(c),class:"review-link"},"审核",8,Be)):b("",!0)]),_:2},1024)):b("",!0)]),_:1},8,["columns","data-source","loading","pagination","row-selection"]),e(n,{title:"配置表格列",placement:"right",visible:B.value,onClose:U,width:"400px"},{default:t(()=>[e(o,{value:G.value,"onUpdate:value":v[0]||(v[0]=m=>G.value=m),onChange:z},{default:t(()=>[e(re,null,{default:t(()=>[(d(),g(E,null,X(L,m=>e(W,{span:12,key:m.dataIndex},{default:t(()=>[e(O,{value:m.dataIndex,disabled:m.fixed},{default:t(()=>[r(_(m.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])])}}},ze=_e(Le,[["__scopeId","data-v-8f781973"]]),Ye={class:"table-area"},Qe={class:"table-operations"},Ve={class:"selection-summary"},Je={style:{color:"#666"}},Ge={class:"summary-content"},He={style:{margin:"12px"}},We=["onClick"],Xe=["onClick"],Ze=["onClick"],et=["onClick"],tt={__name:"applyOrderTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0})},selectedRowKeys:{type:Array,default:()=>[]}},emits:["tableChange","selectChange","export","print","edit","delete","submit","review","batchSubmit","columnsChange"],setup($,{emit:ee}){const J=D(!1),B=()=>{J.value=!J.value},U=[{title:"申请单号",dataIndex:"soNo",key:"soNo",width:180,fixed:"left"},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:180},{title:"审核状态",dataIndex:"approvalStatus",key:"approvalStatus",width:100},{title:"物料数量",dataIndex:"materialCount",key:"materialCount",width:90},{title:"总金额（¥）",dataIndex:"totalAmount",key:"totalAmount",width:120},{title:"采购员",dataIndex:"creator",key:"creator",width:100},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:150},{title:"操作",dataIndex:"action",key:"action",width:220,fixed:"right"}],L=D(["soNo","approvalStatus","materialCount","totalAmount","creator","createTime","action"]),G=Z(()=>U.filter(a=>L.value.includes(a.dataIndex)||a.fixed)),C=[{title:"物料名称",dataIndex:"name",key:"name",width:180},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"备注",dataIndex:"remark",key:"remark",width:150}],z=D(C.map(a=>a.dataIndex)),ne=Z(()=>C.filter(a=>z.value.includes(a.dataIndex)||a.fixed)),k=a=>{L.value=a,f("columnsChange",a)},H=a=>{z.value=a},A=$,f=ee,S=D([]),K=(a,i)=>{a?S.value=[...S.value,i.id]:S.value=S.value.filter(O=>O!==i.id)},te=Z(()=>parseFloat(A.tableData.filter(a=>A.selectedRowKeys.includes(a.id)).reduce((a,i)=>a+(i.totalAmount||0),0).toFixed(2))),w=a=>{f("selectChange",a)},ue=({key:a})=>{console.log(`Batch operation triggered: ${a}`,A.selectedRowKeys),a==="submit"?f("batchSubmit"):a==="delete"?console.log("Batch delete"):a==="export"&&f("export")},M=a=>{f("tableChange",a)},de=a=>({pending:"default",reviewing:"processing",approved:"success",rejected:"error"})[a]||"default",oe=a=>({pending:"未审核",reviewing:"审核中",approved:"审核通过",rejected:"审核不通过"})[a]||"未知",le=a=>["pending","rejected"].includes(a),ie=a=>["pending","rejected"].includes(a),ce=a=>["pending","rejected"].includes(a),pe=a=>a==="reviewing",me=a=>{f("edit",a)},l=a=>{f("delete",a)},v=a=>{f("submit",a)},ae=a=>{f("review",a)},se=()=>{f("export")},ve=()=>{f("print")};return(a,i)=>{const O=s("a-button"),W=s("a-menu-item"),re=s("a-menu"),o=s("a-dropdown"),n=s("a-space"),m=s("a-tooltip"),c=s("a-tag"),I=s("a-table"),Y=s("a-checkbox"),Q=s("a-col"),x=s("a-row"),j=s("a-checkbox-group"),ye=s("a-divider"),N=s("a-drawer");return d(),g("div",Ye,[p("div",Qe,[e(n,null,{default:t(()=>[e(o,null,{overlay:t(()=>[e(re,{onClick:ue},{default:t(()=>[e(W,{key:"submit"},{default:t(()=>i[3]||(i[3]=[r("提交申请")])),_:1}),e(W,{key:"delete"},{default:t(()=>i[4]||(i[4]=[r("删除申请")])),_:1}),e(W,{key:"export"},{default:t(()=>i[5]||(i[5]=[r("导出申请")])),_:1})]),_:1})]),default:t(()=>[e(O,{type:"primary"},{default:t(()=>[i[2]||(i[2]=r(" 批量操作 ")),e(P(Te))]),_:1})]),_:1}),e(O,{type:"primary",onClick:se},{default:t(()=>[e(P(ke)),i[6]||(i[6]=r(" 导出 "))]),_:1}),e(O,{type:"primary",onClick:ve},{default:t(()=>[e(P(we)),i[7]||(i[7]=r(" 打印 "))]),_:1})]),_:1}),e(O,{onClick:B},{default:t(()=>[e(P(fe)),i[8]||(i[8]=r(" 列设置 "))]),_:1})]),p("div",Ve,[p("div",null,[e(m,{placement:"top"},{title:t(()=>i[9]||(i[9]=[p("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),p("div",null,"2. 草稿状态的申请可以编辑、删除或提交审批。",-1),p("div",null,"3. 提交后的申请将进入审批流程，无法再次编辑。",-1)])),default:t(()=>[p("span",Je,[e(P(be),{style:{"margin-right":"4px"}}),i[10]||(i[10]=r("采购申请说明"))])]),_:1})]),p("div",Ge,[p("span",null,[i[11]||(i[11]=r("已选择：")),e(c,{color:"red"},{default:t(()=>[r(_($.selectedRowKeys.length),1)]),_:1}),i[12]||(i[12]=r(" 个申请"))]),p("span",null,[i[13]||(i[13]=r("总金额：")),e(c,{color:"red"},{default:t(()=>[r("¥"+_(te.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),e(I,{columns:G.value,"data-source":$.tableData,loading:$.loading,pagination:$.pagination,onChange:M,"row-key":"id",size:"small","row-selection":{selectedRowKeys:$.selectedRowKeys,onChange:w},bordered:"",scroll:{x:1500},expandable:{expandedRowKeys:S.value,onExpand:K,expandRowByClick:!0}},{expandedRowRender:t(({record:y})=>[p("div",He,[e(I,{columns:ne.value,"data-source":y.materialItems,pagination:!1,"row-key":"id",bordered:"",size:"small"},{bodyCell:t(({column:h,text:q,record:V})=>[h.dataIndex==="unitPrice"||h.dataIndex==="totalPrice"?(d(),g(E,{key:0},[r(_(parseFloat(q).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):b("",!0),h.dataIndex==="urgency"?(d(),T(c,{key:1,color:a.getUrgencyColor(V.urgency)},{default:t(()=>[r(_(a.getUrgencyText(V.urgency)),1)]),_:2},1032,["color"])):b("",!0),h.dataIndex==="department"?(d(),T(c,{key:2,color:a.getDepartmentColor(V.department)},{default:t(()=>[r(_(a.getDepartmentText(V.department)),1)]),_:2},1032,["color"])):b("",!0)]),_:2},1032,["columns","data-source"])])]),bodyCell:t(({column:y,record:h})=>[y.dataIndex==="totalAmount"?(d(),g(E,{key:0},[r(_(parseFloat(h.totalAmount).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):b("",!0),y.dataIndex==="approvalStatus"?(d(),T(c,{key:1,color:de(h.approvalStatus)},{default:t(()=>[r(_(oe(h.approvalStatus)),1)]),_:2},1032,["color"])):b("",!0),y.dataIndex==="action"?(d(),T(n,{key:2},{default:t(()=>[le(h.approvalStatus)?(d(),g("a",{key:0,onClick:q=>me(h)},"编辑",8,We)):b("",!0),ie(h.approvalStatus)?(d(),g("a",{key:1,onClick:q=>l(h)},"删除",8,Xe)):b("",!0),ce(h.approvalStatus)?(d(),g("a",{key:2,onClick:q=>v(h)},"提交",8,Ze)):b("",!0),pe(h.approvalStatus)?(d(),g("a",{key:3,onClick:q=>ae(h)},"审核",8,et)):b("",!0)]),_:2},1024)):b("",!0)]),_:1},8,["columns","data-source","loading","pagination","row-selection","expandable"]),e(N,{title:"配置表格列",placement:"right",visible:J.value,onClose:B,width:"400px"},{default:t(()=>[i[14]||(i[14]=p("h4",{style:{"margin-bottom":"12px"}},"申请单列配置",-1)),e(j,{value:L.value,"onUpdate:value":i[0]||(i[0]=y=>L.value=y),onChange:k},{default:t(()=>[e(x,null,{default:t(()=>[(d(),g(E,null,X(U,y=>e(Q,{span:12,key:y.dataIndex},{default:t(()=>[e(Y,{value:y.dataIndex,disabled:y.fixed},{default:t(()=>[r(_(y.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"]),e(ye),i[15]||(i[15]=p("h4",{style:{"margin-top":"24px","margin-bottom":"12px"}},"物料列配置 (展开行内)",-1)),e(j,{value:z.value,"onUpdate:value":i[1]||(i[1]=y=>z.value=y),onChange:H},{default:t(()=>[e(x,null,{default:t(()=>[(d(),g(E,null,X(C,y=>e(Q,{span:12,key:y.dataIndex},{default:t(()=>[e(Y,{value:y.dataIndex,disabled:y.fixed},{default:t(()=>[r(_(y.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])])}}},at=_e(tt,[["__scopeId","data-v-61294275"]]),nt={class:"apply-container"},ot={class:"search-area"},lt={class:"view-selector"},it={class:"submit-form"},st={class:"submit-summary"},rt={key:0},ut={key:0},dt={__name:"apply",setup($){const ee=D(!1),J=()=>{ee.value=!ee.value},B=[{key:"soNo",label:"申请单号",type:"input"},{key:"materialName",label:"物料名称",type:"input"},{key:"materialModel",label:"物料型号",type:"input"},{key:"createTimeRange",label:"创建时间",type:"dateRange"},{key:"creator",label:"创建人",type:"input"},{key:"approvalStatus",label:"审核状态",type:"select",options:[{label:"未审核",value:"pending"},{label:"审核中",value:"reviewing"},{label:"审核通过",value:"approved"},{label:"审核不通过",value:"rejected"}]},{key:"category",label:"物料分类",type:"select",options:[{label:"电子元件",value:"electronics"},{label:"机械零件",value:"mechanical"},{label:"原材料",value:"raw"}]},{key:"brand",label:"品牌",type:"input"}],U=D(["soNo","createTimeRange","creator","approvalStatus"]),L=Z(()=>B.filter(o=>U.value.includes(o.key))),G=o=>{U.value=o},C=ge({soNo:"",materialName:"",materialModel:"",createTimeRange:[],creator:"",approvalStatus:void 0,category:void 0,brand:""}),z=()=>{A.current=1,l()},ne=()=>{Object.keys(C).forEach(o=>{Array.isArray(C[o])?C[o]=[]:C[o]=void 0}),z()},k=D([]),H=D(!1),A=ge({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),f=D("order"),S=D([]),K=D(!1),te=D(!1),w=ge({submitNote:"",approver:void 0,expectedDeliveryDate:void 0}),ue=D([{id:"user001",name:"张经理",department:"采购部"},{id:"user002",name:"李主管",department:"财务部"},{id:"user003",name:"王总监",department:"运营部"}]),M=Z(()=>{if(f.value==="order")return k.value.filter(o=>S.value.includes(o.id));{const o=k.value.filter(m=>S.value.includes(m.id)),n=new Map;return o.forEach(m=>{n.has(m.soNo)||n.set(m.soNo,{id:m.soNo,soNo:m.soNo,totalAmount:0,materials:[]});const c=n.get(m.soNo);c.totalAmount+=m.totalPrice,c.materials.push(m)}),Array.from(n.values())}}),de=Z(()=>M.value.reduce((o,n)=>o+(n.totalAmount||0),0)),oe=o=>{A.current=o.current,A.pageSize=o.pageSize,l()},le=o=>{S.value=o},ie=o=>{f.value=o,S.value=[],l()},ce=o=>{console.log("Material columns changed:",o)},pe=o=>{console.log("Order columns changed:",o)},me=o=>{const n=o.getFullYear(),m=(o.getMonth()+1).toString().padStart(2,"0"),c=o.getDate().toString().padStart(2,"0"),I=o.getHours().toString().padStart(2,"0"),Y=o.getMinutes().toString().padStart(2,"0");return`${n}-${m}-${c} ${I}:${Y}`},l=()=>{H.value=!0,setTimeout(()=>{const o=[],n=[];Array.from({length:8}).forEach((m,c)=>{const I=new Date(Date.now()-Math.random()*10*24*60*60*1e3),Y=["pending","reviewing","approved","rejected"],Q=Y[c%Y.length],x={id:`apply-order-${c}`,soNo:`APPLY-${2e3+c}`,rfqNo:`RFQ-2023-${2e3+c}`,status:"not_submitted",creator:`申请人${c%4+1}`,approvalStatus:Q,createTime:me(I),totalAmount:0,materialCount:0,materialItems:[],submitNote:"",approver:null,expectedDeliveryDate:null},j=Math.floor(Math.random()*4)+1;x.materialCount=0,Array.from({length:j}).forEach((ye,N)=>{const y=Math.floor(Math.random()*50)+1,h=parseFloat((Math.random()*800+50).toFixed(2)),q=parseFloat((y*h).toFixed(2));x.totalAmount+=q,x.materialCount+=y;const V={id:`apply-material-${c}-${N}`,soNo:x.soNo,name:`测试物料 ${N+1}`,model:`MODEL-${1e3+N}`,brand:N%3===0?"品牌A":N%3===1?"品牌B":"品牌C",category:N%2===0?"电子元件":"机械零件",unit:"个",quantity:y,unitPrice:h,totalPrice:q,rfqNo:`RFQ-2023-${2e3+N}`,status:"not_submitted",creator:x.creator,approvalStatus:x.approvalStatus,createTime:x.createTime,remark:N%3===0?"特殊规格要求":"",preparingQuantity:0,inTransitQuantity:0,acceptedQuantity:0,cancelledQuantity:0,expectedArrivalTime:null,logisticsStatus:null,financialStatus:null};o.push(V),x.materialItems.push(V)}),x.totalAmount=parseFloat(x.totalAmount.toFixed(2)),n.push(x)}),f.value==="product"?k.value=o:k.value=n,A.total=(f.value==="product"?o.length:n.length)*3,H.value=!1},500)},v=o=>{S.value=[o.id],K.value=!0},ae=()=>{if(S.value.length===0){R.warning("请选择要提交的申请");return}K.value=!0},se=async()=>{if(!w.submitNote.trim()){R.error("请输入提交说明");return}te.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),R.success(`成功提交 ${M.value.length} 个采购申请`),K.value=!1,S.value=[],w.submitNote="",w.approver=void 0,w.expectedDeliveryDate=void 0,l()}catch{R.error("提交失败，请重试")}finally{te.value=!1}},ve=()=>{K.value=!1,w.submitNote="",w.approver=void 0,w.expectedDeliveryDate=void 0},a=o=>{R.info(`编辑申请: ${o.soNo}`)},i=o=>{he.confirm({title:"确认删除",content:`确定要删除申请 ${o.soNo} 吗？此操作不可恢复。`,okText:"确定",cancelText:"取消",onOk:async()=>{try{await new Promise(n=>setTimeout(n,500)),R.success("删除成功"),l()}catch{R.error("删除失败，请重试")}}})},O=o=>{he.confirm({title:"确认审核",content:`确定要审核申请 ${o.soNo} 吗？`,okText:"通过",cancelText:"不通过",onOk:async()=>{try{await new Promise(n=>setTimeout(n,500)),R.success("审核通过"),l()}catch{R.error("审核失败，请重试")}},onCancel:async()=>{try{await new Promise(n=>setTimeout(n,500)),R.info("审核不通过"),l()}catch{R.error("操作失败，请重试")}}})},W=()=>{R.info("导出功能待实现")},re=()=>{R.info("打印功能待实现")};return Ne(()=>{l()}),(o,n)=>{const m=s("a-input"),c=s("a-select-option"),I=s("a-select"),Y=s("a-range-picker"),Q=s("a-form-item"),x=s("a-col"),j=s("a-button"),ye=s("a-space"),N=s("a-row"),y=s("a-form"),h=s("a-checkbox"),q=s("a-checkbox-group"),V=s("a-drawer"),xe=s("a-button-group"),Ce=s("a-textarea"),Se=s("a-date-picker"),De=s("a-alert"),$e=s("a-modal");return d(),g("div",nt,[p("div",ot,[e(y,{style:{display:"block"},layout:"inline",model:C},{default:t(()=>[e(N,null,{default:t(()=>[(d(!0),g(E,null,X(L.value,u=>(d(),T(x,{key:u.key,span:4},{default:t(()=>[e(Q,{label:u.label},{default:t(()=>[u.type==="input"?(d(),T(m,{key:0,value:C[u.key],"onUpdate:value":F=>C[u.key]=F,placeholder:`请输入${u.label}`},null,8,["value","onUpdate:value","placeholder"])):u.type==="select"?(d(),T(I,{key:1,value:C[u.key],"onUpdate:value":F=>C[u.key]=F,placeholder:`请选择${u.label}`,style:{width:"100%"},allowClear:""},{default:t(()=>[(d(!0),g(E,null,X(u.options,F=>(d(),T(c,{key:F.value,value:F.value},{default:t(()=>[r(_(F.label),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])):u.type==="dateRange"?(d(),T(Y,{key:2,value:C[u.key],"onUpdate:value":F=>C[u.key]=F,format:"YYYY-MM-DD",style:{width:"100%"}},null,8,["value","onUpdate:value"])):b("",!0)]),_:2},1032,["label"])]),_:2},1024))),128)),e(x,{span:4,class:"search-buttons"},{default:t(()=>[e(ye,null,{default:t(()=>[e(j,{type:"primary",onClick:z},{default:t(()=>n[7]||(n[7]=[r("查询")])),_:1}),e(j,{onClick:ne},{default:t(()=>n[8]||(n[8]=[r("重置")])),_:1}),e(j,{type:"link",onClick:J},{default:t(()=>[e(P(fe)),n[9]||(n[9]=r(" 配置搜索项 "))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e(V,{title:"配置搜索项",placement:"right",visible:ee.value,onClose:J,width:"400px"},{default:t(()=>[e(q,{value:U.value,"onUpdate:value":n[0]||(n[0]=u=>U.value=u),onChange:G},{default:t(()=>[e(N,null,{default:t(()=>[(d(),g(E,null,X(B,u=>e(x,{span:12,key:u.key},{default:t(()=>[e(h,{value:u.key},{default:t(()=>[r(_(u.label),1)]),_:2},1032,["value"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])]),p("div",lt,[e(xe,null,{default:t(()=>[e(j,{type:f.value==="order"?"primary":"default",onClick:n[1]||(n[1]=u=>ie("order"))},{default:t(()=>[e(P(Re)),n[10]||(n[10]=r(" 申请单视图 "))]),_:1},8,["type"]),e(j,{type:f.value==="product"?"primary":"default",onClick:n[2]||(n[2]=u=>ie("product"))},{default:t(()=>[e(P(Ae)),n[11]||(n[11]=r(" 物料视图 "))]),_:1},8,["type"])]),_:1})]),f.value==="product"?(d(),T(ze,{key:0,tableData:k.value,loading:H.value,pagination:A,selectedRowKeys:S.value,onTableChange:oe,onSelectChange:le,onExport:W,onPrint:re,onEdit:a,onDelete:i,onSubmit:v,onReview:O,onColumnsChange:ce},null,8,["tableData","loading","pagination","selectedRowKeys"])):(d(),T(at,{key:1,tableData:k.value,loading:H.value,pagination:A,selectedRowKeys:S.value,onTableChange:oe,onSelectChange:le,onExport:W,onPrint:re,onEdit:a,onDelete:i,onSubmit:v,onReview:O,onBatchSubmit:ae,onColumnsChange:pe},null,8,["tableData","loading","pagination","selectedRowKeys"])),e($e,{visible:K.value,"onUpdate:visible":n[6]||(n[6]=u=>K.value=u),title:"提交采购申请","confirm-loading":te.value,onOk:se,onCancel:ve,width:"600px"},{default:t(()=>[p("div",it,[e(y,{model:w,layout:"vertical"},{default:t(()=>[e(Q,{label:"提交说明",required:""},{default:t(()=>[e(Ce,{value:w.submitNote,"onUpdate:value":n[3]||(n[3]=u=>w.submitNote=u),placeholder:"请输入提交说明，描述此次采购申请的目的和要求",rows:4,showCount:"",maxlength:500},null,8,["value"])]),_:1}),e(Q,{label:"指定审批人"},{default:t(()=>[e(I,{value:w.approver,"onUpdate:value":n[4]||(n[4]=u=>w.approver=u),placeholder:"请选择审批人（可选，不选择将使用默认审批流程）",allowClear:""},{default:t(()=>[(d(!0),g(E,null,X(ue.value,u=>(d(),T(c,{key:u.id,value:u.id},{default:t(()=>[r(_(u.name)+" - "+_(u.department),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),e(Q,{label:"预期交付时间"},{default:t(()=>[e(Se,{value:w.expectedDeliveryDate,"onUpdate:value":n[5]||(n[5]=u=>w.expectedDeliveryDate=u),style:{width:"100%"},placeholder:"请选择预期交付时间",format:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1},8,["model"]),p("div",st,[e(De,{message:`即将提交 ${M.value.length} 个采购申请`,type:"info","show-icon":""},{description:t(()=>[M.value.length>0?(d(),g("div",rt,[n[12]||(n[12]=p("p",null,[p("strong",null,"申请单号：")],-1)),p("ul",null,[(d(!0),g(E,null,X(M.value.slice(0,5),u=>{var F;return d(),g("li",{key:u.id},_(u.soNo)+" - 总金额: ¥"+_((F=u.totalAmount)==null?void 0:F.toLocaleString()),1)}),128)),M.value.length>5?(d(),g("li",ut," ...还有 "+_(M.value.length-5)+" 个申请单 ",1)):b("",!0)]),p("p",null,[p("strong",null,"总计金额：¥"+_(de.value.toLocaleString()),1)])])):b("",!0)]),_:1},8,["message"])])])]),_:1},8,["visible","confirm-loading"])])}}},gt=_e(dt,[["__scopeId","data-v-5d93835f"]]);export{gt as default};
