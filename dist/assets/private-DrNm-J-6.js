import{_ as Ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as t,I as Fe,r as v,a as M,c as A,m as ze,b as K,w as a,h as u,d as r,j as i,l as S,P as Ve,f as j,k as U,t as qe,o as I}from"./index-CAFrc3c2.js";import{U as Be}from"./UploadOutlined-Ck8S9964.js";import{M as Ae}from"./MailOutlined-DP2u13EN.js";import{I as te}from"./InboxOutlined-DFyO9W96.js";var Ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"};function de(k){for(var o=1;o<arguments.length;o++){var g=arguments[o]!=null?Object(arguments[o]):{},f=Object.keys(g);typeof Object.getOwnPropertySymbols=="function"&&(f=f.concat(Object.getOwnPropertySymbols(g).filter(function(C){return Object.getOwnPropertyDescriptor(g,C).enumerable}))),f.forEach(function(C){je(k,C,g[C])})}return k}function je(k,o,g){return o in k?Object.defineProperty(k,o,{value:g,enumerable:!0,configurable:!0,writable:!0}):k[o]=g,k}var ae=function(o,g){var f=de({},o,g.attrs);return t(Fe,de({},f,{icon:Ke}),null)};ae.displayName="DatabaseOutlined";ae.inheritAttrs=!1;const Ne={class:"private-supplier-container"},He={class:"dashboard-header"},Ye={class:"progress-stats"},Ge={class:"dashboard-actions"},Je={key:2,class:"table-actions"},Qe=["onClick"],We=["onClick"],Xe=["onClick"],Ze=["onClick"],et={class:"upload-container"},tt={class:"ant-upload-drag-icon"},at={class:"api-integration-form"},lt={class:"notice-setting"},nt={class:"ant-upload-drag-icon"},ot={class:"upload-container"},st={class:"ant-upload-drag-icon"},it={__name:"private",setup(k){const o=v([{key:"1",name:"杭州机电有限公司",contact:"张三",phone:"13800138000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-15",registerTime:"2023-08-17",dataCompletionRate:100,type:"component"},{key:"2",name:"苏州精密机械制造厂",contact:"李四",phone:"13900139000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-15",registerTime:"2023-08-20",dataCompletionRate:85,type:"material"},{key:"3",name:"深圳电子科技有限公司",contact:"王五",phone:"13700137000",email:"<EMAIL>",status:"未入驻",inviteTime:"2023-08-18",registerTime:null,dataCompletionRate:0,type:"component"},{key:"4",name:"宁波自动化设备有限公司",contact:"赵六",phone:"13600136000",email:"<EMAIL>",status:"待邀请",inviteTime:null,registerTime:null,dataCompletionRate:0,type:"service"},{key:"5",name:"无锡工业零部件有限公司",contact:"钱七",phone:"13500135000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-10",registerTime:"2023-08-12",dataCompletionRate:100,type:"component"}]),g=[{title:"供应商名称",dataIndex:"name",key:"name"},{title:"联系人",dataIndex:"contact",key:"contact"},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"邮箱",dataIndex:"email",key:"email"},{title:"供应商类型",dataIndex:"type",key:"type",customRender:({text:l})=>({component:"零部件供应商",material:"原材料供应商",service:"服务供应商",other:"其他供应商"})[l]||l},{title:"入驻状态",dataIndex:"status",key:"status"},{title:"邀请时间",dataIndex:"inviteTime",key:"inviteTime"},{title:"入驻时间",dataIndex:"registerTime",key:"registerTime"},{title:"数据迁移状态",dataIndex:"dataStatus",key:"dataStatus"},{title:"操作",dataIndex:"action",key:"action"}],f=M({registered:3,pending:1,waitingForInvite:1,registerRate:60}),C=A(()=>o.value.length>0),pe=A(()=>f.pending>0),me=v(!1),le=v("all"),O=v(!1),N=v(!1),ne=v([]),oe=v("excel"),P=v([]),ve=v(""),se=v(["email","sms"]),_=M({systemType:void 0,apiUrl:"",authType:"token",authCredential:""}),H=v(!1),Y=v(!1),G=v([]),x=M({supplierId:void 0,dataTypes:["deliveryRecords","contracts"]}),fe=A(()=>o.value.map(l=>({value:l.key,label:l.name}))),ce=(l,e)=>((e==null?void 0:e.label)??"").toLowerCase().includes(l.toLowerCase());A(()=>o.value.filter(l=>l.status==="待邀请"));const ge=()=>{P.value=[],ve.value="",O.value=!0},ye=l=>{const e=l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel",p=l.size/1024/1024<2;return e?p?!1:window.$message.error("文件必须小于2MB!"):window.$message.error("只能上传Excel文件!")},be=l=>l.size/1024/1024<20?!1:window.$message.error("文件必须小于20MB!"),we=()=>{console.log("下载模板")},ke=async()=>{if(P.value.length===0)return window.$message.warning("请至少选择一家供应商进行邀请!");N.value=!0;try{await new Promise(b=>setTimeout(b,1500));const l=new Date,e=`${l.getFullYear()}-${String(l.getMonth()+1).padStart(2,"0")}-${String(l.getDate()).padStart(2,"0")}`;P.value.forEach(b=>{const L=o.value.findIndex(F=>F.key===b);L!==-1&&(o.value[L].status="未入驻",o.value[L].inviteTime=e)});const p=P.value.length;f.waitingForInvite-=p,f.pending+=p,window.$message.success(`成功向 ${p} 家供应商发送入驻邀请!`),O.value=!1}catch(l){console.error("邀请发送失败",l),window.$message.error("邀请发送失败，请重试!")}finally{N.value=!1}},_e=async()=>{Y.value=!0;try{await new Promise(e=>setTimeout(e,1500)),console.log("提交数据导入",{supplierId:x.supplierId,dataTypes:x.dataTypes,fileList:G.value}),window.$message.success("数据上传成功!"),H.value=!1;const l=o.value.find(e=>e.key===x.supplierId);l&&(l.dataCompletionRate=100)}catch(l){console.error("数据上传失败",l),window.$message.error("数据上传失败，请重试!")}finally{Y.value=!1}},xe=async l=>{try{await new Promise(e=>setTimeout(e,800)),console.log("发送催办提醒",l),window.$message.success(`已向 ${l.name} 发送入驻催办提醒!`)}catch(e){console.error("催办发送失败",e),window.$message.error("催办发送失败，请重试!")}},Ie=l=>{console.log("查看供应商详情",l)},Te=async()=>{try{const l=o.value.filter(e=>e.status==="未入驻");console.log("批量催办",l),await new Promise(e=>setTimeout(e,1e3)),window.$message.success(`已向 ${l.length} 家未入驻供应商发送入驻催办提醒!`)}catch(l){console.error("批量催办失败",l),window.$message.error("批量催办失败，请重试!")}},D=v(!1),J=v(!1),R=v("manual"),h=v([]),d=M({name:"",contact:"",phone:"",email:"",type:"component",remark:""}),E=v(!1),Q=v(!1),s=M({key:"",name:"",contact:"",phone:"",email:"",type:"component",remark:""}),Se=()=>{Object.keys(d).forEach(l=>{l!=="type"?d[l]="":d[l]="component"}),R.value="manual",h.value=[],D.value=!0},Ue=l=>{const e=l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel",p=l.size/1024/1024<2;return e?p?!1:window.$message.error("文件必须小于2MB!"):window.$message.error("只能上传Excel文件!")},Ce=()=>{console.log("下载供应商导入模板")},Le=async()=>{J.value=!0;try{if(await new Promise(l=>setTimeout(l,1e3)),R.value==="manual"){const l={key:`${o.value.length+1}`,name:d.name,contact:d.contact,phone:d.phone,email:d.email,type:d.type,status:"待邀请",inviteTime:null,registerTime:null,dataCompletionRate:0};o.value.push(l),f.waitingForInvite+=1,window.$message.success("供应商添加成功!")}else console.log("Excel导入供应商",h.value),window.$message.success("成功导入3家供应商!"),f.waitingForInvite+=3;D.value=!1}catch(l){console.error("添加供应商失败",l),window.$message.error("添加供应商失败，请重试!")}finally{J.value=!1}},$e=l=>{s.key=l.key,s.name=l.name,s.contact=l.contact,s.phone=l.phone,s.email=l.email,s.type=l.type||"component",s.remark=l.remark||"",E.value=!0},Me=async()=>{Q.value=!0;try{await new Promise(e=>setTimeout(e,1e3));const l=o.value.findIndex(e=>e.key===s.key);l!==-1&&(o.value[l]={...o.value[l],name:s.name,contact:s.contact,phone:s.phone,email:s.email,type:s.type,remark:s.remark},window.$message.success("供应商信息更新成功!")),E.value=!1}catch(l){console.error("更新供应商失败",l),window.$message.error("更新供应商失败，请重试!")}finally{Q.value=!1}},Oe=async l=>{try{await new Promise(p=>setTimeout(p,800));const e=o.value.findIndex(p=>p.key===l.key);if(e!==-1){const p=new Date,b=`${p.getFullYear()}-${String(p.getMonth()+1).padStart(2,"0")}-${String(p.getDate()).padStart(2,"0")}`;o.value[e].status="未入驻",o.value[e].inviteTime=b,f.waitingForInvite-=1,f.pending+=1,window.$message.success(`已向 ${l.name} 发送入驻邀请!`)}}catch(e){console.error("邀请发送失败",e),window.$message.error("邀请发送失败，请重试!")}};return ze(()=>{console.log("组件挂载")}),(l,e)=>{const p=u("a-statistic"),b=u("a-button"),L=u("a-card"),F=u("a-col"),ie=u("a-row"),Pe=u("a-tag"),De=u("a-progress"),z=u("a-divider"),Re=u("a-table"),T=u("a-tab-pane"),W=u("a-tabs"),X=u("a-upload-dragger"),y=u("a-select-option"),V=u("a-select"),m=u("a-form-item"),w=u("a-input"),Z=u("a-radio"),he=u("a-radio-group"),q=u("a-form"),$=u("a-checkbox"),ue=u("a-checkbox-group"),B=u("a-modal"),re=u("a-textarea");return I(),K("div",Ne,[t(ie,{gutter:16},{default:a(()=>[t(F,{span:24},{default:a(()=>[t(L,{title:"私有供应商管理",class:"dashboard-card"},{default:a(()=>[r("div",He,[r("div",Ye,[t(p,{title:"已入驻供应商",value:f.registered,suffix:"家",class:"stat-item"},null,8,["value"]),t(p,{title:"未入驻供应商",value:f.pending,suffix:"家",class:"stat-item"},null,8,["value"]),t(p,{title:"待邀请供应商",value:f.waitingForInvite,suffix:"家",class:"stat-item"},null,8,["value"]),t(p,{title:"入驻率",value:f.registerRate,suffix:"%",class:"stat-item"},null,8,["value"])]),r("div",Ge,[t(b,{type:"primary",onClick:Se,class:"action-btn"},{default:a(()=>[t(S(Ve)),e[29]||(e[29]=i(" 添加供应商 "))]),_:1}),t(b,{onClick:ge,class:"action-btn",disabled:!C.value},{default:a(()=>[t(S(Be)),e[30]||(e[30]=i(" 邀请供应商入驻 "))]),_:1},8,["disabled"]),t(b,{onClick:l.showDataImportModal},{default:a(()=>[t(S(ae)),e[31]||(e[31]=i(" 上传合作数据 "))]),_:1},8,["onClick"]),t(b,{onClick:Te,disabled:!pe.value},{default:a(()=>[t(S(Ae)),e[32]||(e[32]=i(" 批量催办 "))]),_:1},8,["disabled"])])])]),_:1})]),_:1})]),_:1}),t(ie,{gutter:16,class:"row-margin"},{default:a(()=>[t(F,{span:24},{default:a(()=>[t(L,{title:"供应商迁移看板",class:"migration-board"},{default:a(()=>[t(W,{activeKey:le.value,"onUpdate:activeKey":e[0]||(e[0]=n=>le.value=n)},{default:a(()=>[t(T,{key:"all",tab:"全部供应商"},{default:a(()=>[t(Re,{dataSource:o.value,columns:g,pagination:{pageSize:10},loading:me.value},{bodyCell:a(({column:n,record:c})=>[n.dataIndex==="status"?(I(),j(Pe,{key:0,color:c.status==="已入驻"?"success":c.status==="未入驻"?"warning":"default"},{default:a(()=>[i(qe(c.status),1)]),_:2},1032,["color"])):U("",!0),n.dataIndex==="dataStatus"?(I(),j(De,{key:1,percent:c.dataCompletionRate,size:"small",status:c.dataCompletionRate===100?"success":"active"},null,8,["percent","status"])):U("",!0),n.dataIndex==="action"?(I(),K("div",Je,[c.status==="待邀请"?(I(),K("a",{key:0,onClick:ee=>Oe(c)},"邀请入驻",8,Qe)):U("",!0),c.status==="待邀请"?(I(),j(z,{key:1,type:"vertical"})):U("",!0),c.status==="未入驻"&&c.inviteTime?(I(),K("a",{key:2,onClick:ee=>xe(c)},"催办",8,We)):U("",!0),c.status==="未入驻"&&c.inviteTime?(I(),j(z,{key:3,type:"vertical"})):U("",!0),r("a",{onClick:ee=>Ie(c)},"详情",8,Xe),t(z,{type:"vertical"}),r("a",{onClick:ee=>$e(c)},"编辑",8,Ze)])):U("",!0)]),_:1},8,["dataSource","loading"])]),_:1}),t(T,{key:"registered",tab:"已入驻供应商"}),t(T,{key:"pending",tab:"未入驻供应商"})]),_:1},8,["activeKey"])]),_:1})]),_:1})]),_:1}),t(B,{visible:O.value,"onUpdate:visible":e[8]||(e[8]=n=>O.value=n),title:"一键邀请供应商",onOk:ke,confirmLoading:N.value,width:"700px"},{default:a(()=>[t(W,{activeKey:oe.value,"onUpdate:activeKey":e[6]||(e[6]=n=>oe.value=n)},{default:a(()=>[t(T,{key:"excel",tab:"Excel导入"},{default:a(()=>[r("div",et,[t(X,{fileList:ne.value,"onUpdate:fileList":e[1]||(e[1]=n=>ne.value=n),beforeUpload:ye,multiple:!1,showUploadList:!0,accept:".xlsx,.xls"},{default:a(()=>[r("p",tt,[t(S(te))]),e[33]||(e[33]=r("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[34]||(e[34]=r("p",{class:"ant-upload-hint"}," 支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段 ",-1))]),_:1},8,["fileList"]),r("div",{class:"template-download"},[r("a",{onClick:we},"下载导入模板")])])]),_:1}),t(T,{key:"api",tab:"API对接"},{default:a(()=>[r("div",at,[t(q,{model:_,layout:"vertical"},{default:a(()=>[t(m,{label:"系统类型",name:"systemType"},{default:a(()=>[t(V,{value:_.systemType,"onUpdate:value":e[2]||(e[2]=n=>_.systemType=n),placeholder:"请选择系统类型"},{default:a(()=>[t(y,{value:"erp"},{default:a(()=>e[35]||(e[35]=[i("ERP系统")])),_:1}),t(y,{value:"crm"},{default:a(()=>e[36]||(e[36]=[i("CRM系统")])),_:1}),t(y,{value:"other"},{default:a(()=>e[37]||(e[37]=[i("其他系统")])),_:1})]),_:1},8,["value"])]),_:1}),t(m,{label:"API链接",name:"apiUrl"},{default:a(()=>[t(w,{value:_.apiUrl,"onUpdate:value":e[3]||(e[3]=n=>_.apiUrl=n),placeholder:"请输入API链接"},null,8,["value"])]),_:1}),t(m,{label:"认证类型",name:"authType"},{default:a(()=>[t(he,{value:_.authType,"onUpdate:value":e[4]||(e[4]=n=>_.authType=n)},{default:a(()=>[t(Z,{value:"token"},{default:a(()=>e[38]||(e[38]=[i("Token")])),_:1}),t(Z,{value:"oauth"},{default:a(()=>e[39]||(e[39]=[i("OAuth")])),_:1}),t(Z,{value:"basic"},{default:a(()=>e[40]||(e[40]=[i("Basic Auth")])),_:1})]),_:1},8,["value"])]),_:1}),t(m,{label:"认证凭证",name:"authCredential"},{default:a(()=>[t(w,{value:_.authCredential,"onUpdate:value":e[5]||(e[5]=n=>_.authCredential=n),placeholder:"请输入认证凭证"},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1})]),_:1},8,["activeKey"]),r("div",lt,[t(z),e[43]||(e[43]=r("h4",null,"通知设置",-1)),t(ue,{value:se.value,"onUpdate:value":e[7]||(e[7]=n=>se.value=n)},{default:a(()=>[t($,{value:"email"},{default:a(()=>e[41]||(e[41]=[i("邮件通知")])),_:1}),t($,{value:"sms"},{default:a(()=>e[42]||(e[42]=[i("短信通知")])),_:1})]),_:1},8,["value"])])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:H.value,"onUpdate:visible":e[12]||(e[12]=n=>H.value=n),title:"上传供应商历史合作数据",onOk:_e,confirmLoading:Y.value,width:"700px"},{default:a(()=>[t(q,{model:x,layout:"vertical"},{default:a(()=>[t(m,{label:"选择供应商",name:"supplierId"},{default:a(()=>[t(V,{value:x.supplierId,"onUpdate:value":e[9]||(e[9]=n=>x.supplierId=n),placeholder:"请选择供应商","show-search":"",options:fe.value,"filter-option":ce},null,8,["value","options"])]),_:1}),t(m,{label:"数据类型",name:"dataType"},{default:a(()=>[t(ue,{value:x.dataTypes,"onUpdate:value":e[10]||(e[10]=n=>x.dataTypes=n)},{default:a(()=>[t($,{value:"deliveryRecords"},{default:a(()=>e[44]||(e[44]=[i("交货记录")])),_:1}),t($,{value:"contracts"},{default:a(()=>e[45]||(e[45]=[i("合同数据")])),_:1}),t($,{value:"qualityRecords"},{default:a(()=>e[46]||(e[46]=[i("质量记录")])),_:1}),t($,{value:"contactInfo"},{default:a(()=>e[47]||(e[47]=[i("联系人信息")])),_:1})]),_:1},8,["value"])]),_:1}),t(m,{label:"选择文件",name:"fileList"},{default:a(()=>[t(X,{fileList:G.value,"onUpdate:fileList":e[11]||(e[11]=n=>G.value=n),beforeUpload:be,multiple:!0,showUploadList:!0},{default:a(()=>[r("p",nt,[t(S(te))]),e[48]||(e[48]=r("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[49]||(e[49]=r("p",{class:"ant-upload-hint"}," 支持Excel、CSV、PDF等格式文件，文件大小不超过20MB ",-1))]),_:1},8,["fileList"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:D.value,"onUpdate:visible":e[21]||(e[21]=n=>D.value=n),title:"添加供应商",onOk:Le,confirmLoading:J.value,width:"700px"},{default:a(()=>[t(W,{activeKey:R.value,"onUpdate:activeKey":e[20]||(e[20]=n=>R.value=n)},{default:a(()=>[t(T,{key:"manual",tab:"手动添加"},{default:a(()=>[t(q,{model:d,layout:"vertical"},{default:a(()=>[t(m,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}]},{default:a(()=>[t(w,{value:d.name,"onUpdate:value":e[13]||(e[13]=n=>d.name=n),placeholder:"请输入供应商名称"},null,8,["value"])]),_:1}),t(m,{label:"联系人",name:"contact",rules:[{required:!0,message:"请输入联系人姓名"}]},{default:a(()=>[t(w,{value:d.contact,"onUpdate:value":e[14]||(e[14]=n=>d.contact=n),placeholder:"请输入联系人姓名"},null,8,["value"])]),_:1}),t(m,{label:"联系电话",name:"phone",rules:[{required:!0,message:"请输入联系电话"}]},{default:a(()=>[t(w,{value:d.phone,"onUpdate:value":e[15]||(e[15]=n=>d.phone=n),placeholder:"请输入联系电话"},null,8,["value"])]),_:1}),t(m,{label:"电子邮箱",name:"email",rules:[{required:!0,message:"请输入电子邮箱"}]},{default:a(()=>[t(w,{value:d.email,"onUpdate:value":e[16]||(e[16]=n=>d.email=n),placeholder:"请输入电子邮箱"},null,8,["value"])]),_:1}),t(m,{label:"供应商类型",name:"type"},{default:a(()=>[t(V,{value:d.type,"onUpdate:value":e[17]||(e[17]=n=>d.type=n),placeholder:"请选择供应商类型"},{default:a(()=>[t(y,{value:"component"},{default:a(()=>e[50]||(e[50]=[i("零部件供应商")])),_:1}),t(y,{value:"material"},{default:a(()=>e[51]||(e[51]=[i("原材料供应商")])),_:1}),t(y,{value:"service"},{default:a(()=>e[52]||(e[52]=[i("服务供应商")])),_:1}),t(y,{value:"other"},{default:a(()=>e[53]||(e[53]=[i("其他供应商")])),_:1})]),_:1},8,["value"])]),_:1}),t(m,{label:"备注",name:"remark"},{default:a(()=>[t(re,{value:d.remark,"onUpdate:value":e[18]||(e[18]=n=>d.remark=n),placeholder:"请输入备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1}),t(T,{key:"excel",tab:"Excel导入"},{default:a(()=>[r("div",ot,[t(X,{fileList:h.value,"onUpdate:fileList":e[19]||(e[19]=n=>h.value=n),beforeUpload:Ue,multiple:!1,showUploadList:!0,accept:".xlsx,.xls"},{default:a(()=>[r("p",st,[t(S(te))]),e[54]||(e[54]=r("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[55]||(e[55]=r("p",{class:"ant-upload-hint"}," 支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段 ",-1))]),_:1},8,["fileList"]),r("div",{class:"template-download"},[r("a",{onClick:Ce},"下载导入模板")])])]),_:1})]),_:1},8,["activeKey"])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:E.value,"onUpdate:visible":e[28]||(e[28]=n=>E.value=n),title:"编辑供应商",onOk:Me,confirmLoading:Q.value},{default:a(()=>[t(q,{model:s,layout:"vertical"},{default:a(()=>[t(m,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}]},{default:a(()=>[t(w,{value:s.name,"onUpdate:value":e[22]||(e[22]=n=>s.name=n),placeholder:"请输入供应商名称"},null,8,["value"])]),_:1}),t(m,{label:"联系人",name:"contact",rules:[{required:!0,message:"请输入联系人姓名"}]},{default:a(()=>[t(w,{value:s.contact,"onUpdate:value":e[23]||(e[23]=n=>s.contact=n),placeholder:"请输入联系人姓名"},null,8,["value"])]),_:1}),t(m,{label:"联系电话",name:"phone",rules:[{required:!0,message:"请输入联系电话"}]},{default:a(()=>[t(w,{value:s.phone,"onUpdate:value":e[24]||(e[24]=n=>s.phone=n),placeholder:"请输入联系电话"},null,8,["value"])]),_:1}),t(m,{label:"电子邮箱",name:"email",rules:[{required:!0,message:"请输入电子邮箱"}]},{default:a(()=>[t(w,{value:s.email,"onUpdate:value":e[25]||(e[25]=n=>s.email=n),placeholder:"请输入电子邮箱"},null,8,["value"])]),_:1}),t(m,{label:"供应商类型",name:"type"},{default:a(()=>[t(V,{value:s.type,"onUpdate:value":e[26]||(e[26]=n=>s.type=n),placeholder:"请选择供应商类型"},{default:a(()=>[t(y,{value:"component"},{default:a(()=>e[56]||(e[56]=[i("零部件供应商")])),_:1}),t(y,{value:"material"},{default:a(()=>e[57]||(e[57]=[i("原材料供应商")])),_:1}),t(y,{value:"service"},{default:a(()=>e[58]||(e[58]=[i("服务供应商")])),_:1}),t(y,{value:"other"},{default:a(()=>e[59]||(e[59]=[i("其他供应商")])),_:1})]),_:1},8,["value"])]),_:1}),t(m,{label:"备注",name:"remark"},{default:a(()=>[t(re,{value:s.remark,"onUpdate:value":e[27]||(e[27]=n=>s.remark=n),placeholder:"请输入备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirmLoading"])])}}},vt=Ee(it,[["__scopeId","data-v-810c164f"]]);export{vt as default};
