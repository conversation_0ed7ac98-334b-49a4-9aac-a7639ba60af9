import{a as x,r as _,c as b,m as V,b as r,d as t,j as o,t as e,g as n,w as a,h as v,F as C,i as k,u as F,o as c,s as M,k as R}from"./index-CAFrc3c2.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";const P={class:"dashboard-container"},T={class:"metrics-section"},W={class:"metrics-grid"},j={class:"metric-card urgent-card"},E={class:"card-content"},Q={class:"card-value"},U={class:"metric-card urgent-card"},Y={class:"card-content"},G={class:"card-value"},H={class:"metric-card urgent-card"},J={class:"card-content"},K={class:"card-value"},X={class:"metric-card urgent-card"},Z={class:"card-content"},tt={class:"card-value"},st={class:"metrics-section"},it={class:"metrics-grid"},ot={class:"metric-card progress-card"},et={class:"card-content"},nt={class:"card-value"},at={class:"metric-card progress-card"},lt={class:"card-content"},dt={class:"card-value"},rt={class:"metric-card highlight-card"},ct={class:"card-content"},vt={class:"card-value highlight-value"},ut={class:"metric-card progress-card"},pt={class:"card-content"},mt={class:"card-value"},ft={class:"charts-section"},gt={class:"chart-row"},yt={class:"chart-card"},_t={class:"chart-header"},bt={class:"chart-container"},xt={class:"chart-card"},Ct={class:"chart-container"},kt={class:"chart-row"},St={class:"chart-card"},ht={class:"chart-container"},wt={class:"quick-actions-card"},At={class:"section-header"},$t={class:"action-grid"},qt={class:"bottom-section"},Bt={class:"activity-section"},Nt={class:"section-header"},Ot={class:"activity-timeline"},zt={class:"activity-content"},Dt={class:"activity-title"},Lt={class:"activity-time"},Vt={key:0,class:"activity-action"},Ft={class:"announcement-section"},Mt={class:"section-header"},Rt={class:"announcement-list"},It={class:"announcement-header"},Pt={class:"announcement-title"},Tt={class:"announcement-date"},Wt={class:"announcement-content"},jt={__name:"index",setup(Et){const S=F(),u=x({quotations:8,orders:12,deliveries:5,payments:3}),p=x({quotations:45,orders:38,amount:2856e3,deliveries:42}),f=_("6months"),h=_([{id:1,type:"urgent",title:"询价单 #RFQ-2024-001 即将截止",time:"2小时后截止",action:"立即处理"},{id:2,type:"warning",title:"送货单 #DN-2024-156 即将自动收货",time:"明天自动确认",action:"查看详情"},{id:3,type:"info",title:"对账单 #INV-2024-089 待确认",time:"3天后自动确认",action:"立即确认"},{id:4,type:"urgent",title:"付款单 #PAY-2024-045 即将逾期",time:"5天后逾期",action:"立即付款"}]),w=_([{id:1,title:"系统维护通知",date:"2024-01-15",content:"系统将于本周六凌晨2:00-4:00进行例行维护，期间可能影响部分功能使用。"},{id:2,title:"新功能上线",date:"2024-01-12",content:"批量询价功能已上线，支持一次性上传多个BOM文件进行询价。"},{id:3,title:"春节放假通知",date:"2024-01-10",content:"春节期间（2月8日-2月18日）客服响应时间可能延长，请提前安排相关事务。"}]),A=b(()=>({tooltip:{trigger:"axis",axisPointer:{type:"cross"},backgroundColor:"rgba(26, 26, 43, 0.9)",borderColor:"#f94c30",textStyle:{color:"#fff"}},legend:{data:["询价次数","采购金额(万元)"],top:10,textStyle:{color:"#666"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["8月","9月","10月","11月","12月","1月"],axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:[{type:"value",name:"询价次数",position:"left",axisLine:{lineStyle:{color:"#e0e0e0"}}},{type:"value",name:"采购金额(万元)",position:"right",axisLine:{lineStyle:{color:"#e0e0e0"}}}],series:[{name:"询价次数",type:"line",data:[35,42,38,45,52,45],smooth:!0,itemStyle:{color:"#f94c30"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(249, 76, 48, 0.3)"},{offset:1,color:"rgba(249, 76, 48, 0.05)"}]}}},{name:"采购金额(万元)",type:"line",yAxisIndex:1,data:[180,220,195,285,320,285],smooth:!0,itemStyle:{color:"#ffca46"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(255, 202, 70, 0.3)"},{offset:1,color:"rgba(255, 202, 70, 0.05)"}]}}}]})),$=b(()=>({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(26, 26, 43, 0.9)",borderColor:"#f94c30",textStyle:{color:"#fff"}},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{color:"#666"}},series:[{name:"订单状态",type:"pie",radius:["40%","70%"],center:["65%","50%"],data:[{value:15,name:"待确认"},{value:25,name:"确认中"},{value:35,name:"执行中"},{value:20,name:"已完成"},{value:5,name:"已取消"}],itemStyle:{borderRadius:5,borderColor:"#fff",borderWidth:2},color:["#f94c30","#ffca46","#1a1a2b","#52c41a","#ff7875"]}]})),q=b(()=>({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(26, 26, 43, 0.9)",borderColor:"#f94c30",textStyle:{color:"#fff"}},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{color:"#666"}},series:[{name:"物料状态",type:"pie",radius:["40%","70%"],center:["65%","50%"],data:[{value:12,name:"备货中"},{value:18,name:"部分发货"},{value:22,name:"全部发货"},{value:15,name:"部分收货"},{value:8,name:"已收货"},{value:3,name:"退货中"},{value:2,name:"已取消"}],itemStyle:{borderRadius:5,borderColor:"#fff",borderWidth:2},color:["#f94c30","#ffca46","#1a1a2b","#2a2a35","#52c41a","#ff7875","#ffc069"]}]})),B=d=>(d/1e4).toFixed(1)+"万",l=d=>{S.push(d)},N=()=>{console.log("更新趋势图:",f.value)},O=d=>{console.log("处理活动:",d)},z=()=>{console.log("配置快捷操作")};return V(()=>{console.log("Dashboard mounted")}),(d,s)=>{const g=v("a-select-option"),D=v("a-select"),y=v("v-chart"),L=v("a-icon"),m=v("a-button");return c(),r("div",P,[t("div",T,[s[19]||(s[19]=t("h3",{class:"section-title"},[t("span",{class:"title-icon"},"⚠️"),o(" 待处理事项 ")],-1)),t("div",W,[t("div",j,[s[9]||(s[9]=t("div",{class:"card-icon"},"📋",-1)),t("div",E,[s[7]||(s[7]=t("div",{class:"card-title"},"待采纳询价单",-1)),t("div",Q,e(u.quotations),1),s[8]||(s[8]=t("div",{class:"card-trend"},[t("span",{class:"trend-up"},"↗"),o(" 较昨日 +2 ")],-1))])]),t("div",U,[s[12]||(s[12]=t("div",{class:"card-icon"},"📝",-1)),t("div",Y,[s[10]||(s[10]=t("div",{class:"card-title"},"待确认订单",-1)),t("div",G,e(u.orders),1),s[11]||(s[11]=t("div",{class:"card-trend"},[t("span",{class:"trend-up"},"↗"),o(" 较昨日 +5 ")],-1))])]),t("div",H,[s[15]||(s[15]=t("div",{class:"card-icon"},"📦",-1)),t("div",J,[s[13]||(s[13]=t("div",{class:"card-title"},"待签收送货单",-1)),t("div",K,e(u.deliveries),1),s[14]||(s[14]=t("div",{class:"card-trend"},[t("span",{class:"trend-down"},"↘"),o(" 较昨日 -1 ")],-1))])]),t("div",X,[s[18]||(s[18]=t("div",{class:"card-icon"},"💳",-1)),t("div",Z,[s[16]||(s[16]=t("div",{class:"card-title"},"待完成付款",-1)),t("div",tt,e(u.payments),1),s[17]||(s[17]=t("div",{class:"card-trend"},[t("span",{class:"trend-neutral"},"→"),o(" 与昨日持平 ")],-1))])])])]),t("div",st,[s[35]||(s[35]=t("h3",{class:"section-title"},[t("span",{class:"title-icon"},"📊"),o(" 本月进展 ")],-1)),t("div",it,[t("div",ot,[s[23]||(s[23]=t("div",{class:"card-icon"},"🔍",-1)),t("div",et,[s[20]||(s[20]=t("div",{class:"card-title"},"询价次数",-1)),t("div",nt,e(p.quotations),1),s[21]||(s[21]=t("div",{class:"progress-bar"},[t("div",{class:"progress-fill",style:{width:"75%"}})],-1)),s[22]||(s[22]=t("div",{class:"progress-text"},"75% 完成目标",-1))])]),t("div",at,[s[27]||(s[27]=t("div",{class:"card-icon"},"🛒",-1)),t("div",lt,[s[24]||(s[24]=t("div",{class:"card-title"},"下单次数",-1)),t("div",dt,e(p.orders),1),s[25]||(s[25]=t("div",{class:"progress-bar"},[t("div",{class:"progress-fill",style:{width:"63%"}})],-1)),s[26]||(s[26]=t("div",{class:"progress-text"},"63% 完成目标",-1))])]),t("div",rt,[s[30]||(s[30]=t("div",{class:"card-icon"},"💰",-1)),t("div",ct,[s[28]||(s[28]=t("div",{class:"card-title"},"采购金额",-1)),t("div",vt,"¥"+e(B(p.amount)),1),s[29]||(s[29]=t("div",{class:"card-trend"},[t("span",{class:"trend-up"},"↗"),o(" 较上月 +15.6% ")],-1))])]),t("div",ut,[s[34]||(s[34]=t("div",{class:"card-icon"},"🚚",-1)),t("div",pt,[s[31]||(s[31]=t("div",{class:"card-title"},"送货单数",-1)),t("div",mt,e(p.deliveries),1),s[32]||(s[32]=t("div",{class:"progress-bar"},[t("div",{class:"progress-fill",style:{width:"84%"}})],-1)),s[33]||(s[33]=t("div",{class:"progress-text"},"84% 完成目标",-1))])])])]),t("div",ft,[t("div",gt,[t("div",yt,[t("div",_t,[s[39]||(s[39]=t("div",{class:"chart-title"},[t("span",{class:"chart-icon"},"📈"),t("h3",null,"采购趋势分析")],-1)),n(D,{value:f.value,"onUpdate:value":s[0]||(s[0]=i=>f.value=i),class:"period-selector",onChange:N},{default:a(()=>[n(g,{value:"6months"},{default:a(()=>s[36]||(s[36]=[o("近6个月")])),_:1}),n(g,{value:"12months"},{default:a(()=>s[37]||(s[37]=[o("近12个月")])),_:1}),n(g,{value:"year"},{default:a(()=>s[38]||(s[38]=[o("本年度")])),_:1})]),_:1},8,["value"])]),t("div",bt,[n(y,{option:A.value,class:"chart"},null,8,["option"])])]),t("div",xt,[s[40]||(s[40]=t("div",{class:"chart-header"},[t("div",{class:"chart-title"},[t("span",{class:"chart-icon"},"📋"),t("h3",null,"订单状态分布")])],-1)),t("div",Ct,[n(y,{option:$.value,class:"chart"},null,8,["option"])])])]),t("div",kt,[t("div",St,[s[41]||(s[41]=t("div",{class:"chart-header"},[t("div",{class:"chart-title"},[t("span",{class:"chart-icon"},"📦"),t("h3",null,"物料状态分布")])],-1)),t("div",ht,[n(y,{option:q.value,class:"chart"},null,8,["option"])])]),t("div",wt,[t("div",At,[s[43]||(s[43]=t("h3",{class:"section-title"},[t("span",{class:"title-icon"},"⚡"),o(" 快捷操作 ")],-1)),n(m,{type:"link",size:"small",onClick:z},{default:a(()=>[n(L,{type:"setting"}),s[42]||(s[42]=o(" 配置 "))]),_:1})]),t("div",$t,[t("div",{class:"action-item primary-action",onClick:s[1]||(s[1]=i=>l("/bom/index"))},s[44]||(s[44]=[t("div",{class:"action-icon"},"📤",-1),t("div",{class:"action-title"},"上传BOM询价",-1),t("div",{class:"action-desc"},"批量上传BOM文件",-1)])),t("div",{class:"action-item",onClick:s[2]||(s[2]=i=>l("/purchase/rfq"))},s[45]||(s[45]=[t("div",{class:"action-icon"},"🔍",-1),t("div",{class:"action-title"},"询价管理",-1),t("div",{class:"action-desc"},"管理询价单状态",-1)])),t("div",{class:"action-item",onClick:s[3]||(s[3]=i=>l("/purchase/po"))},s[46]||(s[46]=[t("div",{class:"action-icon"},"🛒",-1),t("div",{class:"action-title"},"采购管理",-1),t("div",{class:"action-desc"},"订单跟踪处理",-1)])),t("div",{class:"action-item",onClick:s[4]||(s[4]=i=>l("/purchase/dn"))},s[47]||(s[47]=[t("div",{class:"action-icon"},"📦",-1),t("div",{class:"action-title"},"收货管理",-1),t("div",{class:"action-desc"},"送货单签收",-1)])),t("div",{class:"action-item",onClick:s[5]||(s[5]=i=>l("/purchase/inv"))},s[48]||(s[48]=[t("div",{class:"action-icon"},"📊",-1),t("div",{class:"action-title"},"对账管理",-1),t("div",{class:"action-desc"},"发票对账确认",-1)])),t("div",{class:"action-item",onClick:s[6]||(s[6]=i=>l("/purchase/pay"))},s[49]||(s[49]=[t("div",{class:"action-icon"},"💳",-1),t("div",{class:"action-title"},"付款结算",-1),t("div",{class:"action-desc"},"付款单处理",-1)]))])])])]),t("div",qt,[t("div",Bt,[t("div",Nt,[s[51]||(s[51]=t("h3",{class:"section-title"},[t("span",{class:"title-icon"},"🔔"),o(" 近期活动动态 ")],-1)),n(m,{type:"link",size:"small"},{default:a(()=>s[50]||(s[50]=[o("查看全部")])),_:1})]),t("div",Ot,[(c(!0),r(C,null,k(h.value,i=>(c(),r("div",{key:i.id,class:M(["activity-item",i.type])},[s[52]||(s[52]=t("div",{class:"activity-dot"},null,-1)),t("div",zt,[t("div",Dt,e(i.title),1),t("div",Lt,e(i.time),1)]),i.action?(c(),r("div",Vt,[n(m,{size:"small",type:"link",onClick:Qt=>O(i)},{default:a(()=>[o(e(i.action),1)]),_:2},1032,["onClick"])])):R("",!0)],2))),128))])]),t("div",Ft,[t("div",Mt,[s[54]||(s[54]=t("h3",{class:"section-title"},[t("span",{class:"title-icon"},"📢"),o(" 系统公告 ")],-1)),n(m,{type:"link",size:"small"},{default:a(()=>s[53]||(s[53]=[o("更多公告")])),_:1})]),t("div",Rt,[(c(!0),r(C,null,k(w.value,i=>(c(),r("div",{key:i.id,class:"announcement-item"},[t("div",It,[t("span",Pt,e(i.title),1),t("span",Tt,e(i.date),1)]),t("div",Wt,e(i.content),1)]))),128))])])])])}}},Gt=I(jt,[["__scopeId","data-v-43b3c3c9"]]);export{Gt as default};
