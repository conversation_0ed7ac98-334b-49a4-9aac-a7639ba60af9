import{g as s,I as Ce,A as xe,r as y,p as Ne,a as Ae,c as k,m as De,a3 as Ie,b as w,f as x,k as N,w as i,l as n,a4 as Pe,a5 as U,M as Oe,n as r,Z as Te,j as p,t as d,d as a,s as Be,a6 as ze,a7 as W,a8 as Se,a9 as I,W as b,aa as $e,ab as Ue,ac as g,N as G,F as V,ad as Me,ae as j,af as J,ag as Le,ah as Z,i as Q,R as X,ai as K,J as Ve,u as je,o as u,aj as ee,z as Y,_ as Xe}from"./index-CAFrc3c2.js";import{B as Ye}from"./BankOutlined-BgnBn_Il.js";import{I as te}from"./InboxOutlined-DFyO9W96.js";import{_ as Ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";var He={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};function ae(v){for(var m=1;m<arguments.length;m++){var c=arguments[m]!=null?Object(arguments[m]):{},h=Object.keys(c);typeof Object.getOwnPropertySymbols=="function"&&(h=h.concat(Object.getOwnPropertySymbols(c).filter(function(C){return Object.getOwnPropertyDescriptor(c,C).enumerable}))),h.forEach(function(C){Re(v,C,c[C])})}return v}function Re(v,m,c){return m in v?Object.defineProperty(v,m,{value:c,enumerable:!0,configurable:!0,writable:!0}):v[m]=c,v}var E=function(m,c){var h=ae({},m,c.attrs);return s(Ce,ae({},h,{icon:He}),null)};E.displayName="CreditCardOutlined";E.inheritAttrs=!1;const Fe={class:"payment-detail-container"},qe={class:"payment-prompt"},We={class:"payment-countdown"},Ge=["innerHTML"],Je={style:{"font-size":"20px",color:"#f94c30","font-weight":"bold",margin:"0 4px"}},Ze={class:"payment-method-select"},Qe={key:0,class:"payment-info"},Ke={class:"payment-info-flex"},et={class:"payment-info-section"},tt={class:"payment-detail-item"},at={class:"value"},nt={class:"payment-detail-item"},st={class:"value"},ot={class:"payment-detail-item"},lt={class:"value"},it={class:"payment-detail-item"},dt={class:"value"},rt={class:"payment-detail-item"},ut={class:"value"},pt={class:"payment-detail-item"},mt={class:"value"},ct={class:"payment-info-section payment-tip"},ft={class:"tip-header"},yt={key:1,class:"payment-info"},vt={class:"buttons"},gt={class:"currency"},_t={class:"currency paid"},kt={class:"currency due"},wt=["onClick"],bt={class:"ant-upload-drag-icon"},ht={class:"comment-header"},Ct={class:"comment-user"},xt={class:"comment-time"},Nt={class:"comment-content"},At={class:"log-time"},Dt={class:"log-content"},It={class:"log-user"},Pt={key:0,class:"log-detail"},Ot={class:"upload-proof-container"},Tt={class:"ant-upload-drag-icon"},Bt={class:"proof-memo-container"},zt=xe({__name:"payDetail",setup(v){const m=Ne(),c=je(),h=y(m.params.id||m.query.id||"PAY-1001"),C=y(!1),P=y(""),H=y(!1),B=y("offline"),z=y(!1),M=y(!1),O=y(""),R=y("countdown-normal"),o=Ae({id:"PAY20250522181818",paymentNo:"PAY20250522181818",status:"partially_paid",createTime:"2025-05-15 10:00:00",paymentDate:"",dueDate:"2025-05-31",applyDate:"2024-07-16",creator:"张三",processor:"李四",paymentMethod:"现金/电汇",paymentTerms:"账期结算，30天",statementId:"INV20250522141516",statementNo:"INV20250522141516",currency:"CNY",totalAmount:15e4,paidAmount:7e4,dueAmount:8e4,supplier:{id:"SUP-001",name:"研选工场（苏州）网络有限公司",code:"SUP-001",contactPerson:"王经理",contactPhone:"13812345678",taxId:"91320594MAE5L8PD96"},materials:[{id:"mat-1",materialName:"控制板",spec:"CB-2024-V1",brand:"ABB",unit:"个",quantity:10,price:5e3,totalAmount:5e4,poNo:"PO-2024-005",poId:"PO-2024-005"},{id:"mat-2",materialName:"传感器",spec:"SN-101",brand:"Siemens",unit:"套",quantity:5,price:12e3,totalAmount:6e4,poNo:"PO-2024-005",poId:"PO-2024-005"},{id:"mat-3",materialName:"连接线缆",spec:"10m",brand:"普天",unit:"根",quantity:20,price:2e3,totalAmount:4e4,poNo:"PO-2024-008",poId:"PO-2024-008"}],relatedPOs:[{poId:"PO-2024-005",soNo:"PO-2024-005",poDate:"2024-06-10",poAmount:9e4,appliedAmount:4e4},{poId:"PO-2024-008",soNo:"PO-2024-008",poDate:"2024-06-25",poAmount:6e4,appliedAmount:3e4}],payerBankName:"中国工商银行 北京支行",payerBankAccount:"6222 **** **** 1234",payeeBankName:"招商银行股份有限公司苏州独墅湖支行",payeeBankAccount:"***************",payeeBankCode:"************",payeeBankAddress:"苏州工业园区启月街288号",paymentChannel:"招行企业银行",transactionId:"TRANS-********-987654",paymentHistory:[{id:"hist-1",time:"2024-07-16 11:00:00",action:"发起支付申请",operator:"张三",amount:15e4,success:!0,notes:"申请支付PO-2024-005和PO-2024-008部分款项"},{id:"hist-2",time:"2024-07-20 14:30:00",action:"支付成功 (部分)",operator:"李四",amount:7e4,success:!0,notes:"通过招行企业银行支付70000元"}],attachments:[{id:"att-1",name:"转账凭证_********.pdf",url:"/download/att-1",size:"1.2MB",uploadTime:"2024-07-20 15:00:00",uploadUser:"李四",type:"pdf"},{id:"att-2",name:"相关发票_INV-001.jpg",url:"/download/att-2",size:"800KB",uploadTime:"2024-07-16 10:30:00",uploadUser:"张三",type:"jpg"}],operationLogs:[{id:"log-1",time:"2024-07-15 10:00:00",userName:"张三",action:"创建付款单",detail:"关联采购单PO-2024-005, PO-2024-008"},{id:"log-2",time:"2024-07-16 09:00:00",userName:"王主管",action:"审批通过付款申请"},{id:"log-3",time:"2024-07-20 14:35:00",userName:"李四",action:"确认部分付款",detail:"实付金额 70000 CNY"},{id:"log-4",time:"2024-07-20 15:01:00",userName:"李四",action:"上传附件",detail:"付款水单_********.pdf"}],comments:[{id:"cmt-1",userName:"张三",time:"2024-07-16 10:05:00",content:"已提交付款申请，请主管审批。"},{id:"cmt-2",userName:"王主管",time:"2024-07-16 14:20:00",content:"已审批，请财务尽快安排支付。"},{id:"cmt-3",userName:"李四",time:"2024-07-20 16:00:00",content:"今日已支付7万元，剩余款项待下周处理。"}]}),ne=k(()=>o.status==="pending"||o.status==="partially_paid"||o.status==="overdue"),T=y({days:0,hours:0,minutes:0,seconds:0,isOverdue:!1}),F=()=>{if(!o.dueDate){const L=new Date;L.setDate(L.getDate()+15),o.dueDate=L.toISOString()}const t=new Date,l=new Date(o.dueDate).getTime()-t.getTime();if(l<=0){T.value={days:0,hours:0,minutes:0,seconds:0,isOverdue:!0};return}const f=Math.floor(l/(1e3*60*60*24)),_=Math.floor(l%(1e3*60*60*24)/(1e3*60*60)),be=Math.floor(l%(1e3*60*60)/(1e3*60)),he=Math.floor(l%(1e3*60)/1e3);T.value={days:f,hours:_,minutes:be,seconds:he,isOverdue:!1}},se=k(()=>{if(o.status==="overdue"||T.value.isOverdue)return"付款已逾期，请立即处理";const{days:t,hours:e,minutes:l,seconds:f}=T.value;if(t===0&&e===0&&l===0&&f===0)return"付款时间已到，请立即付款";let _="";return t>0&&(_+=`<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${t}</span>天`),(e>0||t>0)&&(_+=`<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${e.toString().padStart(2,"0")}</span>小时`),(l>0||e>0||t>0)&&(_+=`<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${l.toString().padStart(2,"0")}</span>分钟`),_+=`<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${f.toString().padStart(2,"0")}</span>秒`,`距离付款截止时间还有${_}`}),oe=k(()=>T.value.days);k(()=>o.status==="pending"||o.status==="overdue"),k(()=>o.status==="pending"||o.status==="overdue"||o.status==="partially_paid"),k(()=>o.status==="pending"||o.status==="partially_paid");const le=k(()=>!0),ie=k(()=>!0),de=[{title:"物料名称",dataIndex:"materialName",key:"materialName",width:200},{title:"型号",dataIndex:"spec",key:"spec",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:150},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,align:"right"},{title:"单价",dataIndex:"price",key:"price",width:120,align:"right"},{title:"总价",dataIndex:"totalAmount",key:"totalAmount",width:150,align:"right"},{title:"所属采购单号",dataIndex:"poNo",key:"poNo",width:150}],re=[{title:"文件名",dataIndex:"name",key:"name"},{title:"大小",dataIndex:"size",key:"size",width:100},{title:"上传时间",dataIndex:"uploadTime",key:"uploadTime",width:180,customRender:({text:t})=>S(t)},{title:"上传人",dataIndex:"uploadUser",key:"uploadUser",width:120},{title:"操作",key:"action",width:120,customRender:({record:t})=>Y(Xe,{},[Y("a",{href:t.url,target:"_blank",rel:"noopener noreferrer"},"预览"),Y("a",{href:t.url,download:t.name},"下载")])}],ue=()=>{c.go(-1)},q=async()=>{C.value=!0;try{await new Promise(t=>setTimeout(t,500)),R.value=oe.value<=3?"countdown-urgent":"countdown-normal"}catch(t){r.error("获取付款单详情失败"),console.error("Error fetching payment details:",t)}finally{C.value=!1}},pe=t=>({pending:"待支付",partially_paid:"部分支付",paid:"已支付",cancelled:"已取消",overdue:"已逾期"})[t]||"未知",me=t=>({pending:"orange",partially_paid:"blue",paid:"green",cancelled:"red",overdue:"volcano"})[t]||"default",A=t=>t==null?"-":t.toLocaleString("zh-CN",{style:"currency",currency:o.currency||"CNY"}),ce=t=>{if(!t)return"-";try{return new Date(t).toLocaleDateString("zh-CN")}catch{return t}},S=t=>{if(!t)return"-";try{const e={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1};return new Date(t).toLocaleString("zh-CN",e)}catch{return t}},fe=t=>{t&&(r.info(`跳转到对账单详情: ${t}`),c.push({name:"StatementDetail",params:{id:t}}))},ye=t=>{r.info(`跳转到采购单详情: ${t}`),c.push({name:"PoDetail",params:{id:t}})},ve=t=>{t.file.status,t.file.status==="done"?(r.success(`${t.file.name} 上传成功`),q()):t.file.status==="error"&&r.error(`${t.file.name} 上传失败.`)},ge=()=>{r.info("跳转到第三方支付平台 (待实现)")},_e=t=>{t.file.status==="uploading"&&console.log("凭证上传中:",t.file,t.fileList),t.file.status==="done"?r.success(`${t.file.name} 上传成功`):t.file.status==="error"&&r.error(`${t.file.name} 上传失败: ${t.file.error}`)},ke=async()=>{if(!O.value.trim()){r.warning("请填写付款备注信息");return}M.value=!0;try{await new Promise(t=>setTimeout(t,800)),o.operationLogs.push({id:Date.now().toString(),time:new Date().toISOString(),userName:"当前用户",action:"上传付款凭证",detail:O.value}),r.success("付款凭证提交成功"),z.value=!1,O.value=""}catch(t){r.error("提交失败，请重试"),console.error("上传凭证错误:",t)}finally{M.value=!1}},we=async()=>{if(!P.value.trim()){r.warning("请输入评论内容");return}H.value=!0;try{await new Promise(e=>setTimeout(e,300));const t={id:Date.now().toString(),userName:"当前用户",time:new Date().toISOString(),content:P.value};o.comments.push(t),P.value="",r.success("评论添加成功")}catch{r.error("添加评论失败")}finally{H.value=!1}},D=async t=>{try{await navigator.clipboard.writeText(t),r.success("已复制到剪贴板")}catch{const l=document.createElement("textarea");l.value=t,document.body.appendChild(l),l.focus(),l.select();try{document.execCommand("copy"),r.success("已复制到剪贴板")}catch{r.error("复制失败")}document.body.removeChild(l)}};let $=null;return De(()=>{q(),F(),$=setInterval(F,1e3)}),Ie(()=>{$&&(clearInterval($),$=null)}),(t,e)=>(u(),w("div",Fe,[s(n(Pe),{title:"付款单详情 - "+o.paymentNo,onBack:ue},{tags:i(()=>[s(n(Te),{color:me(o.status)},{default:i(()=>[p(d(pe(o.status)),1)]),_:1},8,["color"])]),_:1},8,["title"]),ne.value?(u(),x(n(U),{key:0,class:"detail-card payment-prompt-card"},{default:i(()=>[a("div",qe,[s(n(ze),{type:"warning","show-icon":""},{message:i(()=>[a("div",We,[e[12]||(e[12]=a("span",{class:"countdown-label"},"付款提醒：",-1)),a("span",{class:Be(R.value),innerHTML:se.value},null,10,Ge)])]),description:i(()=>[e[13]||(e[13]=p(" 请尽快完成剩余 ")),a("span",Je,d(A(o.dueAmount)),1),e[14]||(e[14]=p(" 的支付。逾期可能会影响您的信用记录及后续合作。 "))]),_:1}),a("div",Ze,[e[32]||(e[32]=a("span",{class:"method-label"},"选择支付方式：",-1)),s(n(Se),{value:B.value,"onUpdate:value":e[0]||(e[0]=l=>B.value=l),"button-style":"solid"},{default:i(()=>[s(n(W),{value:"offline"},{default:i(()=>[s(n(Ye)),e[15]||(e[15]=p(" 线下打款 "))]),_:1}),s(n(W),{value:"online"},{default:i(()=>[s(n(E)),e[16]||(e[16]=p(" 线上网银转账 "))]),_:1})]),_:1},8,["value"]),B.value==="offline"?(u(),w("div",Qe,[a("div",Ke,[a("div",et,[e[23]||(e[23]=a("p",null,[a("strong",null,"线下打款说明：")],-1)),a("div",tt,[e[17]||(e[17]=a("span",{class:"label"},"账户名称：",-1)),a("span",at,d(o.supplier.name),1),s(n(b),{type:"link",size:"small",onClick:e[1]||(e[1]=l=>D(o.supplier.name))},{default:i(()=>[s(n(I))]),_:1})]),a("div",nt,[e[18]||(e[18]=a("span",{class:"label"},"开户银行：",-1)),a("span",st,d(o.payeeBankName||"招商银行 深圳支行"),1),s(n(b),{type:"link",size:"small",onClick:e[2]||(e[2]=l=>D(o.payeeBankName||"招商银行 深圳支行"))},{default:i(()=>[s(n(I))]),_:1})]),a("div",ot,[e[19]||(e[19]=a("span",{class:"label"},"银行账号：",-1)),a("span",lt,d(o.payeeBankAccount||"6214 **** **** 5678"),1),s(n(b),{type:"link",size:"small",onClick:e[3]||(e[3]=l=>D(o.payeeBankAccount||"6214 **** **** 5678"))},{default:i(()=>[s(n(I))]),_:1})]),a("div",it,[e[20]||(e[20]=a("span",{class:"label"},"税号：",-1)),a("span",dt,d(o.supplier.taxId||"91320594MA1XFAYU2Y"),1),s(n(b),{type:"link",size:"small",onClick:e[4]||(e[4]=l=>D(o.supplier.taxId||"91320594MA1XFAYU2Y"))},{default:i(()=>[s(n(I))]),_:1})]),a("div",rt,[e[21]||(e[21]=a("span",{class:"label"},"银联号：",-1)),a("span",ut,d(o.payeeBankCode||"************"),1),s(n(b),{type:"link",size:"small",onClick:e[5]||(e[5]=l=>D(o.payeeBankCode||"************"))},{default:i(()=>[s(n(I))]),_:1})]),a("div",pt,[e[22]||(e[22]=a("span",{class:"label"},"开户行地址：",-1)),a("span",mt,d(o.payeeBankAddress||"江苏省苏州市工业园区苏州大道东278号"),1),s(n(b),{type:"link",size:"small",onClick:e[6]||(e[6]=l=>D(o.payeeBankAddress||"江苏省苏州市工业园区苏州大道东278号"))},{default:i(()=>[s(n(I))]),_:1})]),e[24]||(e[24]=a("p",{class:"note"},"*请使用公司对公账户进行转账",-1))]),a("div",ct,[a("div",ft,[s(n($e)),e[25]||(e[25]=p()),e[26]||(e[26]=a("strong",null,"注意事项",-1))]),e[27]||(e[27]=a("p",null,"请确保转账时：",-1)),e[28]||(e[28]=a("ul",null,[a("li",null,"使用公司对公账户转账"),a("li",null,"转账备注中注明付款单号"),a("li",null,"保留银行转账凭证"),a("li",null,"转账完成后及时将转账凭证作为附件上传")],-1)),e[29]||(e[29]=a("p",null,"以确保款项能及时核销入账",-1))])])])):B.value==="online"?(u(),w("div",yt,[e[30]||(e[30]=a("p",null,[a("strong",null,"线上转账将跳转到第三方支付平台")],-1)),e[31]||(e[31]=a("p",{class:"note"},"*确认金额无误后，点击下方按钮前往支付",-1)),a("div",vt,[s(n(b),{type:"primary",onClick:ge},{default:i(()=>[p("立即支付 "+d(A(o.dueAmount)),1)]),_:1})])])):N("",!0)])])]),_:1})):N("",!0),s(n(U),{title:"基本信息",class:"detail-card"},{default:i(()=>[s(n(Ue),{column:3,bordered:"",size:"middle"},{default:i(()=>[s(n(g),{label:"付款单号"},{default:i(()=>[p(d(o.paymentNo),1)]),_:1}),s(n(g),{label:"付款方式"},{default:i(()=>[p(d(o.paymentMethod),1)]),_:1}),s(n(g),{label:"付款条款"},{default:i(()=>[p(d(o.paymentTerms),1)]),_:1}),s(n(g),{label:"创建时间"},{default:i(()=>[p(d(S(o.createTime)),1)]),_:1}),s(n(g),{label:"所属对账单"},{default:i(()=>[a("a",{onClick:e[7]||(e[7]=l=>fe(o.statementId))},d(o.statementNo||"-"),1)]),_:1}),s(n(g),{label:"付款完成日期"},{default:i(()=>[p(d(ce(o.paymentDate)),1)]),_:1}),s(n(g),{label:"应付总额"},{default:i(()=>[a("span",gt,d(A(o.totalAmount)),1)]),_:1}),s(n(g),{label:"实付总额"},{default:i(()=>[a("span",_t,d(A(o.paidAmount)),1)]),_:1}),s(n(g),{label:"待付总额"},{default:i(()=>[a("span",kt,d(A(o.dueAmount)),1)]),_:1})]),_:1})]),_:1}),s(n(U),{title:"物料信息",class:"detail-card"},{default:i(()=>[s(n(G),{columns:de,"data-source":o.materials||[],pagination:!1,size:"middle","row-key":"id"},{bodyCell:i(({column:l,record:f})=>[l.dataIndex==="price"||l.dataIndex==="totalAmount"?(u(),w(V,{key:0},[p(d(A(f[l.dataIndex])),1)],64)):N("",!0),l.dataIndex==="poNo"?(u(),w("a",{key:1,onClick:_=>ye(f.poId)},d(f.poNo),9,wt)):N("",!0)]),_:1},8,["data-source"])]),_:1}),s(n(U),{title:"附件与备注",class:"detail-card"},{default:i(()=>[s(n(Me),{"default-active-key":"1"},{default:i(()=>[s(n(j),{key:"1",tab:"附件资料"},{default:i(()=>[le.value?(u(),x(n(J),{key:0,name:"file",multiple:!0,action:"/api/upload",onChange:ve},{default:i(()=>[a("p",bt,[s(n(te))]),e[33]||(e[33]=a("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[34]||(e[34]=a("p",{class:"ant-upload-hint"},"支持单个或批量上传。支持 PDF, Word, Excel, 图片等格式文件",-1))]),_:1})):N("",!0),o.attachments&&o.attachments.length>0?(u(),x(n(G),{key:1,columns:re,"data-source":o.attachments,pagination:!1,size:"small"},null,8,["data-source"])):(u(),x(n(Le),{key:2,description:"暂无附件"}))]),_:1}),s(n(j),{key:"2",tab:"沟通记录"},{default:i(()=>[s(n(Z),null,{default:i(()=>[(u(!0),w(V,null,Q(o.comments,(l,f)=>(u(),x(n(ee),{key:f},{default:i(()=>[a("div",ht,[a("span",Ct,d(l.userName),1),a("span",xt,d(S(l.time)),1)]),a("div",Nt,d(l.content),1)]),_:2},1024))),128))]),_:1}),ie.value?(u(),x(n(Ve),{key:0,layout:"inline",class:"comment-form"},{default:i(()=>[s(n(X),{style:{flex:"1"}},{default:i(()=>[s(n(K),{value:P.value,"onUpdate:value":e[8]||(e[8]=l=>P.value=l),placeholder:"添加备注...",rows:2},null,8,["value"])]),_:1}),s(n(X),null,{default:i(()=>[s(n(b),{type:"primary",onClick:we},{default:i(()=>e[35]||(e[35]=[p("发送")])),_:1})]),_:1})]),_:1})):N("",!0)]),_:1}),s(n(j),{key:"3",tab:"操作历史"},{default:i(()=>[s(n(Z),null,{default:i(()=>[(u(!0),w(V,null,Q(o.operationLogs,(l,f)=>(u(),x(n(ee),{key:f},{default:i(()=>[a("div",At,d(S(l.time)),1),a("div",Dt,[a("span",It,d(l.userName),1),a("span",null,d(l.action),1)]),l.detail?(u(),w("div",Pt,d(l.detail),1)):N("",!0)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1}),s(n(Oe),{visible:z.value,"onUpdate:visible":e[10]||(e[10]=l=>z.value=l),title:"上传付款凭证",onOk:ke,onCancel:e[11]||(e[11]=l=>z.value=!1),confirmLoading:M.value,width:"600px"},{default:i(()=>[a("div",Ot,[e[38]||(e[38]=a("p",{class:"upload-description"},"请上传银行转账回单、电汇凭证或其他可证明已完成付款的凭证",-1)),s(n(J),{name:"file",multiple:!0,action:"/api/upload-payment-attachment",data:{paymentId:h.value},onChange:_e,"list-type":"picture",maxCount:5,accept:".jpg,.jpeg,.png,.pdf,.doc,.docx"},{default:i(()=>[a("p",Tt,[s(n(te))]),e[36]||(e[36]=a("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[37]||(e[37]=a("p",{class:"ant-upload-hint"},"支持上传银行转账凭证、付款水单、电汇凭证等 (最大5个文件)",-1))]),_:1},8,["data"]),a("div",Bt,[s(n(X),{label:"付款备注",labelCol:{span:4},wrapperCol:{span:20}},{default:i(()=>[s(n(K),{value:O.value,"onUpdate:value":e[9]||(e[9]=l=>O.value=l),placeholder:"请输入付款备注说明，如：通过XX银行转账XX元，于XX日XX时完成",rows:3,maxLength:200,showCount:""},null,8,["value"])]),_:1})])])]),_:1},8,["visible","confirmLoading"])]))}}),Lt=Ee(zt,[["__scopeId","data-v-199d4dbd"]]);export{Lt as default};
