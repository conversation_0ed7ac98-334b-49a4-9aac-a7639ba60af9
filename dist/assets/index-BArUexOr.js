import{r as _,a as Z,c as K,m as Oe,ak as Ue,b as v,d as s,k as h,g as t,w as l,h as f,F as T,i as I,j as o,t as u,f as C,s as Y,n as z,o as d,y as ee,l as me,S as Ye}from"./index-CAFrc3c2.js";import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as Je}from"./StarOutlined-DP7A_Hga.js";const Ae={class:"supplier-index-container"},Fe={class:"search-filter-section"},De={class:"search-card"},Ee={class:"filters-panel"},Ke={class:"table-operations"},Pe={key:0},Me={key:0},Ve={class:"selection-summary"},qe={class:"summary-content"},Xe={key:0,class:"active-filters"},Qe={class:"main-content"},Ge={class:"right-content"},He={key:0,class:"list-view"},We={key:0,class:"supplier-name-cell"},Ze=["onClick"],ea={key:1,class:"fas fa-heart bookmark-icon",title:"已收藏"},aa={key:1,class:"card-view"},la={class:"supplier-cards-container"},ta={class:"card-content"},sa={class:"supplier-header"},na={class:"supplier-logo"},oa=["src","alt"],ia={key:1,class:"default-logo"},ra={class:"supplier-name-section"},ua={class:"supplier-name"},da={class:"supplier-tags"},ca={class:"header-actions"},pa={class:"supplier-location"},ma={class:"supplier-details"},va={class:"detail-row"},fa={class:"detail-value"},ya={class:"detail-row"},ga={class:"detail-value"},ka={class:"detail-row"},ba={class:"detail-value"},_a={class:"detail-row"},ha={class:"detail-value"},wa={class:"supplier-products"},Ca={class:"products-tags"},Sa={key:0,class:"more-products"},za={class:"card-actions"},Ta={class:"card-pagination"},Na={key:2,class:"map-view"},xa={class:"map-container"},La={class:"map-controls"},$a={key:0,class:"map-supplier-panel"},Ia={class:"map-supplier-info"},ja={key:0,class:"supplier-detail-content"},Ba={key:1},Oa=["href"],Ua={class:"detail-actions"},Ya={class:"comparison-content"},Ra={__name:"index",setup(Ja){const R=_(!1),$=_("card"),P=_("relevance"),i=Z({keyword:"",location:[],industry:[],companySize:"",establishmentYear:[],productCategories:[],businessType:[],supplierType:"",quickFilter:""}),M=_(!1),V=_(!1),g=_([]),m=_(null),S=_(null),b=Z({current:1,pageSize:12,total:0}),ve=K(()=>({current:b.current,pageSize:b.pageSize,total:b.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(a,e)=>`第 ${e[0]}-${e[1]} 条，共 ${a} 条`}));_([{key:"all",label:"全部",icon:"fas fa-list"},{key:"recommended",label:"推荐品牌商",icon:"fas fa-crown"},{key:"mechanical",label:"机械零件",icon:"fas fa-cog"},{key:"electrical",label:"电气元件",icon:"fas fa-bolt"},{key:"hydraulic",label:"液压组件",icon:"fas fa-tint"}]);const ae=_([{value:"guangdong",label:"广东省",children:[{value:"shenzhen",label:"深圳市"},{value:"guangzhou",label:"广州市"},{value:"dongguan",label:"东莞市"}]},{value:"jiangsu",label:"江苏省",children:[{value:"suzhou",label:"苏州市"},{value:"nanjing",label:"南京市"},{value:"wuxi",label:"无锡市"}]},{value:"zhejiang",label:"浙江省",children:[{value:"hangzhou",label:"杭州市"},{value:"ningbo",label:"宁波市"},{value:"wenzhou",label:"温州市"}]}]),le=_([{value:"mechanical",label:"机械制造"},{value:"electronics",label:"电子电气"},{value:"automation",label:"自动化设备"},{value:"precision",label:"精密加工"},{value:"materials",label:"新材料"},{value:"hydraulic",label:"液压气动"}]),te=_([{value:"small",label:"小型企业 (< 500万)"},{value:"medium",label:"中型企业 (500万-5000万)"},{value:"large",label:"大型企业 (> 5000万)"}]),fe=_([{title:"机械零件",value:"mechanical",key:"mechanical",children:[{title:"轴承",value:"bearing",key:"bearing"},{title:"齿轮",value:"gear",key:"gear"},{title:"联轴器",value:"coupling",key:"coupling"}]},{title:"电气元件",value:"electrical",key:"electrical",children:[{title:"传感器",value:"sensor",key:"sensor"},{title:"电机",value:"motor",key:"motor"},{title:"控制器",value:"controller",key:"controller"}]},{title:"液压组件",value:"hydraulic",key:"hydraulic",children:[{title:"液压泵",value:"pump",key:"pump"},{title:"液压缸",value:"cylinder",key:"cylinder"},{title:"液压阀",value:"valve",key:"valve"}]}]),N=_([]),q=[{id:"1",supplierId:"SUP001",companyName:"深圳精密机械有限公司",sellerId:"SELLER001",sellerNickname:"精密机械专家",shopName:"深圳精密机械旗舰店",region:"广东省深圳市",imageUrl:"https://example.com/supplier1.jpg",logoUrl:"https://via.placeholder.com/80x80/1890ff/ffffff?text=深圳精密",factoryLevel:"A级",levelScore:95,serviceYears:8,wangwangResponseScore:92,goodRatingScore:98,complianceScore:96,buyerIntention:"高",categoryName:"机械零件",homepageLink:"https://shop.example.com/supplier1",searchKeywords:JSON.stringify(["精密轴承","齿轮箱","联轴器"]),tianyanchaCompanyId:"TYC001",tianyanchaCompanyName:"深圳精密机械有限公司",registrationNumber:"91440300123456789X",registrationStatus:"存续",unifiedSocialCreditCode:"91440300123456789X",establishmentTime:14200704e5,registeredCapital:"500万人民币",companyType:"有限责任公司",organizationCode:"12345678-9",legalRepresentative:"张三",businessScope:"精密机械零件制造、销售；机械设备技术开发",registeredAddress:"深圳市南山区科技园南区",phone:"0755-12345678",email:"<EMAIL>",emailList:JSON.stringify(["<EMAIL>","<EMAIL>"]),website:"https://www.szjmjx.com",approvalDate:14200704e5,businessTermFrom:14200704e5,businessTermTo:17356608e5,socialSecurityCount:120,formerName:"",industry:"机械制造",stockName:"",stockCode:"",formerStockName:"",taxpayerIdentificationNumber:"91440300123456789X",companyAbbreviation:"深圳精密",district:"南山区",companyTags:JSON.stringify(["精密制造","高新技术","质量认证"]),supplierType:"recommended",isBookmarked:!0},{id:"2",supplierId:"SUP002",companyName:"苏州电子科技股份有限公司",sellerId:"SELLER002",sellerNickname:"电子科技先锋",shopName:"苏州电子科技专营店",region:"江苏省苏州市",imageUrl:"https://example.com/supplier2.jpg",logoUrl:"https://via.placeholder.com/80x80/52c41a/ffffff?text=苏州电子",factoryLevel:"B级",levelScore:88,serviceYears:11,wangwangResponseScore:89,goodRatingScore:94,complianceScore:91,buyerIntention:"中",categoryName:"电子元件",homepageLink:"https://shop.example.com/supplier2",searchKeywords:JSON.stringify(["传感器","控制模块","电机驱动器"]),tianyanchaCompanyId:"TYC002",tianyanchaCompanyName:"苏州电子科技股份有限公司",registrationNumber:"91320500987654321A",registrationStatus:"存续",unifiedSocialCreditCode:"91320500987654321A",establishmentTime:1325376e6,registeredCapital:"800万人民币",companyType:"股份有限公司",organizationCode:"98765432-1",legalRepresentative:"李四",businessScope:"电子产品研发、制造、销售；自动化设备技术服务",registeredAddress:"苏州市工业园区星海街",phone:"0512-87654321",email:"<EMAIL>",emailList:JSON.stringify(["<EMAIL>","<EMAIL>"]),website:"https://www.szdzkj.com",approvalDate:1325376e6,businessTermFrom:1325376e6,businessTermTo:17356608e5,socialSecurityCount:200,formerName:"苏州电子有限公司",industry:"电子电气",stockName:"",stockCode:"",formerStockName:"",taxpayerIdentificationNumber:"91320500987654321A",companyAbbreviation:"苏州电子",district:"工业园区",companyTags:JSON.stringify(["电子制造","技术创新","ISO认证"]),supplierType:"normal",isBookmarked:!1},{id:"3",supplierId:"SUP003",companyName:"上海智能制造有限公司",sellerId:"SELLER003",sellerNickname:"智能制造专家",shopName:"上海智能制造旗舰店",region:"上海市浦东新区",imageUrl:"https://example.com/supplier3.jpg",logoUrl:"",factoryLevel:"A级",levelScore:92,serviceYears:6,wangwangResponseScore:95,goodRatingScore:96,complianceScore:94,buyerIntention:"高",categoryName:"自动化设备",homepageLink:"https://shop.example.com/supplier3",searchKeywords:JSON.stringify(["工业机器人","自动化产线","智能控制系统"]),tianyanchaCompanyId:"TYC003",tianyanchaCompanyName:"上海智能制造有限公司",registrationNumber:"91310115123456789Y",registrationStatus:"存续",unifiedSocialCreditCode:"91310115123456789Y",establishmentTime:14832e8,registeredCapital:"1200万人民币",companyType:"有限责任公司",organizationCode:"12345678-0",legalRepresentative:"王五",businessScope:"智能制造设备研发、生产、销售；工业自动化技术服务",registeredAddress:"上海市浦东新区张江高科技园区",phone:"021-12345678",email:"<EMAIL>",emailList:JSON.stringify(["<EMAIL>","<EMAIL>"]),website:"https://www.shznzz.com",approvalDate:14832e8,businessTermFrom:14832e8,businessTermTo:17356608e5,socialSecurityCount:180,formerName:"",industry:"自动化设备",stockName:"",stockCode:"",formerStockName:"",taxpayerIdentificationNumber:"91310115123456789Y",companyAbbreviation:"上海智能",district:"浦东新区",companyTags:JSON.stringify(["智能制造","工业4.0","技术领先"]),supplierType:"recommended",isBookmarked:!0}],X=_([]),j=Z({currentRegion:0,recommended:0,normal:0});K(()=>{let a=0;return i.keyword&&a++,i.location.length>0&&a++,i.industry.length>0&&a++,i.companySize&&a++,i.establishmentYear.length>0&&a++,i.productCategories.length>0&&a++,i.businessType.length>0&&a++,i.auditStatus&&a++,a});const se=K(()=>{const a=[];if(i.keyword&&a.push({key:"keyword",label:`关键词: ${i.keyword}`}),i.location.length>0){const e=i.location.map(c=>{const r=ae.value.find(p=>p.value===c[0]);if(r&&c[1]){const p=r.children.find(L=>L.value===c[1]);return p?`${r.label} ${p.label}`:r.label}return r?r.label:c});a.push({key:"location",label:`地区: ${e.join(", ")}`})}if(i.industry.length>0){const e=i.industry.map(c=>{const r=le.value.find(p=>p.value===c);return r?r.label:c});a.push({key:"industry",label:`行业: ${e.join(", ")}`})}if(i.companySize){const e=te.value.find(c=>c.value===i.companySize);a.push({key:"companySize",label:`规模: ${e?e.label:i.companySize}`})}if(i.supplierType){const e=i.supplierType==="recommended"?"推荐品牌商":"普通供应商";a.push({key:"supplierType",label:`类型: ${e}`})}return a}),ye=K(()=>({selectedRowKeys:g.value,onChange:a=>{g.value=a}})),ge=_([{title:"供应商名称",dataIndex:"companyName",key:"name",width:200,fixed:"left"},{title:"所在地区",key:"location",width:120},{title:"行业类别",dataIndex:"industry",key:"industry",width:100},{title:"工厂等级",dataIndex:"factoryLevel",key:"factoryLevel",width:100},{title:"成立时间",dataIndex:"establishmentTime",key:"establishmentYear",width:100,sorter:!0},{title:"注册资本",key:"registeredCapital",width:120,sorter:!0},{title:"社保人数",dataIndex:"socialSecurityCount",key:"employeeCount",width:100,sorter:!0},{title:"服务年限",dataIndex:"serviceYears",key:"serviceYears",width:100},{title:"操作",key:"actions",width:150,fixed:"right"}]),ne=_([]),oe=_([]),B=async()=>{R.value=!0;try{await new Promise(r=>setTimeout(r,500));let a=[...q];if(i.keyword){const r=i.keyword.toLowerCase();a=a.filter(p=>p.companyName.toLowerCase().includes(r)||p.sellerNickname.toLowerCase().includes(r)||p.shopName.toLowerCase().includes(r)||JSON.parse(p.searchKeywords).some(L=>L.toLowerCase().includes(r)))}if(i.location.length>0&&(a=a.filter(r=>i.location.some(p=>p.length===1?r.region.includes(p[0]):r.region.includes(p[0])&&r.region.includes(p[1])))),i.industry.length>0&&(a=a.filter(r=>i.industry.includes(r.industry))),i.supplierType&&(a=a.filter(r=>r.supplierType===i.supplierType)),i.businessType.length>0&&(a=a.filter(r=>i.businessType.includes(r.businessType))),i.quickFilter)switch(i.quickFilter){case"recommended":a=a.filter(r=>r.supplierType==="recommended");break;case"mechanical":a=a.filter(r=>r.industry==="机械制造");break;case"electrical":a=a.filter(r=>r.industry==="电子电气");break;case"hydraulic":a=a.filter(r=>r.industry==="液压气动");break}P.value!=="relevance"&&a.sort((r,p)=>{switch(P.value){case"establishmentYear":return p.establishmentTime-r.establishmentTime;case"registeredCapital":const L=D=>{const O=D.match(/(\d+)/);return O?parseInt(O[1]):0};return L(p.registeredCapital)-L(r.registeredCapital);case"employeeCount":return p.socialSecurityCount-r.socialSecurityCount;default:return 0}}),b.total=a.length;const e=(b.current-1)*b.pageSize,c=e+b.pageSize;N.value=a.slice(e,c)}catch{z.error("搜索失败，请重试")}finally{R.value=!1}},ke=()=>{Object.keys(i).forEach(a=>{Array.isArray(i[a])?i[a]=[]:i[a]=""}),b.current=1,B()},be=a=>{switch(a){case"keyword":i.keyword="";break;case"location":i.location=[];break;case"industry":i.industry=[];break;case"companySize":i.companySize="";break;case"supplierType":i.supplierType="";break}B()},_e=(a,e)=>{b.current=a,b.pageSize=e,B()},he=(a,e,c)=>{b.current=a.current,b.pageSize=a.pageSize,c.field&&(P.value=c.field),B()},ie=a=>{const e=g.value.indexOf(a);e>-1?g.value.splice(e,1):g.value.push(a)},J=a=>{m.value=a,M.value=!0},A=async a=>{try{await new Promise(e=>setTimeout(e,300)),a.isBookmarked=!a.isBookmarked,a.isBookmarked?z.success(`已收藏 ${a.companyName}`):z.success(`已取消收藏 ${a.companyName}`)}catch{z.error("操作失败，请重试")}},we=a=>{a.target.style.display="none";const e=a.target.nextElementSibling;e&&(e.style.display="flex")},re=a=>{if(X.value.length>=3){z.warning("最多只能对比3家供应商");return}if(X.value.find(e=>e.id===a.id)){z.warning("该供应商已在对比列表中");return}X.value.push(a),z.success(`已将 ${a.name} 添加到对比列表`)},Ce=()=>{if(g.value.length<2){z.warning("至少需要选择2家供应商才能进行对比");return}if(g.value.length>3){z.warning("最多只能对比3家供应商");return}const a=N.value.filter(e=>g.value.includes(e.id));ne.value=[{title:"对比项目",dataIndex:"item",key:"item",fixed:"left",width:120},...a.map(e=>({title:e.companyName,dataIndex:e.id,key:e.id,width:200}))],oe.value=[{key:"location",item:"所在地区",...a.reduce((e,c)=>(e[c.id]=c.region,e),{})},{key:"industry",item:"行业类别",...a.reduce((e,c)=>(e[c.id]=c.industry,e),{})},{key:"establishmentYear",item:"成立时间",...a.reduce((e,c)=>(e[c.id]=`${F(c.establishmentTime)}年`,e),{})},{key:"registeredCapital",item:"注册资本",...a.reduce((e,c)=>(e[c.id]=c.registeredCapital,e),{})},{key:"employeeCount",item:"员工数量",...a.reduce((e,c)=>(e[c.id]=`${c.socialSecurityCount}人`,e),{})},{key:"supplierType",item:"供应商类型",...a.reduce((e,c)=>(e[c.id]=c.supplierType==="recommended"?"推荐品牌商":"普通供应商",e),{})},{key:"factoryLevel",item:"工厂等级",...a.reduce((e,c)=>(e[c.id]=c.factoryLevel,e),{})}],V.value=!0},Se=()=>{if(g.value.length===0){z.warning("请先选择要导出的供应商");return}const a=N.value.filter(e=>g.value.includes(e.id));z.success(`正在导出 ${a.length} 家供应商数据...`),console.log("导出数据:",a)},ze=a=>a>=1e4?`${(a/1e4).toFixed(1)}万元`:`${a.toLocaleString()}元`,F=a=>a?new Date(a).getFullYear():"";return Oe(()=>{N.value=q.slice(0,b.pageSize),b.total=q.length,$.value}),Ue($,a=>{a==="map"&&setTimeout(()=>{j.currentRegion=N.value.length,j.recommended=N.value.filter(e=>e.supplierType==="recommended").length,j.normal=N.value.filter(e=>e.supplierType==="normal").length},100)}),(a,e)=>{const c=f("a-input"),r=f("a-form-item"),p=f("a-col"),L=f("a-cascader"),D=f("a-select-option"),O=f("a-select"),Te=f("a-range-picker"),Ne=f("a-tree-select"),Q=f("a-radio"),ue=f("a-radio-group"),w=f("a-button"),U=f("a-space"),xe=f("a-row"),G=f("a-radio-button"),x=f("a-tag"),de=f("a-menu-item"),Le=f("a-menu"),$e=f("a-dropdown"),ce=f("a-table"),Ie=f("a-checkbox"),E=f("a-card"),je=f("a-pagination"),H=f("a-statistic"),k=f("a-descriptions-item"),Be=f("a-descriptions"),pe=f("a-modal");return d(),v("div",Ae,[s("div",Fe,[s("div",De,[s("div",Ee,[t(xe,{gutter:16},{default:l(()=>[t(p,{span:6},{default:l(()=>[t(r,{label:"供应商名称"},{default:l(()=>[t(c,{value:i.keyword,"onUpdate:value":e[0]||(e[0]=n=>i.keyword=n),placeholder:"请输入供应商名称","allow-clear":""},null,8,["value"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"地区筛选"},{default:l(()=>[t(L,{value:i.location,"onUpdate:value":e[1]||(e[1]=n=>i.location=n),options:ae.value,placeholder:"选择省份/城市","change-on-select":"","allow-clear":""},null,8,["value","options"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"行业类别"},{default:l(()=>[t(O,{value:i.industry,"onUpdate:value":e[2]||(e[2]=n=>i.industry=n),placeholder:"选择行业类别","allow-clear":"",mode:"multiple"},{default:l(()=>[(d(!0),v(T,null,I(le.value,n=>(d(),C(D,{key:n.value,value:n.value},{default:l(()=>[o(u(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"企业规模"},{default:l(()=>[t(O,{value:i.companySize,"onUpdate:value":e[3]||(e[3]=n=>i.companySize=n),placeholder:"按注册资本","allow-clear":""},{default:l(()=>[(d(!0),v(T,null,I(te.value,n=>(d(),C(D,{key:n.value,value:n.value},{default:l(()=>[o(u(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"成立年限"},{default:l(()=>[t(Te,{value:i.establishmentYear,"onUpdate:value":e[4]||(e[4]=n=>i.establishmentYear=n),picker:"year",placeholder:"选择年份范围"},null,8,["value"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"产品类别"},{default:l(()=>[t(Ne,{value:i.productCategories,"onUpdate:value":e[5]||(e[5]=n=>i.productCategories=n),"tree-data":fe.value,placeholder:"选择产品类别",multiple:"","tree-checkable":"","allow-clear":"","max-tag-count":2},null,8,["value","tree-data"])]),_:1})]),_:1}),t(p,{span:6},{default:l(()=>[t(r,{label:"供应商类型"},{default:l(()=>[t(ue,{value:i.supplierType,"onUpdate:value":e[6]||(e[6]=n=>i.supplierType=n)},{default:l(()=>[t(Q,{value:""},{default:l(()=>e[17]||(e[17]=[o("全部")])),_:1}),t(Q,{value:"recommended"},{default:l(()=>e[18]||(e[18]=[o("推荐品牌商")])),_:1}),t(Q,{value:"normal"},{default:l(()=>e[19]||(e[19]=[o("普通供应商")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),t(p,null,{default:l(()=>[t(U,null,{default:l(()=>[t(w,{type:"primary",onClick:B,loading:R.value},{default:l(()=>e[20]||(e[20]=[s("i",{class:"fas fa-search"},null,-1),o(" 搜索 ")])),_:1},8,["loading"]),t(w,{onClick:ke},{default:l(()=>e[21]||(e[21]=[s("i",{class:"fas fa-undo"},null,-1),o(" 重置 ")])),_:1})]),_:1})]),_:1})]),_:1})])])]),s("div",Ke,[t(U,null,{default:l(()=>[t(w,{type:"primary",onClick:Se,disabled:g.value.length===0},{default:l(()=>[e[22]||(e[22]=s("i",{class:"fas fa-download"},null,-1)),e[23]||(e[23]=o(" 导出 ")),g.value.length>0?(d(),v("span",Pe,"("+u(g.value.length)+")",1)):h("",!0)]),_:1},8,["disabled"]),t(w,{onClick:Ce,disabled:g.value.length<2||g.value.length>3},{default:l(()=>[e[24]||(e[24]=s("i",{class:"fas fa-balance-scale"},null,-1)),e[25]||(e[25]=o(" 对比 ")),g.value.length>0?(d(),v("span",Me,"("+u(g.value.length)+")",1)):h("",!0)]),_:1},8,["disabled"])]),_:1}),t(U,null,{default:l(()=>[t(ue,{value:$.value,"onUpdate:value":e[7]||(e[7]=n=>$.value=n),"button-style":"solid",size:"small"},{default:l(()=>[t(G,{value:"list"},{default:l(()=>e[26]||(e[26]=[s("i",{class:"fas fa-list"},null,-1),o(" 列表 ")])),_:1}),t(G,{value:"card"},{default:l(()=>e[27]||(e[27]=[s("i",{class:"fas fa-th-large"},null,-1),o(" 卡片 ")])),_:1}),t(G,{value:"map"},{default:l(()=>e[28]||(e[28]=[s("i",{class:"fas fa-map"},null,-1),o(" 地图 ")])),_:1})]),_:1},8,["value"])]),_:1})]),s("div",Ve,[e[31]||(e[31]=s("div",null,null,-1)),s("div",qe,[s("span",null,[e[29]||(e[29]=o("已选择：")),t(x,{color:"red"},{default:l(()=>[o(u(g.value.length),1)]),_:1}),e[30]||(e[30]=o(" 家供应商"))])])]),se.value.length>0?(d(),v("div",Xe,[e[32]||(e[32]=s("span",{class:"active-filters-label"},"当前筛选：",-1)),(d(!0),v(T,null,I(se.value,n=>(d(),C(x,{key:n.key,closable:"",onClose:y=>be(n.key),color:"#f94c30"},{default:l(()=>[o(u(n.label),1)]),_:2},1032,["onClose"]))),128))])):h("",!0),s("div",Qe,[s("div",Ge,[$.value==="list"?(d(),v("div",He,[t(ce,{columns:ge.value,"data-source":N.value,pagination:ve.value,loading:R.value,"row-selection":ye.value,"row-key":"id",onChange:he,class:"supplier-table",size:"small",bordered:""},{bodyCell:l(({column:n,record:y})=>[n.key==="name"?(d(),v("div",We,[s("a",{onClick:W=>J(y),style:{color:"#f94c30"}},u(y.companyName),9,Ze),y.supplierType==="recommended"?(d(),C(x,{key:0,color:"gold"},{default:l(()=>e[33]||(e[33]=[s("i",{class:"fas fa-crown"},null,-1),o(" 推荐品牌商 ")])),_:1})):h("",!0),y.isBookmarked?(d(),v("i",ea)):h("",!0)])):h("",!0),n.key==="location"?(d(),v(T,{key:1},[o(u(y.region),1)],64)):h("",!0),n.key==="registeredCapital"?(d(),v(T,{key:2},[o(u(y.registeredCapital),1)],64)):h("",!0),n.key==="establishmentYear"?(d(),v(T,{key:3},[o(u(F(y.establishmentTime))+"年 ",1)],64)):h("",!0),n.key==="actions"?(d(),C(U,{key:4},{default:l(()=>[t(w,{type:"link",size:"small",onClick:W=>J(y)},{default:l(()=>e[34]||(e[34]=[s("i",{class:"fas fa-eye"},null,-1),o(" 查看详情 ")])),_:2},1032,["onClick"]),t($e,null,{overlay:l(()=>[t(Le,null,{default:l(()=>[t(de,{onClick:W=>re(y)},{default:l(()=>e[36]||(e[36]=[s("i",{class:"fas fa-balance-scale"},null,-1),o(" 对比 ")])),_:2},1032,["onClick"]),t(de,{onClick:W=>A(y)},{default:l(()=>[s("i",{class:Y(y.isBookmarked?"fas fa-heart":"far fa-heart")},null,2),o(" "+u(y.isBookmarked?"取消收藏":"收藏"),1)]),_:2},1032,["onClick"])]),_:2},1024)]),default:l(()=>[t(w,{type:"link",size:"small"},{default:l(()=>e[35]||(e[35]=[o(" 更多 "),s("i",{class:"fas fa-caret-down"},null,-1)])),_:1})]),_:2},1024)]),_:2},1024)):h("",!0)]),_:1},8,["columns","data-source","pagination","loading","row-selection"])])):$.value==="card"?(d(),v("div",aa,[s("div",la,[(d(!0),v(T,null,I(N.value,n=>(d(),v("div",{key:n.id,class:"supplier-card-wrapper"},[t(E,{class:Y(["supplier-card",{selected:g.value.includes(n.id)}]),onClick:y=>ie(n.id)},{default:l(()=>[s("div",ta,[s("div",sa,[t(Ie,{checked:g.value.includes(n.id),onClick:ee(y=>ie(n.id),["stop"]),class:"card-checkbox"},null,8,["checked","onClick"]),s("div",na,[n.logoUrl?(d(),v("img",{key:0,src:n.logoUrl,alt:n.companyName,onError:we},null,40,oa)):(d(),v("div",ia,e[37]||(e[37]=[s("i",{class:"fas fa-building"},null,-1)])))]),s("div",ra,[s("div",ua,u(n.companyName),1),s("div",da,[n.supplierType==="recommended"?(d(),C(x,{key:0,color:"gold",class:"recommended-tag",size:"small"},{default:l(()=>e[38]||(e[38]=[s("i",{class:"fas fa-crown"},null,-1),o(" 推荐品牌商 ")])),_:1})):h("",!0),t(x,{color:"blue",size:"small"},{default:l(()=>[o(u(n.factoryLevel),1)]),_:2},1024)])]),s("div",ca,[t(w,{type:"text",size:"small",onClick:ee(y=>A(n),["stop"]),class:Y(["bookmark-btn",{bookmarked:n.isBookmarked}]),title:n.isBookmarked?"取消收藏":"收藏"},{default:l(()=>[n.isBookmarked?(d(),C(me(Ye),{key:1})):(d(),C(me(Je),{key:0}))]),_:2},1032,["onClick","class","title"])])]),s("div",pa,[e[39]||(e[39]=s("i",{class:"fas fa-map-marker-alt"},null,-1)),s("span",null,u(n.region),1)]),s("div",ma,[s("div",va,[e[40]||(e[40]=s("span",{class:"detail-label"},"行业类别",-1)),s("span",fa,u(n.industry),1)]),s("div",ya,[e[41]||(e[41]=s("span",{class:"detail-label"},"成立时间",-1)),s("span",ga,u(F(n.establishmentTime))+"年",1)]),s("div",ka,[e[42]||(e[42]=s("span",{class:"detail-label"},"注册资本",-1)),s("span",ba,u(n.registeredCapital),1)]),s("div",_a,[e[43]||(e[43]=s("span",{class:"detail-label"},"员工规模",-1)),s("span",ha,u(n.socialSecurityCount)+"人",1)])]),s("div",wa,[e[44]||(e[44]=s("div",{class:"products-label"},"主营产品",-1)),s("div",Ca,[(d(!0),v(T,null,I(JSON.parse(n.searchKeywords).slice(0,3),y=>(d(),C(x,{key:y,size:"small",color:"blue"},{default:l(()=>[o(u(y),1)]),_:2},1024))),128)),JSON.parse(n.searchKeywords).length>3?(d(),v("span",Sa," +"+u(JSON.parse(n.searchKeywords).length-3),1)):h("",!0)])]),s("div",za,[t(w,{type:"primary",onClick:ee(y=>J(n),["stop"]),class:"details-btn"},{default:l(()=>e[45]||(e[45]=[s("i",{class:"fas fa-eye"},null,-1),o(" 查看详情 ")])),_:2},1032,["onClick"])])])]),_:2},1032,["class","onClick"])]))),128))]),s("div",Ta,[t(je,{current:b.current,"onUpdate:current":e[8]||(e[8]=n=>b.current=n),"page-size":b.pageSize,"onUpdate:pageSize":e[9]||(e[9]=n=>b.pageSize=n),total:b.total,"show-size-changer":!0,"show-quick-jumper":!0,"show-total":(n,y)=>`第 ${y[0]}-${y[1]} 条，共 ${n} 条`,onChange:_e},null,8,["current","page-size","total","show-total"])])])):$.value==="map"?(d(),v("div",Na,[s("div",xa,[e[53]||(e[53]=s("div",{id:"supplier-map",class:"map-canvas"},null,-1)),s("div",La,[t(E,{size:"small",class:"map-legend"},{default:l(()=>e[46]||(e[46]=[s("div",{class:"legend-item"},[s("span",{class:"legend-dot recommended"}),s("span",null,"推荐品牌商")],-1),s("div",{class:"legend-item"},[s("span",{class:"legend-dot normal"}),s("span",null,"普通供应商")],-1)])),_:1}),t(E,{size:"small",class:"map-stats"},{default:l(()=>[t(H,{title:"当前区域供应商",value:j.currentRegion},null,8,["value"]),t(H,{title:"推荐品牌商",value:j.recommended},null,8,["value"]),t(H,{title:"普通供应商",value:j.normal},null,8,["value"])]),_:1})]),S.value?(d(),v("div",$a,[t(E,{size:"small"},{title:l(()=>[o(u(S.value.name)+" ",1),t(w,{type:"text",size:"small",onClick:e[10]||(e[10]=n=>S.value=null),style:{float:"right"}},{default:l(()=>e[47]||(e[47]=[s("i",{class:"fas fa-times"},null,-1)])),_:1})]),actions:l(()=>[t(w,{type:"primary",size:"small",onClick:e[11]||(e[11]=n=>J(S.value))},{default:l(()=>e[52]||(e[52]=[o(" 查看详情 ")])),_:1}),t(w,{size:"small",onClick:e[12]||(e[12]=n=>A(S.value))},{default:l(()=>[s("i",{class:Y(S.value.isBookmarked?"fas fa-heart":"far fa-heart")},null,2),o(" "+u(S.value.isBookmarked?"取消收藏":"收藏"),1)]),_:1})]),default:l(()=>[s("div",Ia,[s("p",null,[e[48]||(e[48]=s("strong",null,"地址：",-1)),o(u(S.value.address),1)]),s("p",null,[e[49]||(e[49]=s("strong",null,"行业：",-1)),o(u(S.value.industry),1)]),s("p",null,[e[50]||(e[50]=s("strong",null,"成立时间：",-1)),o(u(S.value.establishmentYear)+"年",1)]),s("p",null,[e[51]||(e[51]=s("strong",null,"注册资本：",-1)),o(u(ze(S.value.registeredCapital)),1)])])]),_:1})])):h("",!0)])])):h("",!0)])]),t(pe,{open:M.value,"onUpdate:open":e[15]||(e[15]=n=>M.value=n),title:"供应商详情",width:"800px",footer:null,class:"supplier-detail-modal"},{default:l(()=>[m.value?(d(),v("div",ja,[t(Be,{column:2,bordered:""},{default:l(()=>[t(k,{label:"企业名称"},{default:l(()=>[o(u(m.value.companyName),1)]),_:1}),t(k,{label:"供应商类型"},{default:l(()=>[m.value.supplierType==="recommended"?(d(),C(x,{key:0,color:"gold"},{default:l(()=>e[54]||(e[54]=[s("i",{class:"fas fa-crown"},null,-1),o(" 推荐品牌商 ")])),_:1})):(d(),v("span",Ba,"普通供应商"))]),_:1}),t(k,{label:"卖家昵称"},{default:l(()=>[o(u(m.value.sellerNickname),1)]),_:1}),t(k,{label:"店铺名称"},{default:l(()=>[o(u(m.value.shopName),1)]),_:1}),t(k,{label:"所在地区"},{default:l(()=>[o(u(m.value.region),1)]),_:1}),t(k,{label:"行业类别"},{default:l(()=>[o(u(m.value.industry),1)]),_:1}),t(k,{label:"工厂等级"},{default:l(()=>[o(u(m.value.factoryLevel),1)]),_:1}),t(k,{label:"等级分数"},{default:l(()=>[o(u(m.value.levelScore)+"分",1)]),_:1}),t(k,{label:"成立时间"},{default:l(()=>[o(u(F(m.value.establishmentTime))+"年",1)]),_:1}),t(k,{label:"注册资本"},{default:l(()=>[o(u(m.value.registeredCapital),1)]),_:1}),t(k,{label:"社保人数"},{default:l(()=>[o(u(m.value.socialSecurityCount)+"人",1)]),_:1}),t(k,{label:"服务年限"},{default:l(()=>[o(u(m.value.serviceYears)+"年",1)]),_:1}),t(k,{label:"联系电话"},{default:l(()=>[o(u(m.value.phone),1)]),_:1}),t(k,{label:"企业邮箱"},{default:l(()=>[o(u(m.value.email),1)]),_:1}),t(k,{label:"企业网站"},{default:l(()=>[s("a",{href:m.value.website,target:"_blank"},u(m.value.website),9,Oa)]),_:1}),t(k,{label:"法定代表人"},{default:l(()=>[o(u(m.value.legalRepresentative),1)]),_:1}),t(k,{label:"注册地址",span:2},{default:l(()=>[o(u(m.value.registeredAddress),1)]),_:1}),t(k,{label:"经营范围",span:2},{default:l(()=>[o(u(m.value.businessScope),1)]),_:1}),t(k,{label:"搜索关键词",span:2},{default:l(()=>[(d(!0),v(T,null,I(JSON.parse(m.value.searchKeywords),n=>(d(),C(x,{key:n},{default:l(()=>[o(u(n),1)]),_:2},1024))),128))]),_:1}),t(k,{label:"公司标签",span:2},{default:l(()=>[(d(!0),v(T,null,I(JSON.parse(m.value.companyTags),n=>(d(),C(x,{key:n,color:"blue"},{default:l(()=>[o(u(n),1)]),_:2},1024))),128))]),_:1})]),_:1}),s("div",Ua,[t(U,null,{default:l(()=>[t(w,{onClick:e[13]||(e[13]=n=>re(m.value))},{default:l(()=>e[55]||(e[55]=[s("i",{class:"fas fa-balance-scale"},null,-1),o(" 对比 ")])),_:1}),t(w,{onClick:e[14]||(e[14]=n=>A(m.value))},{default:l(()=>[s("i",{class:Y(m.value.isBookmarked?"fas fa-heart":"far fa-heart")},null,2),o(" "+u(m.value.isBookmarked?"取消收藏":"收藏"),1)]),_:1})]),_:1})])])):h("",!0)]),_:1},8,["open"]),t(pe,{open:V.value,"onUpdate:open":e[16]||(e[16]=n=>V.value=n),title:"供应商对比分析",width:"1000px",footer:null,class:"comparison-modal"},{default:l(()=>[s("div",Ya,[t(ce,{columns:ne.value,"data-source":oe.value,pagination:!1,bordered:""},null,8,["columns","data-source"])])]),_:1},8,["open"])])}}},Ea=Re(Ra,[["__scopeId","data-v-bb72f0a9"]]);export{Ea as default};
