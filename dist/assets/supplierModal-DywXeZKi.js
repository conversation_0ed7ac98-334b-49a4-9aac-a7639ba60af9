import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as m,f as u,w as e,g as _,d as I,h as o,b as f,i as B,j as L,t as y,F as N,k as V,o as s}from"./index-CAFrc3c2.js";const R={key:0},K={style:{margin:"0"}},M={__name:"supplierModal",props:{title:{type:String,default:"配置供应商"},visible:{type:Boolean,default:!1},confirmLoading:{type:Boolean,default:!1}},emits:["update:visible","ok","cancel","select"],setup(l,{emit:b}){const i=b,x=[{title:"供应商名称",dataIndex:"name",key:"name",width:200},{title:"供应商类型",dataIndex:"type",key:"type",width:150},{title:"经营品牌",dataIndex:"brands",key:"brands",width:200}],c=m([{id:"1",name:"京东电子",type:"贸易商",brands:"Intel, AMD, NVIDIA",expandData:{厂房产权:"自有",厂房面积:"5000平方米",在职人数:"120人",年营业额:"1500万元",代理区域范围:"华北、华东地区"}},{id:"2",name:"力创科技",type:"品牌商",brands:"Samsung, LG",expandData:{厂房产权:"租赁",厂房面积:"8000平方米",在职人数:"350人",年营业额:"6000万元"}},{id:"3",name:"联创电子",type:"加工商",brands:"Philips, Sony",expandData:{厂房产权:"自有",厂房面积:"12000平方米",在职人数:"500人",年营业额:"8000万元",核心加工能力:"PCB组装、电子元器件加工"}},{id:"4",name:"鼎盛电子",type:"加工商",brands:"Huawei, Xiaomi",expandData:{厂房产权:"自有",厂房面积:"15000平方米",在职人数:"680人",年营业额:"9500万元",核心加工能力:"芯片封装、电路板制造"}},{id:"5",name:"中科电子",type:"贸易商",brands:"Dell, HP, Lenovo",expandData:{厂房产权:"租赁",厂房面积:"3000平方米",在职人数:"85人",年营业额:"3200万元",代理区域范围:"华南、西南地区"}}]),a=m([]),g=t=>{a.value=t},h=()=>{if(a.value.length>0){const t=c.value.filter(r=>a.value.includes(r.id));i("ok",t)}else alert("请选择至少一个供应商")},v=()=>{i("update:visible",!1),i("cancel")};return(t,r)=>{const w=o("a-descriptions-item"),k=o("a-descriptions"),D=o("a-table"),C=o("a-modal");return s(),u(C,{title:l.title,width:800,visible:l.visible,"confirm-loading":l.confirmLoading,onOk:h,onCancel:v},{default:e(()=>[_(D,{columns:x,"data-source":c.value,pagination:{pageSize:5},rowKey:"id",size:"middle","row-selection":{selectedRowKeys:a.value,onChange:g},bordered:""},{bodyCell:e(({column:d,record:n})=>[d.dataIndex==="name"?(s(),f("span",R,y(n.type==="品牌商"?n.name:"严选供应商"),1)):V("",!0)]),expandedRowRender:e(({record:d})=>[I("p",K,[_(k,null,{default:e(()=>[(s(!0),f(N,null,B(d.expandData,(n,p)=>(s(),u(w,{key:p,label:p},{default:e(()=>[L(y(n),1)]),_:2},1032,["label"]))),128))]),_:2},1024)])]),_:1},8,["data-source","row-selection"])]),_:1},8,["title","visible","confirm-loading"])}}},A=S(M,[["__scopeId","data-v-b537f24c"]]);export{A as S};
