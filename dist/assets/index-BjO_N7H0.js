import{r as x,a as F,c as k,m as le,b as O,d as o,g as a,w as n,h as u,j as r,l as c,al as ie,am as re,t as R,an as de,aa as ue,ao as $,B as E,k as y,f as w,F as ce,i as pe,ag as me,M as I,n as T,u as fe,o as g,s as ge,y as _e,ap as ve}from"./index-CAFrc3c2.js";import{_ as he}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as Re}from"./BellOutlined-DOPcL6jX.js";const ke={class:"notification-container"},ye={class:"search-area"},De={class:"action-area"},Ce={class:"stats-info"},Se={class:"stat-item"},Ne={class:"stat-item"},Oe={class:"stat-item"},we={class:"action-buttons"},Te={class:"message-list"},ze={class:"message-wrapper"},Ie=["onClick"],Me={class:"message-header"},Ue={class:"message-info"},be={class:"message-title"},xe={class:"message-time"},Fe={class:"message-content-actions"},$e={class:"message-content"},Ee=["innerHTML"],qe={key:0,class:"pagination-wrapper"},Be={__name:"index",setup(Qe){const q=fe(),D=x(!1),p=x([]),l=F({module:void 0,readStatus:void 0,dateRange:[]}),i=F({current:1,pageSize:15,total:0}),M=k(()=>p.value.filter(t=>!t.isRead).length),U=k(()=>p.value.filter(t=>t.isRead).length),B=k(()=>p.value.length),z=k(()=>{let t=[...p.value];if(l.module&&(t=t.filter(e=>e.module===l.module)),l.readStatus){const e=l.readStatus==="read";t=t.filter(d=>d.isRead===e)}if(l.dateRange&&l.dateRange.length===2){const[e,d]=l.dateRange;t=t.filter(f=>{const m=new Date(f.createTime);return m>=e&&m<=d})}return t.sort((e,d)=>new Date(d.createTime)-new Date(e.createTime)),i.total=t.length,t}),Q=k(()=>{const t=(i.current-1)*i.pageSize,e=t+i.pageSize;return z.value.slice(t,e)}),A=[{id:1,module:"rfq",title:"询价单已截止",content:"您发起的询价单<strong>RFQ-2024-001</strong>已截止。",createTime:"2024-01-15 14:30:00",isRead:!1,actionUrl:"/purchase/rfq/detail/RFQ-2024-001",relatedId:"RFQ-2024-001"},{id:2,module:"purchase",title:"采购订单合同已生成",content:"您发起的采购订单<strong>SO-2024-002</strong>合同已生成。",createTime:"2024-01-15 10:15:00",isRead:!1,actionUrl:"/purchase/poDetail?soNo=SO-2024-002",relatedId:"SO-2024-002"},{id:3,module:"purchase",title:"采购订单已确认",content:"您发起的采购订单<strong>SO-2024-003</strong>已确认。",createTime:"2024-01-14 16:45:00",isRead:!0,actionUrl:"/purchase/poDetail?soNo=SO-2024-003",relatedId:"SO-2024-003"},{id:4,module:"delivery",title:"新的送货单生成",content:"您有一份新的送货单<strong>DN-2024-004</strong>。",createTime:"2024-01-14 09:20:00",isRead:!1,actionUrl:"/purchase/dnDetail?dnNo=DN-2024-004",relatedId:"DN-2024-004"},{id:5,module:"delivery",title:"送货单即将自动收货",content:"您的送货单<strong>DN-2024-005</strong>即将自动收货，请及时处理。",createTime:"2024-01-13 18:30:00",isRead:!1,actionUrl:"/purchase/dnDetail?dnNo=DN-2024-005",relatedId:"DN-2024-005"},{id:6,module:"delivery",title:"送货单已自动收货",content:"您的送货单<strong>DN-2024-006</strong>已自动收货。",createTime:"2024-01-13 12:00:00",isRead:!0,actionUrl:"/purchase/dnDetail?dnNo=DN-2024-006",relatedId:"DN-2024-006"},{id:7,module:"purchase",title:"采购订单已完成",content:"您发起的采购订单<strong>SO-2024-007</strong>已完成。",createTime:"2024-01-12 15:20:00",isRead:!0,actionUrl:"/purchase/poDetail?soNo=SO-2024-007",relatedId:"SO-2024-007"},{id:8,module:"purchase",title:"采购订单已取消",content:"您发起的采购订单<strong>SO-2024-008</strong>已取消。",createTime:"2024-01-12 11:10:00",isRead:!0,actionUrl:"/purchase/poDetail?soNo=SO-2024-008",relatedId:"SO-2024-008"},{id:9,module:"rfq",title:"询价单已过期",content:"您发起的询价单<strong>RFQ-2024-009</strong>已过期。",createTime:"2024-01-11 09:30:00",isRead:!1,actionUrl:"/purchase/rfq/detail/RFQ-2024-009",relatedId:"RFQ-2024-009"},{id:10,module:"delivery",title:"送货异常处理",content:"您的送货单<strong>DN-2024-010</strong>运输异常，请联系供应商。",createTime:"2024-01-10 14:45:00",isRead:!1,actionUrl:"/purchase/dnDetail?dnNo=DN-2024-010",relatedId:"DN-2024-010"},{id:11,module:"purchase",title:"采购订单部分确认",content:"您发起的采购订单<strong>SO-2024-011</strong>已部分确认。",createTime:"2024-01-10 11:20:00",isRead:!0,actionUrl:"/purchase/poDetail?soNo=SO-2024-011",relatedId:"SO-2024-011"},{id:12,module:"delivery",title:"收货确认通知",content:"您的送货单<strong>DN-2024-012</strong>已完成收货确认。",createTime:"2024-01-09 16:10:00",isRead:!0,actionUrl:"/purchase/dnDetail?dnNo=DN-2024-012",relatedId:"DN-2024-012"},{id:13,module:"delivery",title:"系统维护通知",content:"系统将于今晚22:00-23:00进行维护，期间可能影响正常使用。",createTime:"2024-01-08 15:30:00",isRead:!1,relatedId:null}],L=t=>({rfq:"询价管理",purchase:"采购管理",delivery:"收货管理"})[t]||"未知",V=t=>({rfq:"blue",purchase:"orange",delivery:"green"})[t]||"default",P=t=>{const e=new Date,d=new Date(t),f=e-d,m=Math.floor(f/(1e3*60)),_=Math.floor(f/(1e3*60*60)),S=Math.floor(f/(1e3*60*60*24));return m<1?"刚刚":m<60?`${m}分钟前`:_<24?`${_}小时前`:S<7?`${S}天前`:t.split(" ")[0]},C=()=>{i.current=1},Y=()=>{l.module=void 0,l.readStatus=void 0,l.dateRange=[],i.current=1},j=t=>{t.isRead||b(t)},b=t=>{t.isRead=!0,T.success("已标记为已读")},H=()=>{I.confirm({title:"确认操作",content:"确定要将所有未读消息标记为已读吗？",onOk(){p.value.forEach(t=>{t.isRead||(t.isRead=!0)}),T.success("所有消息已标记为已读")}})},G=()=>{I.confirm({title:"确认操作",content:"确定要清空所有已读消息吗？此操作不可恢复。",onOk(){p.value=p.value.filter(t=>!t.isRead),T.success("已清空所有已读消息"),i.current=1}})},J=t=>{I.confirm({title:"确认删除",content:"确定要删除这条消息吗？",onOk(){const e=p.value.findIndex(d=>d.id===t.id);e>-1&&(p.value.splice(e,1),T.success("消息已删除"))}})},K=t=>{t.actionUrl&&q.push(t.actionUrl)},W=(t,e)=>{i.current=t,i.pageSize=e},X=(t,e)=>{i.current=1,i.pageSize=e};return le(()=>{D.value=!0,setTimeout(()=>{p.value=[...A],D.value=!1},500)}),(t,e)=>{const d=u("a-select-option"),f=u("a-select"),m=u("a-form-item"),_=u("a-col"),S=u("a-range-picker"),v=u("a-button"),N=u("a-space"),Z=u("a-row"),ee=u("a-form"),te=u("a-tag"),ae=u("a-badge"),ne=u("a-empty"),se=u("a-spin"),oe=u("a-pagination");return g(),O("div",ke,[o("div",ye,[a(ee,{model:l},{default:n(()=>[a(Z,{gutter:16},{default:n(()=>[a(_,{span:4},{default:n(()=>[a(m,{label:"消息类型"},{default:n(()=>[a(f,{value:l.module,"onUpdate:value":e[0]||(e[0]=s=>l.module=s),placeholder:"请选择消息类型",style:{width:"100%"},allowClear:"",onChange:C},{default:n(()=>[a(d,{value:"rfq"},{default:n(()=>e[6]||(e[6]=[r("询价管理")])),_:1}),a(d,{value:"purchase"},{default:n(()=>e[7]||(e[7]=[r("采购管理")])),_:1}),a(d,{value:"delivery"},{default:n(()=>e[8]||(e[8]=[r("收货管理")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),a(_,{span:4},{default:n(()=>[a(m,{label:"阅读状态"},{default:n(()=>[a(f,{value:l.readStatus,"onUpdate:value":e[1]||(e[1]=s=>l.readStatus=s),placeholder:"请选择阅读状态",style:{width:"100%"},allowClear:"",onChange:C},{default:n(()=>[a(d,{value:"unread"},{default:n(()=>e[9]||(e[9]=[r("未读")])),_:1}),a(d,{value:"read"},{default:n(()=>e[10]||(e[10]=[r("已读")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),a(_,{span:10},{default:n(()=>[a(m,{label:"时间范围"},{default:n(()=>[a(S,{value:l.dateRange,"onUpdate:value":e[2]||(e[2]=s=>l.dateRange=s),format:"YYYY-MM-DD",style:{width:"100%"},onChange:C},null,8,["value"])]),_:1})]),_:1}),a(_,{span:6},{default:n(()=>[a(N,null,{default:n(()=>[a(v,{type:"primary",onClick:C},{default:n(()=>[a(c(ie)),e[11]||(e[11]=r(" 查询 "))]),_:1}),a(v,{onClick:Y},{default:n(()=>[a(c(re)),e[12]||(e[12]=r(" 重置 "))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),o("div",De,[o("div",Ce,[a(N,{size:"large"},{default:n(()=>[o("span",Se,[a(c(Re),{style:{color:"#ff4d4f","margin-right":"4px"}}),e[13]||(e[13]=r(" 未读: ")),o("strong",null,R(M.value),1)]),o("span",Ne,[a(c(de),{style:{color:"#52c41a","margin-right":"4px"}}),e[14]||(e[14]=r(" 已读: ")),o("strong",null,R(U.value),1)]),o("span",Oe,[a(c(ue),{style:{color:"#1890ff","margin-right":"4px"}}),e[15]||(e[15]=r(" 总计: ")),o("strong",null,R(B.value),1)])]),_:1})]),o("div",we,[a(N,null,{default:n(()=>[a(v,{onClick:H,type:"primary",disabled:M.value===0,size:"small"},{default:n(()=>[a(c($)),e[16]||(e[16]=r(" 全部已读 "))]),_:1},8,["disabled"]),a(v,{onClick:G,type:"primary",disabled:U.value===0,size:"small"},{default:n(()=>[a(c(E)),e[17]||(e[17]=r(" 清空已读 "))]),_:1},8,["disabled"])]),_:1})])]),o("div",Te,[a(se,{spinning:D.value},{default:n(()=>[o("div",ze,[(g(!0),O(ce,null,pe(Q.value,s=>(g(),O("div",{key:s.id,class:ge(["message-item",{unread:!s.isRead}]),onClick:h=>j(s)},[o("div",Me,[o("div",Ue,[a(te,{color:V(s.module),size:"small"},{default:n(()=>[r(R(L(s.module)),1)]),_:2},1032,["color"]),o("span",be,R(s.title),1),s.isRead?y("",!0):(g(),w(ae,{key:0,status:"processing"}))]),o("div",xe,R(P(s.createTime)),1)]),o("div",Fe,[o("div",$e,[o("div",{class:"message-text",innerHTML:s.content},null,8,Ee)]),o("div",{class:"message-actions",onClick:e[3]||(e[3]=_e(()=>{},["stop"]))},[a(N,{size:"small"},{default:n(()=>[s.actionUrl?(g(),w(v,{key:0,type:"link",size:"small",onClick:h=>K(s)},{default:n(()=>[a(c(ve)),e[18]||(e[18]=r(" 查看 "))]),_:2},1032,["onClick"])):y("",!0),s.isRead?y("",!0):(g(),w(v,{key:1,type:"link",size:"small",onClick:h=>b(s)},{default:n(()=>[a(c($)),e[19]||(e[19]=r(" 已读 "))]),_:2},1032,["onClick"])),a(v,{type:"link",size:"small",danger:"",onClick:h=>J(s)},{default:n(()=>[a(c(E)),e[20]||(e[20]=r(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])])],10,Ie))),128)),z.value.length===0&&!D.value?(g(),w(ne,{key:0,description:"暂无消息",image:c(me).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):y("",!0)])]),_:1},8,["spinning"]),z.value.length>0?(g(),O("div",qe,[a(oe,{current:i.current,"onUpdate:current":e[4]||(e[4]=s=>i.current=s),"page-size":i.pageSize,"onUpdate:pageSize":e[5]||(e[5]=s=>i.pageSize=s),total:i.total,"show-size-changer":!0,"show-quick-jumper":!0,"show-total":(s,h)=>`第 ${h[0]}-${h[1]} 条，共 ${s} 条`,onChange:W,onShowSizeChange:X},null,8,["current","page-size","total","show-total"])])):y("",!0)])])}}},Pe=he(Be,[["__scopeId","data-v-2a5f34ef"]]);export{Pe as default};
