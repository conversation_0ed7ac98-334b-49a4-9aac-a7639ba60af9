import{_ as ge}from"./logo-7sOJmgiz.js";import{r as k,m as be,b as R,e as ke,d as a,g as t,w as l,h as u,n as g,p as we,u as he,o as y,j as d,t as p,k as E,l as qe,D as Pe,f as w,F as Ce,i as xe}from"./index-CAFrc3c2.js";import{_ as Ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Me={class:"temp-quote-page"},Re={class:"main-content"},De={class:"rfq-summary"},Ue={class:"summary-header"},Ie={class:"buyer-info"},Se={class:"buyer-details"},je={class:"rfq-status"},ze={class:"deadline-info"},Ae={class:"deadline-value"},Be={class:"summary-item"},Te={class:"value"},Fe={class:"summary-item"},Ve={class:"value"},$e={class:"summary-item"},Ee={class:"value"},Qe={class:"summary-item"},Le={class:"value"},Oe={class:"quote-form-section"},Je={class:"supplier-info-section"},Ke={class:"materials-quote-section"},Ge={class:"batch-operations",style:{"margin-bottom":"16px"}},He={key:0,style:{"margin-left":"8px",color:"#1890ff"}},We={class:"material-table"},Xe={key:1,class:"material-name-cell"},Ye={class:"main-text"},Ze={class:"form-actions"},et={style:{"text-align":"center",padding:"20px 0"}},tt={style:{padding:"20px 0"}},at={style:{"margin-bottom":"16px"}},lt={style:{"margin-bottom":"24px"}},st={style:{"text-align":"right"}},nt={class:"supplier-footer"},ot={class:"footer-content"},rt={class:"footer-section"},it={class:"footer-section"},ut={__name:"temp-quote",setup(dt){const Q=we(),A=he(),B=k(),N=k(!1),C=k(!1),x=k(!1),v=k([]),M=k([]),q=k({remark:""}),L=["暂时无货","不做该产品","价格不合适","交期无法满足","规格不匹配","产能不足"],T=k({name:"大疆科技",fullName:"深圳市大疆科技有限公司"}),m=k({id:"",rfqNo:"",status:"inProgress",deadline:"",creator:"",contactPhone:"",materialModelCount:0,materials:[]}),s=k({supplierType:"",companyName:"",contactName:"",contactPhone:"",mainBrands:"",mainProducts:"",materials:[]}),O={supplierType:[{required:!0,message:"请选择供应商类型",trigger:"change"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur"}],contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],mainBrands:[{required:!0,message:"请输入主营品牌",trigger:"blur"}],mainProducts:[{required:!0,message:"请输入主营产品分类",trigger:"blur"}]},J=[{title:"",key:"selection",width:50,fixed:"left"},{title:"物料名称",dataIndex:"name",key:"materialName",width:120,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:100},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80,align:"center"},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100,align:"center"},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100,align:"center"},{title:"备注",dataIndex:"remark",key:"remark",width:120,customRender:({text:n})=>n||"无"},{title:"单价 (¥)",key:"price",width:120,align:"center"},{title:"总价 (¥)",key:"totalPrice",width:120,align:"center"},{title:"承诺交期（天）",key:"promisedDelivery",width:130,align:"center"},{title:"平替型号",key:"alternativeModel",width:120,align:"center"},{title:"报价备注",key:"quoteRemark",width:120,align:"center"},{title:"报价状态",key:"quoteStatus",width:100,align:"center"}],K=n=>n||"",G=n=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[n]||n,H=n=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[n]||"default",W=()=>m.value.materials.reduce((n,e)=>n+e.quantity,0),X=n=>{const e=s.value.materials[n],r=m.value.materials[n];e!=null&&e.price&&(r!=null&&r.quantity)&&(e.totalPrice=e.price*r.quantity)},Y=(n,e)=>{if(e)v.value.push(n.id),M.value.push(n);else{const r=v.value.indexOf(n.id);r>-1&&(v.value.splice(r,1),M.value.splice(r,1))}},Z=()=>{A.back()},ee=()=>{if(v.value.length===0){g.warning("请先选择要拒绝的物料");return}q.value.remark="",x.value=!0},te=()=>{if(v.value.length===0){g.warning("请先选择要重置的物料");return}v.value.forEach(n=>{const e=m.value.materials.findIndex(r=>r.id===n);e!==-1&&(s.value.materials[e]={price:null,totalPrice:null,promisedDelivery:null,remark:"",alternativeModel:"",status:"quoting"})}),v.value=[],M.value=[],g.success("已重置选中物料的报价信息")},ae=n=>{q.value.remark=n},le=()=>{if(!q.value.remark.trim()){g.warning("请填写拒绝原因");return}v.value.forEach(n=>{const e=m.value.materials.findIndex(r=>r.id===n);e!==-1&&(s.value.materials[e].status="rejected",s.value.materials[e].remark=q.value.remark,s.value.materials[e].price=null,s.value.materials[e].totalPrice=null,s.value.materials[e].promisedDelivery=null,s.value.materials[e].alternativeModel="")}),v.value=[],M.value=[],x.value=!1,g.success("已拒绝选中的物料")},se=async()=>{try{if(await B.value.validateFields(),!s.value.materials.some(e=>(e==null?void 0:e.price)>0&&(e==null?void 0:e.promisedDelivery))){g.warning("请至少为一个物料填写完整的报价信息（单价、承诺交期）");return}C.value=!0}catch(n){console.error("表单验证失败:",n),g.error("请检查填写的信息是否完整")}},ne=async()=>{try{N.value=!0,C.value=!1,await new Promise(n=>setTimeout(n,2e3)),g.success("报价提交成功！买方将收到您的报价信息。"),setTimeout(()=>{A.push("/supplier/quote-success")},1e3)}catch{g.error("提交失败，请重试")}finally{N.value=!1}},oe=()=>{try{const n=Q.query.param;if(!n)throw new Error("无效的分享链接");return JSON.parse(atob(n))}catch{return g.error("分享链接格式错误"),null}},re=async()=>{try{const n=oe();if(!n)return;await new Promise(e=>setTimeout(e,500)),m.value={id:"1",rfqNo:n.rfqNo||"RFQ-2023-0001",status:"inProgress",deadline:"2023-10-25 18:00:00",creator:"张三",contactPhone:"13800138000",materialModelCount:4,materials:Array.from({length:4}).map((e,r)=>({id:`material-${r}`,name:`电子元件 ${r+1}`,model:`MODEL-${100+r}`,brand:r%3===0?"品牌A":r%3===1?"品牌B":"品牌C",quantity:Math.floor(Math.random()*100+10),expectedDelivery:"30天",status:"inProgress",remark:r%2===0?"紧急采购":"",acceptAlternative:r%3===0}))},s.value.materials=m.value.materials.map(()=>({price:null,totalPrice:null,promisedDelivery:null,remark:"",alternativeModel:"",status:"quoting"}))}catch{g.error("获取询价单详情失败")}},ie=()=>{window.open("http://prototype.yanxuan.icu/www/supplier","_blank")},ue=()=>{window.open("http://prototype.yanxuan.icu/www/supplier","_blank")};return be(()=>{re()}),(n,e)=>{const r=u("a-avatar"),D=u("a-tag"),U=u("a-divider"),c=u("a-col"),I=u("a-row"),F=u("a-card"),S=u("a-select-option"),de=u("a-select"),_=u("a-form-item"),h=u("a-input"),V=u("a-menu-item"),me=u("a-menu"),b=u("a-button"),pe=u("a-dropdown"),ve=u("a-checkbox"),j=u("a-input-number"),ce=u("a-table"),fe=u("a-form"),z=u("a-space"),$=u("a-modal"),ye=u("a-textarea");return y(),R("div",Me,[e[46]||(e[46]=ke('<div class="page-header" data-v-f13acb0c><div class="header-content" data-v-f13acb0c><div class="logo-section" data-v-f13acb0c><img src="'+ge+'" alt="研选工场" class="logo-image" data-v-f13acb0c><span class="page-title" data-v-f13acb0c>报价单</span></div><div class="header-info" data-v-f13acb0c></div></div></div>',1)),a("div",Re,[a("div",De,[t(F,null,{default:l(()=>[a("div",Ue,[a("div",Ie,[t(r,{size:"large",style:{"background-color":"#1890ff"}},{default:l(()=>[d(p(T.value.name.charAt(0)),1)]),_:1}),a("div",Se,[a("h3",null,p(T.value.fullName),1),a("p",null,"询价单号："+p(m.value.rfqNo),1)])]),a("div",je,[t(D,{color:H(m.value.status)},{default:l(()=>[d(p(G(m.value.status)),1)]),_:1},8,["color"]),a("div",ze,[e[11]||(e[11]=a("span",{class:"deadline-label"},"截止时间：",-1)),a("span",Ae,p(K(m.value.deadline)),1)])])]),t(U),t(I,{gutter:24},{default:l(()=>[t(c,{span:6},{default:l(()=>[a("div",Be,[e[12]||(e[12]=a("span",{class:"label"},"物料种类",-1)),a("span",Te,p(m.value.materialModelCount)+" 种",1)])]),_:1}),t(c,{span:6},{default:l(()=>[a("div",Fe,[e[13]||(e[13]=a("span",{class:"label"},"总数量",-1)),a("span",Ve,p(W())+" 件",1)])]),_:1}),t(c,{span:6},{default:l(()=>[a("div",$e,[e[14]||(e[14]=a("span",{class:"label"},"联系人",-1)),a("span",Ee,p(m.value.creator),1)])]),_:1}),t(c,{span:6},{default:l(()=>[a("div",Qe,[e[15]||(e[15]=a("span",{class:"label"},"联系电话",-1)),a("span",Le,p(m.value.contactPhone),1)])]),_:1})]),_:1})]),_:1})]),a("div",Oe,[t(F,{title:"供应商信息"},{default:l(()=>[t(fe,{ref_key:"quoteFormRef",ref:B,model:s.value,rules:O,layout:"vertical",onFinish:se},{default:l(()=>[a("div",Je,[e[19]||(e[19]=a("h3",{style:{"margin-bottom":"16px",color:"#262626"}},"供应商信息",-1)),t(I,{gutter:24},{default:l(()=>[t(c,{span:8},{default:l(()=>[t(_,{label:"供应商类型",name:"supplierType",required:""},{default:l(()=>[t(de,{value:s.value.supplierType,"onUpdate:value":e[0]||(e[0]=o=>s.value.supplierType=o),placeholder:"请选择供应商类型"},{default:l(()=>[t(S,{value:"brand"},{default:l(()=>e[16]||(e[16]=[d("品牌商")])),_:1}),t(S,{value:"trader"},{default:l(()=>e[17]||(e[17]=[d("贸易商")])),_:1}),t(S,{value:"processor"},{default:l(()=>e[18]||(e[18]=[d("加工商")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(_,{label:"企业名称",name:"companyName",required:""},{default:l(()=>[t(h,{value:s.value.companyName,"onUpdate:value":e[1]||(e[1]=o=>s.value.companyName=o),placeholder:"请输入企业全称"},null,8,["value"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(_,{label:"联系人姓名",name:"contactName",required:""},{default:l(()=>[t(h,{value:s.value.contactName,"onUpdate:value":e[2]||(e[2]=o=>s.value.contactName=o),placeholder:"请输入联系人姓名"},null,8,["value"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(_,{label:"联系电话",name:"contactPhone",required:""},{default:l(()=>[t(h,{value:s.value.contactPhone,"onUpdate:value":e[3]||(e[3]=o=>s.value.contactPhone=o),placeholder:"请输入联系电话"},null,8,["value"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(_,{label:"主营品牌",name:"mainBrands",required:""},{default:l(()=>[t(h,{value:s.value.mainBrands,"onUpdate:value":e[4]||(e[4]=o=>s.value.mainBrands=o),placeholder:"请输入主营品牌（多个用逗号分隔）"},null,8,["value"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(_,{label:"主营产品分类",name:"mainProducts",required:""},{default:l(()=>[t(h,{value:s.value.mainProducts,"onUpdate:value":e[5]||(e[5]=o=>s.value.mainProducts=o),placeholder:"请输入主营产品分类（多个用逗号分隔）"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),t(U),a("div",Ke,[e[23]||(e[23]=a("div",{class:"section-header"},[a("h3",{style:{"margin-bottom":"16px",color:"#262626"}},"物料报价")],-1)),a("div",Ge,[t(pe,{disabled:v.value.length===0},{overlay:l(()=>[t(me,null,{default:l(()=>[t(V,{key:"reject",onClick:ee},{default:l(()=>e[20]||(e[20]=[d(" 拒绝 ")])),_:1}),t(V,{key:"reset",onClick:te},{default:l(()=>e[21]||(e[21]=[d(" 重置 ")])),_:1})]),_:1})]),default:l(()=>[t(b,null,{default:l(()=>[e[22]||(e[22]=d(" 批量操作 ")),t(qe(Pe))]),_:1})]),_:1},8,["disabled"]),v.value.length>0?(y(),R("span",He," 已选择 "+p(v.value.length)+" 项 ",1)):E("",!0)]),a("div",We,[t(ce,{dataSource:m.value.materials,columns:J,pagination:!1,scroll:{x:1600},bordered:"",rowKey:"id",size:"small"},{bodyCell:l(({column:o,text:_e,record:P,index:i})=>[o.key==="selection"?(y(),w(ve,{key:0,checked:v.value.includes(P.id),onChange:f=>Y(P,f.target.checked)},null,8,["checked","onChange"])):o.key==="materialName"?(y(),R("div",Xe,[a("div",Ye,p(P.name),1)])):o.key==="acceptAlternative"?(y(),w(D,{key:2,color:P.acceptAlternative?"green":"orange"},{default:l(()=>[d(p(P.acceptAlternative?"是":"否"),1)]),_:2},1032,["color"])):o.key==="price"?(y(),w(_,{key:3,name:["materials",i,"price"],style:{margin:"0"}},{default:l(()=>[t(j,{value:s.value.materials[i].price,"onUpdate:value":f=>s.value.materials[i].price=f,placeholder:"请输入",precision:2,min:0,style:{width:"100%"},size:"small",disabled:s.value.materials[i].status==="rejected",onChange:f=>X(i)},null,8,["value","onUpdate:value","disabled","onChange"])]),_:2},1032,["name"])):o.key==="totalPrice"?(y(),w(_,{key:4,name:["materials",i,"totalPrice"],style:{margin:"0"}},{default:l(()=>[t(j,{value:s.value.materials[i].totalPrice,"onUpdate:value":f=>s.value.materials[i].totalPrice=f,placeholder:"自动计算",precision:2,min:0,style:{width:"100%"},size:"small",disabled:""},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])):o.key==="promisedDelivery"?(y(),w(_,{key:5,name:["materials",i,"promisedDelivery"],style:{margin:"0"}},{default:l(()=>[t(j,{value:s.value.materials[i].promisedDelivery,"onUpdate:value":f=>s.value.materials[i].promisedDelivery=f,placeholder:"请输入天数",min:1,style:{width:"100%"},size:"small",disabled:s.value.materials[i].status==="rejected"},null,8,["value","onUpdate:value","disabled"])]),_:2},1032,["name"])):o.key==="alternativeModel"?(y(),w(_,{key:6,name:["materials",i,"alternativeModel"],style:{margin:"0"}},{default:l(()=>[t(h,{value:s.value.materials[i].alternativeModel,"onUpdate:value":f=>s.value.materials[i].alternativeModel=f,placeholder:"请输入平替型号",size:"small",disabled:!P.acceptAlternative||s.value.materials[i].status==="rejected"},null,8,["value","onUpdate:value","disabled"])]),_:2},1032,["name"])):o.key==="quoteRemark"?(y(),w(_,{key:7,name:["materials",i,"remark"],style:{margin:"0"}},{default:l(()=>[t(h,{value:s.value.materials[i].remark,"onUpdate:value":f=>s.value.materials[i].remark=f,placeholder:"可选",size:"small",disabled:s.value.materials[i].status==="rejected"},null,8,["value","onUpdate:value","disabled"])]),_:2},1032,["name"])):o.key==="quoteStatus"?(y(),w(D,{key:8,color:s.value.materials[i].status==="rejected"?"red":"blue"},{default:l(()=>[d(p(s.value.materials[i].status==="rejected"?"已拒绝":"报价中"),1)]),_:2},1032,["color"])):E("",!0)]),_:1},8,["dataSource"])])]),a("div",Ze,[t(b,{type:"primary",size:"large","html-type":"submit",loading:N.value},{default:l(()=>e[24]||(e[24]=[d(" 提交报价 ")])),_:1},8,["loading"]),t(b,{size:"large",onClick:Z},{default:l(()=>e[25]||(e[25]=[d(" 取消 ")])),_:1})])]),_:1},8,["model"])]),_:1})])]),t($,{open:C.value,"onUpdate:open":e[7]||(e[7]=o=>C.value=o),title:"确认提交",footer:null,width:"400px"},{default:l(()=>[a("div",et,[e[28]||(e[28]=a("div",{style:{"font-size":"48px",color:"#faad14","margin-bottom":"16px"}},"⚠️",-1)),e[29]||(e[29]=a("p",{style:{"font-size":"16px","margin-bottom":"24px"}}," 报价提交之后不可修改，是否继续？ ",-1)),t(z,null,{default:l(()=>[t(b,{onClick:e[6]||(e[6]=o=>C.value=!1)},{default:l(()=>e[26]||(e[26]=[d("取消")])),_:1}),t(b,{type:"primary",onClick:ne,loading:N.value},{default:l(()=>e[27]||(e[27]=[d(" 确认提交 ")])),_:1},8,["loading"])]),_:1})])]),_:1},8,["open"]),t($,{open:x.value,"onUpdate:open":e[10]||(e[10]=o=>x.value=o),title:"拒绝报价",footer:null,width:"500px"},{default:l(()=>[a("div",tt,[e[34]||(e[34]=a("p",{style:{"margin-bottom":"16px"}},"请选择拒绝原因或填写备注：",-1)),a("div",at,[e[30]||(e[30]=a("h4",{style:{"margin-bottom":"8px"}},"常见原因：",-1)),t(z,{wrap:""},{default:l(()=>[(y(),R(Ce,null,xe(L,o=>t(b,{key:o,size:"small",onClick:_e=>ae(o)},{default:l(()=>[d(p(o),1)]),_:2},1032,["onClick"])),64))]),_:1})]),a("div",lt,[e[31]||(e[31]=a("h4",{style:{"margin-bottom":"8px"}},"备注：",-1)),t(ye,{value:q.value.remark,"onUpdate:value":e[8]||(e[8]=o=>q.value.remark=o),placeholder:"请输入拒绝原因",rows:3},null,8,["value"])]),a("div",st,[t(z,null,{default:l(()=>[t(b,{onClick:e[9]||(e[9]=o=>x.value=!1)},{default:l(()=>e[32]||(e[32]=[d("取消")])),_:1}),t(b,{type:"primary",onClick:le},{default:l(()=>e[33]||(e[33]=[d(" 确认拒绝 ")])),_:1})]),_:1})])])]),_:1},8,["open"]),a("div",nt,[a("div",ot,[t(I,{gutter:48},{default:l(()=>[t(c,{span:8},{default:l(()=>[a("div",rt,[e[36]||(e[36]=a("h3",null,"成为正式供应商",-1)),e[37]||(e[37]=a("p",null,"加入研选工场供应商生态圈",-1)),e[38]||(e[38]=a("p",null,"享受AI智能匹配、数据分析、渠道拓展等专业服务",-1)),t(b,{type:"link",onClick:ie,style:{"padding-left":"0"}},{default:l(()=>e[35]||(e[35]=[d(" 立即入驻 → ")])),_:1})])]),_:1}),t(c,{span:8},{default:l(()=>e[39]||(e[39]=[a("div",{class:"footer-section"},[a("h4",null,"平台优势"),a("ul",null,[a("li",null,"AI智能供需匹配，匹配精度85%"),a("li",null,"数据智能分析，业绩提升40%"),a("li",null,"多渠道销售拓展，增长300%"),a("li",null,"1000+合作伙伴生态")])],-1)])),_:1}),t(c,{span:8},{default:l(()=>[a("div",it,[e[41]||(e[41]=a("h4",null,"供应商服务",-1)),e[42]||(e[42]=a("p",null,"咨询热线：400-888-9999",-1)),e[43]||(e[43]=a("p",null,"邮箱：<EMAIL>",-1)),e[44]||(e[44]=a("p",null,"快速入驻，专属客户经理服务",-1)),t(b,{size:"small",onClick:ue},{default:l(()=>e[40]||(e[40]=[d("了解更多")])),_:1})])]),_:1})]),_:1}),t(U),e[45]||(e[45]=a("div",{class:"footer-bottom"},[a("p",null,"© 2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号"),a("p",null,"专业的工业品采购平台 · 智能化供应链解决方案")],-1))])])])}}},ct=Ne(ut,[["__scopeId","data-v-f13acb0c"]]);export{ct as default};
