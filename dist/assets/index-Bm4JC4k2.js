import{S as Ie}from"./supplierModal-DywXeZKi.js";import{_ as he}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as v,c as ye,a as ke,h as r,b as x,o as p,d as u,g as t,w as a,j as o,l as ce,D as Ce,G as we,t as _,f as A,k as f,F as Se,i as qe,u as Pe,n as xe}from"./index-CAFrc3c2.js";import{S as Te}from"./SettingOutlined-CbvaVSGf.js";const $e={class:"material-table"},Me={class:"table-operations"},De={class:"selection-summary"},Re={style:{color:"#666"}},Oe={class:"summary-content"},Ne=["onClick"],Fe=["onClick"],Be=["onClick"],Ae=["onClick"],Ue=["onClick"],je={style:{margin:"12px"}},Ve={__name:"materialTable",props:{searchForm:{type:Object,default:()=>({})}},emits:["search","reset"],setup(be,{expose:I,emit:m}){const J=v(!1),H=()=>{J.value=!J.value},w=[{title:"商品名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100,customRender:({text:l})=>l?"是":"否"},{title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120,customRender:({text:l,record:e})=>e.acceptAlternative&&l?l:"-"},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120,customRender:({text:l,record:e})=>e.acceptAlternative&&l?l:"-"},{title:"单价 (¥)",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:l,record:e})=>e.selectedSupplier?`¥${e.selectedSupplier.price.toFixed(2)}`:""},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:l,record:e})=>e.selectedSupplier?`¥${e.selectedSupplier.totalPrice.toFixed(2)}`:""},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:100,customRender:({text:l,record:e})=>e.selectedSupplier?e.selectedSupplier.minOrderQuantity:""},{title:"交期",dataIndex:"delivery",key:"delivery",width:100,customRender:({text:l,record:e})=>e.selectedSupplier?e.selectedSupplier.promisedDelivery:""},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"询价单号",dataIndex:"rfqNo",key:"rfqNo",width:150,customRender:({text:l,record:e})=>e.status==="notStarted"?"-":l},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:150,customRender:({text:l,record:e})=>e.status==="notStarted"?"-":l},{title:"结束时间",dataIndex:"endTime",key:"endTime",width:150,customRender:({text:l,record:e})=>e.status==="inProgress"?"-":l},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:150},{title:"询价状态",dataIndex:"status",key:"status",width:100},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:275}],S=v(["name","model","brand","quantity","acceptAlternative","alternativeBrand","alternativeModel","unitPrice","totalPrice","expectedDelivery","rfqNo","rfqTime","deadline","status","action"]),M=ye(()=>w.filter(l=>S.value.includes(l.dataIndex)||l.fixed)),h=l=>{S.value=l},ne=l=>{const e=[];return e.push({title:"供应商",dataIndex:"supplierName",key:"supplierName",width:150,customRender:({text:s,record:d})=>s||d.name||"-"}),l&&e.push({title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s||"-"},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s||"-"}),e.push({title:"报价 (¥)",dataIndex:"price",key:"price",width:100,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s?`¥${s.toFixed(2)}`:""},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s?`¥${s.toFixed(2)}`:""},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:150},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:120,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:120,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150,customRender:({text:s,record:d})=>d.status==="pending"||d.status==="rejected"?"-":s},{title:"报价状态",dataIndex:"status",key:"status",width:100},{title:"报价类型",dataIndex:"quoteType",key:"quoteType",width:100,customRender:({text:s})=>s==="platform"?"平台报价":"外部报价"},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作",dataIndex:"action",key:"action",width:100}),e},T=v([]),U=v(!1),Q=ke({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),P=v([]),R=l=>{P.value=l},c=ye(()=>parseFloat(T.value.filter(l=>P.value.includes(l.id)).reduce((l,e)=>{const s=e.selectedSupplier?e.selectedSupplier.totalPrice:e.totalPrice||0;return l+s},0).toFixed(2))),j=(l,e)=>{if(e){e.suppliers.forEach(d=>{d.isSelected=!1});const s=e.suppliers.find(d=>d.id===l.id);s&&(s.isSelected=!0,e.selectedSupplier=s,e.acceptAlternative&&s.alternativeBrand&&s.alternativeModel&&(e.alternativeBrand=s.alternativeBrand,e.alternativeModel=s.alternativeModel))}},O=l=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[l]||"default",D=l=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[l]||"未知",le=l=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[l]||"default",oe=l=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[l]||"未知",ie=l=>l==="notStarted",W=l=>l==="notStarted",X=l=>l==="accepted",L=l=>["accepted","expired","invalid","cancelled"].includes(l),E=l=>l==="inProgress",re=l=>["notStarted","expired","invalid","cancelled"].includes(l),Z=l=>{Q.current=l.current,Q.pageSize=l.pageSize,N()},N=()=>{U.value=!0,setTimeout(()=>{T.value=Array.from({length:10}).map((l,e)=>{const s=Math.floor(Math.random()*100+10),d=Math.floor(Math.random()*1e3+100),K=e%6===0?"notStarted":e%6===1?"inProgress":e%6===2?"accepted":e%6===3?"expired":e%6===4?"invalid":"cancelled",$=Array.from({length:3}).map((B,C)=>{const ue=C%4===0?"pending":C%4===1?"quoted":C%4===2?"rejected":"expired",me=Math.floor(Math.random()*1e3+100),ve=C===1,fe=["兼容品牌A","兼容品牌B","通用品牌C"],_e=["ALT-001","COMP-002","GEN-003"],b=C%2===0?"platform":"external",g={platform:["严选供应商","深圳市电子科技有限公司","北京智能制造有限公司"],external:["上海精密器件有限公司","广州电子元件供应商","天津工业设备公司"]};return{id:`supplier-${e}-${C}`,name:`供应商 ${C+1}`,supplierName:g[b][C%g[b].length],price:me,totalPrice:me*s,minOrderQuantity:Math.floor(Math.random()*100+10),promisedDelivery:`${Math.floor(Math.random()*30+15)}天`,validityPeriod:"2023-07-05",quoteTime:"2023-06-05",status:ue,remark:C%2===0?"含税价格":"",isSelected:!1,quoteType:b,alternativeBrand:ve&&ue==="quoted"?fe[C%fe.length]:null,alternativeModel:ve&&ue==="quoted"?_e[C%_e.length]:null}});let y=null,F=null,G=null;if(K==="accepted"){const B=$.find(C=>C.status==="quoted");B&&(B.isSelected=!0,y=B,e%3===0&&B.alternativeBrand&&B.alternativeModel&&(F=B.alternativeBrand,G=B.alternativeModel))}return{id:`rfq-${e}`,name:`测试商品 ${e+1}`,model:`MODEL-${100+e}`,brand:e%3===0?"A":e%3===1?"B":"C",quantity:s,unitPrice:d,totalPrice:s*d,expectedDelivery:"30天",rfqNo:K==="notStarted"?"":`RFQ-2023-${1e3+e}`,rfqTime:K==="notStarted"?"":"2023-06-01",endTime:"2023-06-15",deadline:"2023-06-10",status:K,remark:e%2===0?"紧急采购":"",suppliers:$,selectedSupplier:y,acceptAlternative:e%3===0,alternativeBrand:F,alternativeModel:G}}),Q.total=100,U.value=!1},500)},V=l=>{console.log("删除询价单",l)},z=v(!1),ee=v(!1),i=v(null),n=l=>{i.value=l,z.value=!0},k=l=>{console.log("启动询价",l)},Y=l=>{console.log("转采购单",l)},te=l=>{console.log("再次询价",l)},se=l=>{console.log("取消",l)},ae=l=>{i.value&&l&&l.length>0&&(l.forEach(e=>{if(i.value.suppliers.findIndex(d=>d.id===e.id)===-1){const d={id:e.id,name:e.name,supplierName:e.name,price:null,totalPrice:null,minOrderQuantity:1,promisedDelivery:"",validityPeriod:"",quoteTime:"",status:"pending",remark:"",isSelected:!1,quoteType:"platform"};i.value.suppliers.push(d)}}),z.value=!1,alert(`已成功添加${l.length}个供应商`))},de=()=>{z.value=!1},pe=({key:l})=>{if(P.value.length===0)return alert("请先选择要操作的询价单");const e=T.value.filter(s=>P.value.includes(s.id));switch(l){case"startInquiry":console.log("批量启动询价",e);break;case"toPurchaseOrder":console.log("批量转采购单",e);break;case"cancel":console.log("批量取消",e);break;case"delete":console.log("批量删除",e);break}},ge=()=>{if(P.value.length===0)return alert("请先选择要分配的询价单");const l=T.value.filter(e=>P.value.includes(e.id));console.log("智能分配供应商",l)};return I({fetchData:N,selectedRowKeys:P,selectedTotalPrice:c}),N(),(l,e)=>{const s=r("a-button"),d=r("a-menu-item"),K=r("a-menu"),$=r("a-dropdown"),y=r("a-space"),F=r("a-tooltip"),G=r("a-tag"),B=r("a-popconfirm"),C=r("a-table"),ue=r("a-checkbox"),me=r("a-col"),ve=r("a-row"),fe=r("a-checkbox-group"),_e=r("a-drawer");return p(),x("div",$e,[u("div",Me,[t(y,null,{default:a(()=>[t($,null,{overlay:a(()=>[t(K,{onClick:pe},{default:a(()=>[t(d,{key:"startInquiry"},{default:a(()=>e[3]||(e[3]=[o("启动询价")])),_:1}),t(d,{key:"toPurchaseOrder"},{default:a(()=>e[4]||(e[4]=[o("转采购单")])),_:1}),t(d,{key:"cancel"},{default:a(()=>e[5]||(e[5]=[o("取消")])),_:1}),t(d,{key:"delete"},{default:a(()=>e[6]||(e[6]=[o("删除")])),_:1})]),_:1})]),default:a(()=>[t(s,{type:"primary"},{default:a(()=>[e[2]||(e[2]=o(" 批量操作 ")),t(ce(Ce))]),_:1})]),_:1}),t(s,{type:"primary",class:"smart-allocation-btn",onClick:ge},{default:a(()=>e[7]||(e[7]=[o("智能分配")])),_:1})]),_:1}),t(s,{onClick:H},{default:a(()=>[t(ce(Te)),e[8]||(e[8]=o(" 列设置"))]),_:1})]),u("div",De,[u("div",null,[t(F,{placement:"top"},{title:a(()=>e[9]||(e[9]=[u("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),u("div",null,[o("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),u("br"),o("订单总金额¥0.00 - ¥499.99，运费¥15.00"),u("br"),o("订单总金额¥500.00 - ¥999.99，运费¥8.00"),u("br"),o("订单总金额¥1000以上，免运费")],-1)])),default:a(()=>[u("span",Re,[t(ce(we),{style:{"margin-right":"4px"}}),e[10]||(e[10]=o("价格与运费说明"))])]),_:1})]),u("div",Oe,[u("span",null,[e[11]||(e[11]=o("已选择：")),t(G,{color:"red"},{default:a(()=>[o(_(P.value.length),1)]),_:1}),e[12]||(e[12]=o(" 个询价单"))]),u("span",null,[e[13]||(e[13]=o("总金额：")),t(G,{color:"red"},{default:a(()=>[o("¥"+_(c.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),t(C,{columns:M.value,"data-source":T.value,size:"middle",loading:U.value,pagination:Q,onChange:Z,"row-key":"id","row-selection":{selectedRowKeys:P.value,onChange:R},bordered:"",scroll:{x:1500}},{bodyCell:a(({column:b,record:g})=>[b.dataIndex==="action"?(p(),A(y,{key:0},{default:a(()=>[ie(g.status)?(p(),x("a",{key:0,onClick:q=>n(g)},"配置供应商",8,Ne)):f("",!0),W(g.status)?(p(),x("a",{key:1,onClick:q=>k(g)},"启动询价",8,Fe)):f("",!0),X(g.status)?(p(),x("a",{key:2,onClick:q=>Y(g)},"转采购单",8,Be)):f("",!0),L(g.status)?(p(),x("a",{key:3,onClick:q=>te(g)},"再次询价",8,Ae)):f("",!0),E(g.status)?(p(),x("a",{key:4,onClick:q=>se(g),class:"danger-link"},"取消",8,Ue)):f("",!0),re(g.status)?(p(),A(B,{key:5,title:"确定要删除此询价单吗?","ok-text":"确定","cancel-text":"取消",onConfirm:q=>V(g)},{default:a(()=>e[14]||(e[14]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):f("",!0),e[15]||(e[15]=u("a",null,"询价历史",-1))]),_:2},1024)):f("",!0),b.dataIndex==="status"?(p(),A(G,{key:1,color:O(g.status)},{default:a(()=>[o(_(D(g.status)),1)]),_:2},1032,["color"])):f("",!0)]),expandedRowRender:a(({record:b})=>[u("div",je,[t(C,{size:"small",columns:ne(b.acceptAlternative),"data-source":b.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:a(({column:g,record:q})=>[g.dataIndex==="status"?(p(),A(G,{key:0,color:le(q.status)},{default:a(()=>[o(_(oe(q.status)),1)]),_:2},1032,["color"])):f("",!0),g.dataIndex==="action"?(p(),A(s,{key:1,type:"link",disabled:q.isSelected||q.status==="pending"||q.status==="rejected"||q.quoteType==="external",onClick:vt=>j(q,b)},{default:a(()=>[o(_(q.isSelected?"已选择":"选择"),1)]),_:2},1032,["type","disabled","onClick"])):f("",!0)]),_:2},1032,["columns","data-source"])])]),_:1},8,["columns","data-source","loading","pagination","row-selection"]),t(_e,{title:"配置表格列",placement:"right",visible:J.value,onClose:H,width:"400px"},{default:a(()=>[t(fe,{value:S.value,"onUpdate:value":e[0]||(e[0]=b=>S.value=b),onChange:h},{default:a(()=>[t(ve,null,{default:a(()=>[(p(),x(Se,null,qe(w,b=>t(me,{span:12,key:b.dataIndex},{default:a(()=>[t(ue,{value:b.dataIndex,disabled:b.fixed},{default:a(()=>[o(_(b.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"]),t(Ie,{title:"配置供应商",visible:z.value,"confirm-loading":ee.value,"onUpdate:visible":e[1]||(e[1]=b=>z.value=b),onOk:ae,onCancel:de},null,8,["visible","confirm-loading"])])}}},ze=he(Ve,[["__scopeId","data-v-35c4adfe"]]),Qe={class:"order-table"},Le={class:"table-operations"},Ee={class:"selection-summary"},Ye={style:{color:"#666"}},Ke={class:"summary-content"},Je=["onClick"],Ge={key:1},He={key:2},We={key:3,style:{"font-weight":"bold",color:"#f5222d"}},Xe=["onClick"],Ze=["onClick"],et=["onClick"],tt=["onClick"],at={class:"share-content"},nt={class:"share-text"},lt={class:"share-actions"},ot={__name:"orderTable",props:{searchForm:{type:Object,default:()=>({})}},emits:["viewDetail"],setup(be,{expose:I,emit:m}){const J=Pe(),H=[{title:"询价单号",dataIndex:"rfqNo",key:"rfqNo",width:160,fixed:"left"},{title:"物料型号数",dataIndex:"materialModelCount",key:"materialModelCount",width:110,align:"center"},{title:"物料总数",dataIndex:"materialCount",key:"materialCount",width:100,align:"center"},{title:"物料总价",dataIndex:"totalPrice",key:"totalPrice",width:140,align:"right"},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:120},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:120},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:200}],w=v([]),S=v(!1),M=ke({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),h=v([]),ne=i=>{h.value=i},T=ye(()=>parseFloat(w.value.filter(i=>h.value.includes(i.rfqNo)).reduce((i,n)=>i+n.totalPrice,0).toFixed(2))),U=i=>i==="accepted",Q=i=>["accepted","deadline","expired","cancelled"].includes(i),P=i=>i==="inProgress",R=i=>["deadline","expired","cancelled"].includes(i),c=i=>{M.current=i.current,M.pageSize=i.pageSize,j()},j=()=>{S.value=!0,setTimeout(()=>{const i=[],n=["inProgress","accepted","deadline","expired","cancelled"];for(let k=0;k<15;k++){const Y=n[k%n.length],te=Math.floor(Math.random()*8)+2,se=Math.floor(Math.random()*5)+1,de=(Math.floor(Math.random()*5e3)+1e3)*te;i.push({rfqNo:`RFQ202300${1e3+k}`,materialCount:te,materialModelCount:se,totalPrice:de,rfqTime:"2023-06-0"+(k%9+1),deadline:"2023-06-"+(10+k%20),status:Y,materials:[]})}w.value=i,M.total=50,S.value=!1},500)},O=i=>{J.push(`/workspace/purchase/rfq/detail/${i.rfqNo}`)},D=i=>{console.log("转采购单",i)},le=i=>{console.log("再次询价",i)},oe=i=>{console.log("取消询价单",i)},ie=i=>{console.log("删除询价单",i),j()},W=({key:i})=>{if(h.value.length===0)return alert("请先选择要操作的询价单");const n=w.value.filter(k=>h.value.includes(k.rfqNo));switch(i){case"toPurchaseOrder":console.log("批量转采购单",n);break;case"reInquiry":console.log("批量再次询价",n);break;case"cancel":console.log("批量取消",n);break;case"delete":console.log("批量删除",n);break}},X=()=>{if(h.value.length===0)return alert("请先选择要转换的询价单");const n=w.value.filter(k=>h.value.includes(k.rfqNo)).filter(k=>U(k.status));if(n.length===0)return alert("选中的询价单中没有可转换的记录（只有已采纳状态的询价单可以转换）");console.log("批量转采购单",n)},L=v(!1),E=v(null),re=v({name:"张三"}),Z=v({fullName:"深圳市研选科技有限公司"}),N=ye(()=>{if(!E.value)return"";const i="http://prototype.yanxuan.icu/bomai/workSpace/company-space/ans-price",n=btoa(JSON.stringify({rfqNo:E.value.rfqNo,timestamp:Date.now()}));return`${i}?param=${n}`}),V=i=>{E.value=i,L.value=!0},z=async()=>{try{await navigator.clipboard.writeText(N.value),xe.success("链接已复制到剪贴板")}catch{const n=document.createElement("textarea");n.value=N.value,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),xe.success("链接已复制到剪贴板")}},ee=()=>{L.value=!1,E.value=null};return I({fetchData:j,selectedRowKeys:h,selectedTotalPrice:T}),j(),(i,n)=>{const k=r("a-button"),Y=r("a-menu-item"),te=r("a-menu"),se=r("a-dropdown"),ae=r("a-space"),de=r("a-tooltip"),pe=r("a-tag"),ge=r("a-popconfirm"),l=r("a-table"),e=r("a-typography-paragraph"),s=r("a-typography-text"),d=r("a-alert"),K=r("a-modal");return p(),x("div",Qe,[u("div",Le,[t(ae,null,{default:a(()=>[t(se,null,{overlay:a(()=>[t(te,{onClick:W},{default:a(()=>[t(Y,{key:"toPurchaseOrder"},{default:a(()=>n[2]||(n[2]=[o("转采购单")])),_:1}),t(Y,{key:"reInquiry"},{default:a(()=>n[3]||(n[3]=[o("再次询价")])),_:1}),t(Y,{key:"cancel"},{default:a(()=>n[4]||(n[4]=[o("取消")])),_:1}),t(Y,{key:"delete"},{default:a(()=>n[5]||(n[5]=[o("删除")])),_:1})]),_:1})]),default:a(()=>[t(k,{type:"primary"},{default:a(()=>[n[1]||(n[1]=o(" 批量操作 ")),t(ce(Ce))]),_:1})]),_:1}),t(k,{type:"primary",onClick:X},{default:a(()=>n[6]||(n[6]=[o("批量转采购单")])),_:1})]),_:1})]),u("div",Ee,[u("div",null,[t(de,{placement:"top"},{title:a(()=>n[7]||(n[7]=[u("div",null,"1. 物料总价为各询价单内已选择供应商的物料价格汇总。",-1),u("div",null,"2. 询价单状态包括：未开始、询价中、已采纳、已过期、已失效、已取消。",-1)])),default:a(()=>[u("span",Ye,[t(ce(we),{style:{"margin-right":"4px"}}),n[8]||(n[8]=o("询价单说明"))])]),_:1})]),u("div",Ke,[u("span",null,[n[9]||(n[9]=o("已选择：")),t(pe,{color:"red"},{default:a(()=>[o(_(h.value.length),1)]),_:1}),n[10]||(n[10]=o(" 个询价单"))]),u("span",null,[n[11]||(n[11]=o("总金额：")),t(pe,{color:"red"},{default:a(()=>[o("¥"+_(T.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),t(l,{columns:H,"data-source":w.value,size:"middle",loading:S.value,pagination:M,onChange:c,"row-key":"rfqNo","row-selection":{selectedRowKeys:h.value,onChange:ne},bordered:""},{bodyCell:a(({column:$,record:y})=>[$.dataIndex==="rfqNo"?(p(),x("a",{key:0,onClick:F=>O(y),style:{color:"#1890ff"}},_(y.rfqNo),9,Je)):f("",!0),$.dataIndex==="materialCount"?(p(),x("span",Ge,_(y.materialCount),1)):f("",!0),$.dataIndex==="materialModelCount"?(p(),x("span",He,_(y.materialModelCount),1)):f("",!0),$.dataIndex==="totalPrice"?(p(),x("span",We,"¥"+_(y.totalPrice.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)):f("",!0),$.dataIndex==="action"?(p(),A(ae,{key:4},{default:a(()=>[U(y.status)?(p(),x("a",{key:0,onClick:F=>D(y)},"转采购单",8,Xe)):f("",!0),Q(y.status)?(p(),x("a",{key:1,onClick:F=>le(y)},"再次询价",8,Ze)):f("",!0),P(y.status)?(p(),x("a",{key:2,onClick:F=>oe(y),class:"danger-link"},"取消",8,et)):f("",!0),R(y.status)?(p(),A(ge,{key:3,title:"确定要删除此询价单吗?","ok-text":"确定","cancel-text":"取消",onConfirm:F=>ie(y)},{default:a(()=>n[12]||(n[12]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):f("",!0),u("a",{onClick:F=>V(y)},"分享",8,tt)]),_:2},1024)):f("",!0)]),_:1},8,["data-source","loading","pagination","row-selection"]),t(K,{visible:L.value,"onUpdate:visible":n[0]||(n[0]=$=>L.value=$),title:"分享询价单",width:"600px",footer:null,onCancel:ee},{default:a(()=>[u("div",at,[u("div",nt,[t(e,null,{default:a(()=>[u("strong",null,_(re.value.name),1),n[13]||(n[13]=o("邀请您加入研选工场，并查看")),u("strong",null,_(Z.value.fullName),1),n[14]||(n[14]=o("的询价单。 "))]),_:1}),t(e,null,{default:a(()=>[n[15]||(n[15]=o(" 点击链接关注最新报价: ")),t(s,{copyable:"","copy-config":{text:N.value},type:"success"},{default:a(()=>[o(_(N.value),1)]),_:1},8,["copy-config"])]),_:1})]),t(d,{message:"本链接为公开链接，任何研选工场会员均可查看报价，请谨慎处理。",type:"warning","show-icon":""}),u("div",lt,[t(ae,null,{default:a(()=>[t(k,{type:"primary",onClick:z},{default:a(()=>n[16]||(n[16]=[o("复制链接")])),_:1}),t(k,{onClick:ee},{default:a(()=>n[17]||(n[17]=[o("关闭")])),_:1})]),_:1})])])]),_:1},8,["visible"])])}}},it=he(ot,[["__scopeId","data-v-2f38db7e"]]),rt={class:"rfq-container"},st={class:"search-area"},dt={class:"table-area"},ut={class:"view-switch"},ct={class:"order-detail"},pt={style:{"margin-top":"20px"}},mt={__name:"index",setup(be){const I=v("material"),m=ke({model:"",brand:void 0,category:void 0,rfqNo:"",rfqTimeRange:[],deadlineRange:[]}),J=v([{label:"品牌A",value:"A"},{label:"品牌B",value:"B"},{label:"品牌C",value:"C"}]),H=v([{title:"电子元件",value:"electronics",children:[{title:"集成电路",value:"ic"},{title:"电阻电容",value:"passive"}]},{title:"机械零件",value:"mechanical",children:[{title:"螺丝螺栓",value:"screws"},{title:"轴承",value:"bearings"}]}]),w=v(null),S=v(null),M=v(!1),h=v({}),ne=()=>{console.log("切换到",I.value,"视图"),I.value==="material"&&w.value?w.value.fetchData():I.value==="order"&&S.value&&S.value.fetchData()},T=()=>{console.log("搜索条件:",m),I.value==="material"&&w.value?w.value.fetchData():I.value==="order"&&S.value&&S.value.fetchData()},U=()=>{Object.keys(m).forEach(R=>{Array.isArray(m[R])?m[R]=[]:m[R]=void 0}),T()},Q=R=>{h.value=R,M.value=!0},P=()=>{M.value=!1,h.value={}};return(R,c)=>{const j=r("a-input"),O=r("a-form-item"),D=r("a-col"),le=r("a-select-option"),oe=r("a-select"),ie=r("a-tree-select"),W=r("a-range-picker"),X=r("a-button"),L=r("a-space"),E=r("a-row"),re=r("a-form"),Z=r("a-radio-button"),N=r("a-radio-group"),V=r("a-descriptions-item"),z=r("a-descriptions"),ee=r("a-alert"),i=r("a-modal");return p(),x("div",rt,[u("div",st,[t(re,{layout:"inline",model:m},{default:a(()=>[t(E,{gutter:12},{default:a(()=>[t(D,{span:8},{default:a(()=>[t(O,{label:"产品型号"},{default:a(()=>[t(j,{value:m.model,"onUpdate:value":c[0]||(c[0]=n=>m.model=n),placeholder:"请输入型号"},null,8,["value"])]),_:1})]),_:1}),t(D,{span:8},{default:a(()=>[t(O,{label:"品牌"},{default:a(()=>[t(oe,{value:m.brand,"onUpdate:value":c[1]||(c[1]=n=>m.brand=n),placeholder:"请选择品牌",style:{width:"100%"},allowClear:""},{default:a(()=>[(p(!0),x(Se,null,qe(J.value,n=>(p(),A(le,{key:n.value,value:n.value},{default:a(()=>[o(_(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(D,{span:8},{default:a(()=>[t(O,{label:"产品分类"},{default:a(()=>[t(ie,{value:m.category,"onUpdate:value":c[2]||(c[2]=n=>m.category=n),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":H.value,placeholder:"请选择产品分类","tree-default-expand-all":"","allow-clear":""},null,8,["value","tree-data"])]),_:1})]),_:1}),t(D,{span:8},{default:a(()=>[t(O,{label:"询价单号"},{default:a(()=>[t(j,{value:m.rfqNo,"onUpdate:value":c[3]||(c[3]=n=>m.rfqNo=n),placeholder:"请输入询价单号"},null,8,["value"])]),_:1})]),_:1}),t(D,{span:8},{default:a(()=>[t(O,{label:"询价时间"},{default:a(()=>[t(W,{value:m.rfqTimeRange,"onUpdate:value":c[4]||(c[4]=n=>m.rfqTimeRange=n),format:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1}),t(D,{span:8},{default:a(()=>[t(O,{label:"截止时间"},{default:a(()=>[t(W,{value:m.deadlineRange,"onUpdate:value":c[5]||(c[5]=n=>m.deadlineRange=n),format:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1}),t(D,{span:2},{default:a(()=>[t(L,null,{default:a(()=>[t(X,{type:"primary",onClick:T},{default:a(()=>c[8]||(c[8]=[o("查询")])),_:1}),t(X,{onClick:U},{default:a(()=>c[9]||(c[9]=[o("重置")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u("div",dt,[u("div",ut,[t(N,{value:I.value,"onUpdate:value":c[6]||(c[6]=n=>I.value=n),"button-style":"solid",onChange:ne},{default:a(()=>[t(Z,{value:"material"},{default:a(()=>c[10]||(c[10]=[o("物料视图")])),_:1}),t(Z,{value:"order"},{default:a(()=>c[11]||(c[11]=[o("单据视图")])),_:1})]),_:1},8,["value"])]),I.value==="material"?(p(),A(ze,{key:0,ref_key:"materialTableRef",ref:w,searchForm:m,onSearch:T,onReset:U},null,8,["searchForm"])):f("",!0),I.value==="order"?(p(),A(it,{key:1,ref_key:"orderTableRef",ref:S,searchForm:m,onViewDetail:Q},null,8,["searchForm"])):f("",!0)]),t(i,{visible:M.value,"onUpdate:visible":c[7]||(c[7]=n=>M.value=n),title:"询价单详情",width:"1200px",footer:null,onCancel:P},{default:a(()=>[u("div",ct,[t(z,{column:3,bordered:""},{default:a(()=>[t(V,{label:"询价单号"},{default:a(()=>[o(_(h.value.rfqNo),1)]),_:1}),t(V,{label:"物料总数"},{default:a(()=>[o(_(h.value.materialCount)+" 种",1)]),_:1}),t(V,{label:"物料总价"},{default:a(()=>{var n;return[o("¥"+_((n=h.value.totalPrice)==null?void 0:n.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]}),_:1}),t(V,{label:"询价时间"},{default:a(()=>[o(_(h.value.rfqTime),1)]),_:1}),t(V,{label:"截止时间"},{default:a(()=>[o(_(h.value.deadline),1)]),_:1})]),_:1}),u("div",pt,[c[12]||(c[12]=u("h4",null,"物料明细",-1)),t(ee,{message:"此处可显示该询价单下的具体物料信息，包括每个物料的供应商报价情况等详细信息。",type:"info","show-icon":""})])])]),_:1},8,["visible"])])}}},ht=he(mt,[["__scopeId","data-v-22531f30"]]);export{ht as default};
