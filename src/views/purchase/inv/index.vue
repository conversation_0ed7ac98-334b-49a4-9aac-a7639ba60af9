<template>
  <div class="reconciliation-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-form layout="horizontal" :model="searchParams" @finish="fetchData">
        <a-row>
          <a-col :span="5">
            <a-form-item label="对账单号" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-input v-model:value="searchParams.reconciliationNumber" placeholder="请输入对账单号" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="对账状态" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-select v-model:value="searchParams.status" placeholder="请选择状态" style="width: 100%" allow-clear>
                <a-select-option value="generating">生成中</a-select-option>
                <a-select-option value="pending">待对账</a-select-option>
                <a-select-option value="confirmed">已锁定</a-select-option>
                <a-select-option value="settled">已结算</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="对账周期" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-range-picker v-model:value="searchParams.periodRange" format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="4" style="display: flex; align-items: flex-end;">
            <a-form-item>
              <a-button type="primary" html-type="submit" style="margin-right: 8px;">查询</a-button>
              <a-button @click="resetSearch">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <div style="margin-bottom: 16px;">
        <a-dropdown>
          <a-button type="primary" style="margin-right: 8px;">
            批量操作 <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleBatchConfirm">对账</a-menu-item>
              <a-menu-item key="2" @click="handleBatchInvoiceRequest">申请开票</a-menu-item>
              <a-menu-item key="3" @click="handleBatchInvoiceDownload">发票下载</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
        style="margin-top: 16px"
        :row-selection="rowSelection"
      >
        <!-- <template #expandedRowRender="{ record: expandedRecord }">
          <div class="expanded-content">
            <a-table
              :columns="materialColumns"
              :data-source="expandedRecord.materials"
              :pagination="false"
              row-key="id"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record: materialRecord }">
                <template v-if="column.key === 'unitPrice'">
                  {{ formatCurrency(materialRecord.unitPrice) }}
                </template>
                <template v-if="column.key === 'totalPrice'">
                  {{ formatCurrency(materialRecord.totalPrice) }}
                </template>
                <template v-if="column.key === 'receiptNumber'">
                  <a @click="handleViewReceipt(materialRecord as MaterialRecord)">{{ materialRecord.receiptNumber }}</a>
                </template>
                <template v-if="column.key === 'poNumber'">
                  <a @click="handleViewPO(materialRecord as MaterialRecord)">{{ materialRecord.poNumber }}</a>
                </template>
              </template>
            </a-table>
            <div style="text-align: right; margin-top: 16px;">
              <a-statistic title="对账单总金额" :value="formatCurrency(expandedRecord.totalAmount)" style="display: inline-block; margin-right: 32px;"/>
              <a-button v-if="expandedRecord.status === 'pending'" type="primary" style="margin-right: 8px;" @click="handleConfirm(expandedRecord as ReconciliationRecord)">确认对账单</a-button>
              <a-button v-if="expandedRecord.status === 'confirmed'" type="primary" @click="handleSettle(expandedRecord as ReconciliationRecord)">结算</a-button>
            </div>
          </div>
        </template> -->
        <template #bodyCell="{ column, record: rowRecord }">
          <template v-if="column.key === 'reconciliationNumber'">
            <a @click="handleView(rowRecord as ReconciliationRecord)">{{ rowRecord.reconciliationNumber }}</a>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(rowRecord.status)">
              {{ getStatusText(rowRecord.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'invoiceStatus'">
            <a-tag :color="getInvoiceStatusColor(rowRecord.invoiceStatus)">
              {{ getInvoiceStatusText(rowRecord.invoiceStatus) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'totalAmount'">
            {{ formatCurrency(rowRecord.totalAmount) }}
          </template>
          <template v-if="column.key === 'period'">
            {{ rowRecord.periodStart }} 至 {{ rowRecord.periodEnd }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a v-if="rowRecord.status === 'pending'" @click="handleConfirm(rowRecord as ReconciliationRecord)">对账</a>
              <a v-if="rowRecord.status === 'confirmed'" @click="handlePayment(rowRecord as ReconciliationRecord)">付款</a>
              <a v-if="rowRecord.status === 'confirmed' && rowRecord.invoiceStatus === 'not_invoiced'" @click="handleInvoiceRequest(rowRecord as ReconciliationRecord)">申请开票</a>
              <a v-if="rowRecord.status === 'settled' && rowRecord.invoiceStatus === 'not_invoiced'" @click="handleInvoiceRequest(rowRecord as ReconciliationRecord)">申请开票</a>
              <a v-if="rowRecord.invoiceStatus === 'invoiced'" @click="handleInvoiceDownload(rowRecord as ReconciliationRecord)">下载发票</a>
              <!-- <a v-if="rowRecord.status === 'pending'" @click="handlePrint(rowRecord as ReconciliationRecord)">打印</a> -->
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Table as ATable, Button as AButton, Card as ACard, Form as AForm, FormItem as AFormItem, 
  Input as AInput, Select as ASelect, SelectOption as ASelectOption, DatePicker as ADatePicker, 
  Space as ASpace, Tag as ATag, Divider as ADivider, message, Statistic as AStatistic,
  Row as ARow, Col as ACol, PageHeader as APageHeader, Menu as AMenu, MenuItem as AMenuItem,
  Dropdown as ADropdown } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import type { TableProps, TablePaginationConfig } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

const ARangePicker = ADatePicker.RangePicker;

// 定义对账单记录接口
interface ReconciliationRecord {
  id: string;
  reconciliationNumber: string;
  customerName: string;
  customerCode: string;
  createTime: string;
  periodStart: string;
  periodEnd: string;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'settled' | 'generating';
  invoiceStatus: 'not_invoiced' | 'invoicing' | 'invoiced';
  materials: MaterialRecord[];
}

// 定义物料记录接口
interface MaterialRecord {
  id: string;
  materialCode: string;
  materialName: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  receiptDate: string;
  receiptNumber: string;
  poNumber: string;
}

// 定义搜索参数接口
interface SearchParams {
  reconciliationNumber: string;
  customerName: string;
  status?: 'pending' | 'confirmed' | 'settled' | 'generating';
  periodRange?: [Dayjs, Dayjs] | undefined;
}

const router = useRouter();
const loading = ref<boolean>(false);
const dataSource = ref<ReconciliationRecord[]>([]);
const selectedRowKeys = ref<string[]>([]);

// 初始化搜索参数
const searchParams = reactive<SearchParams>({
  reconciliationNumber: '',
  customerName: '',
  status: undefined,
  periodRange: undefined,
});

// 分页设置
const pagination = reactive<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 表格选择配置
const rowSelection = reactive({
  onChange: (selectedKeys: (string | number)[], selectedRows: ReconciliationRecord[]) => {
    selectedRowKeys.value = selectedKeys as string[];
  },
  selectedRowKeys: selectedRowKeys,
});

// 表格列定义 - 对账单主表
const columns = [
  {
    title: '对账单号',
    dataIndex: 'reconciliationNumber',
    key: 'reconciliationNumber',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
  },
  {
    title: '对账周期',
    key: 'period',
    width: 180,
    customRender: ({ record }) => {
      return `${record.periodStart} 至 ${record.periodEnd}`;
    },
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '开票状态',
    dataIndex: 'invoiceStatus',
    key: 'invoiceStatus',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
  },
];

// 物料明细表格列定义
const materialColumns = [
  // {
  //   title: '物料编码',
  //   dataIndex: 'materialCode',
  //   key: 'materialCode',
  //   width: 120,
  // },
  {
    title: '物料名称',
    dataIndex: 'materialName',
    key: 'materialName',
    width: 180,
  },
  {
    title: '型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 150,
  },
  // {
  //   title: '单位',
  //   dataIndex: 'unit',
  //   key: 'unit',
  //   width: 80,
  // },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 100,
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 120,
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 150,
  },
  {
    title: '收货日期',
    dataIndex: 'receiptDate',
    key: 'receiptDate',
    width: 120,
  },
  {
    title: '收货单号',
    dataIndex: 'receiptNumber',
    key: 'receiptNumber',
    width: 150,
  },
  {
    title: '采购单号',
    dataIndex: 'poNumber',
    key: 'poNumber',
    width: 150,
  },
];

// 获取状态颜色
const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    generating: 'orange',
    pending: 'blue',
    confirmed: 'green',
    settled: 'purple',
  };
  return statusMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    generating: '生成中',
    pending: '待对账',
    confirmed: '已锁定',
    settled: '已结算',
  };
  return statusMap[status] || '未知状态';
};

// 获取发票状态颜色
const getInvoiceStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: 'default',
    invoicing: 'orange',
    invoiced: 'green',
  };
  return statusMap[status] || 'default';
};

// 获取发票状态文本
const getInvoiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: '未开票',
    invoicing: '开票中',
    invoiced: '已开票',
  };
  return statusMap[status] || '未知状态';
};

// 格式化货币
const formatCurrency = (value: number) => {
  return value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 重置搜索
const resetSearch = () => {
  searchParams.reconciliationNumber = '';
  searchParams.customerName = '';
  searchParams.status = undefined;
  searchParams.periodRange = undefined;
  fetchData();
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  // 清空选中项
  selectedRowKeys.value = [];
  try {
    // 模拟API调用
    setTimeout(() => {
      // 模拟数据
      dataSource.value = [
        {
          id: '1',
          reconciliationNumber: 'INV202505001',
          customerName: '上海智能科技有限公司',
          customerCode: 'CUST001',
          createTime: '2025-05-01 10:30:00',
          periodStart: '2025-04-01',
          periodEnd: '2025-04-30',
          totalAmount: 150000.00,
          status: 'pending',
          invoiceStatus: 'not_invoiced',
          materials: [
            {
              id: '101',
              materialCode: 'M001',
              materialName: '高性能芯片',
              specification: 'A100 型号',
              unit: '个',
              quantity: 100,
              unitPrice: 1000,
              totalPrice: 100000,
              receiptDate: '2025-04-15',
              receiptNumber: 'DN20250415001',
              poNumber: 'SO20250401001',
            },
            {
              id: '102',
              materialCode: 'M002',
              materialName: '电路板',
              specification: 'PCB-0023',
              unit: '片',
              quantity: 200,
              unitPrice: 250,
              totalPrice: 50000,
              receiptDate: '2025-04-20',
              receiptNumber: 'DN20250420002',
              poNumber: 'SO20250405002',
            },
          ],
        },
        {
          id: '2',
          reconciliationNumber: 'INV202504002',
          customerName: '北京科创企业有限公司',
          customerCode: 'CUST002',
          createTime: '2025-04-01 09:15:00',
          periodStart: '2025-03-01',
          periodEnd: '2025-03-31',
          totalAmount: 86500.00,
          status: 'confirmed',
          invoiceStatus: 'invoiced',
          materials: [
            {
              id: '201',
              materialCode: 'M003',
              materialName: '传感器',
              specification: 'S2000',
              unit: '个',
              quantity: 50,
              unitPrice: 800,
              totalPrice: 40000,
              receiptDate: '2025-03-18',
              receiptNumber: 'DN20250318001',
              poNumber: 'SO20250301001',
            },
            {
              id: '202',
              materialCode: 'M004',
              materialName: '连接器',
              specification: 'CN-100',
              unit: '套',
              quantity: 150,
              unitPrice: 310,
              totalPrice: 46500,
              receiptDate: '2025-03-25',
              receiptNumber: 'DN20250325002',
              poNumber: 'SO20250310002',
            },
          ],
        },
        {
          id: '3',
          reconciliationNumber: 'INV202504003',
          customerName: '北京科创企业有限公司',
          customerCode: 'CUST002',
          createTime: '2025-04-01 09:15:00',
          periodStart: '2025-03-01',
          periodEnd: '2025-03-31',
          totalAmount: 86500.00,
          status: 'confirmed',
          invoiceStatus: 'not_invoiced',
          materials: [
            {
              id: '201',
              materialCode: 'M003',
              materialName: '传感器',
              specification: 'S2000',
              unit: '个',
              quantity: 50,
              unitPrice: 800,
              totalPrice: 40000,
              receiptDate: '2025-03-18',
              receiptNumber: 'DN20250318001',
              poNumber: 'SO20250301001',
            },
            {
              id: '202',
              materialCode: 'M004',
              materialName: '连接器',
              specification: 'CN-100',
              unit: '套',
              quantity: 150,
              unitPrice: 310,
              totalPrice: 46500,
              receiptDate: '2025-03-25',
              receiptNumber: 'DN20250325002',
              poNumber: 'SO20250310002',
            },
          ],
        },
        {
          id: '4',
          reconciliationNumber: 'INV202505004',
          customerName: '深圳创新科技有限公司',
          customerCode: 'CUST003',
          createTime: '2025-05-02 14:20:00',
          periodStart: '2025-04-01',
          periodEnd: '2025-04-30',
          totalAmount: 95000.00,
          status: 'generating',
          invoiceStatus: 'not_invoiced',
          materials: [
            {
              id: '301',
              materialCode: 'M005',
              materialName: '显示屏',
              specification: 'LCD-2024',
              unit: '个',
              quantity: 30,
              unitPrice: 2500,
              totalPrice: 75000,
              receiptDate: '2025-04-12',
              receiptNumber: 'DN20250412001',
              poNumber: 'SO20250402001',
            },
            {
              id: '302',
              materialCode: 'M006',
              materialName: '控制器',
              specification: 'CTRL-500',
              unit: '个',
              quantity: 40,
              unitPrice: 500,
              totalPrice: 20000,
              receiptDate: '2025-04-18',
              receiptNumber: 'DN20250418002',
              poNumber: 'SO20250408002',
            },
          ],
        },
        {
          id: '5',
          reconciliationNumber: 'INV202503005',
          customerName: '广州智造有限公司',
          customerCode: 'CUST004',
          createTime: '2025-03-15 11:45:00',
          periodStart: '2025-02-01',
          periodEnd: '2025-02-28',
          totalAmount: 128000.00,
          status: 'settled',
          invoiceStatus: 'not_invoiced',
          materials: [
            {
              id: '401',
              materialCode: 'M007',
              materialName: '电机',
              specification: 'MOTOR-X1',
              unit: '台',
              quantity: 20,
              unitPrice: 4000,
              totalPrice: 80000,
              receiptDate: '2025-02-10',
              receiptNumber: 'DN20250210001',
              poNumber: 'SO20250201001',
            },
            {
              id: '402',
              materialCode: 'M008',
              materialName: '变频器',
              specification: 'INV-200',
              unit: '台',
              quantity: 16,
              unitPrice: 3000,
              totalPrice: 48000,
              receiptDate: '2025-02-20',
              receiptNumber: 'DN20250220002',
              poNumber: 'SO20250205002',
            },
          ],
        },
        {
          id: '6',
          reconciliationNumber: 'INV202503006',
          customerName: '杭州精密制造有限公司',
          customerCode: 'CUST005',
          createTime: '2025-03-20 16:30:00',
          periodStart: '2025-02-01',
          periodEnd: '2025-02-28',
          totalAmount: 75600.00,
          status: 'settled',
          invoiceStatus: 'invoiced',
          materials: [
            {
              id: '501',
              materialCode: 'M009',
              materialName: '精密轴承',
              specification: 'BEAR-2024',
              unit: '个',
              quantity: 120,
              unitPrice: 450,
              totalPrice: 54000,
              receiptDate: '2025-02-15',
              receiptNumber: 'DN20250215001',
              poNumber: 'SO20250203001',
            },
            {
              id: '502',
              materialCode: 'M010',
              materialName: '密封圈',
              specification: 'SEAL-100',
              unit: '个',
              quantity: 360,
              unitPrice: 60,
              totalPrice: 21600,
              receiptDate: '2025-02-25',
              receiptNumber: 'DN20250225002',
              poNumber: 'SO20250208002',
            },
          ],
        },
        {
          id: '7',
          reconciliationNumber: 'INV202505007',
          customerName: '成都智能装备有限公司',
          customerCode: 'CUST006',
          createTime: '2025-05-03 09:10:00',
          periodStart: '2025-04-01',
          periodEnd: '2025-04-30',
          totalAmount: 112000.00,
          status: 'generating',
          invoiceStatus: 'not_invoiced',
          materials: [
            {
              id: '601',
              materialCode: 'M011',
              materialName: '伺服电机',
              specification: 'SERVO-A1',
              unit: '台',
              quantity: 14,
              unitPrice: 6000,
              totalPrice: 84000,
              receiptDate: '2025-04-08',
              receiptNumber: 'DN20250408001',
              poNumber: 'SO20250401003',
            },
            {
              id: '602',
              materialCode: 'M012',
              materialName: '编码器',
              specification: 'ENC-500',
              unit: '个',
              quantity: 28,
              unitPrice: 1000,
              totalPrice: 28000,
              receiptDate: '2025-04-22',
              receiptNumber: 'DN20250422002',
              poNumber: 'SO20250410003',
            },
          ],
        },
      ];
      pagination.total = 7;
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('获取对账单数据失败:', error);
    message.error('获取对账单数据失败');
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (pag: TablePaginationConfig) => {
  pagination.current = pag.current || 1;
  pagination.pageSize = pag.pageSize || 10;
  fetchData();
};

// 确认对账单
const handleConfirm = (record: ReconciliationRecord) => {
  message.success(`对账单 ${record.reconciliationNumber} 已确认`);
  // 实际项目中应该调用API更新状态
  fetchData();
};

// 拒绝对账单
const handleReject = (record: ReconciliationRecord) => {
  message.warning(`对账单 ${record.reconciliationNumber} 已拒绝`);
  // 实际项目中应该调用API更新状态
  fetchData();
};

// 结算对账单
const handleSettle = (record: ReconciliationRecord) => {
  message.success(`对账单 ${record.reconciliationNumber} 已结算`);
  // 实际项目中应该调用API更新状态
  fetchData();
};

// 查看详情
const handleView = (record: ReconciliationRecord) => {
  router.push({
    path: `/workspace/purchase/inv/detail/${record.id}`,
    query: { id: record.id }
  });
};

// 打印对账单
const handlePrint = (record: ReconciliationRecord) => {
  message.info(`打印对账单: ${record.reconciliationNumber}`);
  // 实际项目中应该调用打印功能
};

// 导出对账单
const handleExport = () => {
  message.info('导出对账单');
  // 实际项目中应该调用导出功能
};

// 批量确认处理
const handleBatchConfirm = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条对账单');
    return;
  }
  message.success(`已批量确认选中的 ${selectedRowKeys.value.length} 条对账单`);
  // 实际项目中应该调用API批量更新状态
  selectedRowKeys.value = [];
  fetchData();
};

// 批量申请开票处理
const handleBatchInvoiceRequest = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条对账单');
    return;
  }
  message.success(`已批量申请开票 ${selectedRowKeys.value.length} 条对账单`);
  // 实际项目中应该调用API批量更新状态
  selectedRowKeys.value = [];
  fetchData();
};

// 批量下载发票处理
const handleBatchInvoiceDownload = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条对账单');
    return;
  }
  message.success(`已批量下载 ${selectedRowKeys.value.length} 条发票`);
  // 实际项目中应该调用API批量下载
  selectedRowKeys.value = [];
  fetchData();
};

// 付款处理
const handlePayment = (record: ReconciliationRecord) => {
  message.success(`对账单 ${record.reconciliationNumber} 已付款`);
  // 实际项目中应该调用API更新状态
  fetchData();
};

// 申请开票处理
const handleInvoiceRequest = (record: ReconciliationRecord) => {
  message.success(`已申请开票: ${record.reconciliationNumber}`);
  // 实际项目中应该调用API更新状态
  fetchData();
};

// 下载发票处理
const handleInvoiceDownload = (record: ReconciliationRecord) => {
  message.success(`正在下载发票: ${record.reconciliationNumber}`);
  // 实际项目中应该调用下载功能
};

// 查看收货单详情
const handleViewReceipt = (record: MaterialRecord) => {
  message.info(`查看收货单: ${record.receiptNumber}`);
  // 实际项目中应该跳转到收货单详情页
  // router.push(`/purchase/receipt/detail?receiptNumber=${record.receiptNumber}`);
};

// 查看采购单详情
const handleViewPO = (record: MaterialRecord) => {
  message.info(`查看采购单: ${record.poNumber}`);
  // 实际项目中应该跳转到采购单详情页
  // router.push(`/purchase/po/detail?poNumber=${record.poNumber}`);
};

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.expanded-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

:deep(.ant-table-expanded-row > td) {
  background-color: #fafafa;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
}

.info-item .label {
  color: rgba(0, 0, 0, 0.65);
  margin-right: 8px;
  min-width: 90px;
}

.info-item .value {
  color: rgba(0, 0, 0, 0.85);
  flex: 1;
}

.info-item .important {
  font-weight: bold;
  color: #f5222d;
}
</style>

