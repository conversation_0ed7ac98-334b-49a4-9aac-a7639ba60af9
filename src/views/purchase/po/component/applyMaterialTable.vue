<template>
  <div class="table-area">
    <!-- 表格操作按钮 -->
    <div class="table-operations">
      <a-space>
        <a-button type="primary" @click="handleExport"> <export-outlined /> 导出 </a-button>
        <a-button type="primary" @click="handlePrint"> <printer-outlined /> 打印 </a-button>
      </a-space>
      <a-button @click="toggleTableConfig"> <setting-outlined /> 列设置 </a-button>
    </div>

    <!-- 选中物料汇总信息 -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
              <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
              <div>2. 草稿状态的物料申请可以编辑、删除或提交审批。</div>
              <div>3. 提交后的物料申请将进入审批流程，无法再次编辑。</div>
            </template>
          <span style="color: #666"><InfoCircleFilled style="margin-right: 4px" />物料申请说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedRowKeys.length }}</a-tag> 个物料</span
        >
        <span
          >总金额：<a-tag color="red">¥{{ selectedTotalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-tag></span
        >
      </div>
    </div>

    <!-- 主表格 -->
    <a-table 
      :columns="visibleColumns" 
      :data-source="tableData" 
      :loading="loading" 
      :pagination="pagination" 
      @change="handleTableChange" 
      row-key="id" 
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" 
      bordered 
      :scroll="{ x: 1800 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'unitPrice' || column.dataIndex === 'totalPrice'">
          {{ parseFloat(record[column.dataIndex]).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
        </template>
        <template v-if="column.dataIndex === 'approvalStatus'">
          <a-tag :color="getApprovalStatusColor(record.approvalStatus)">{{ getApprovalStatusText(record.approvalStatus) }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleOrderDetail(record)">申请详情</a>
            <a v-if="canEdit(record.approvalStatus)" @click="handleEdit(record)">编辑</a>
            <a v-if="canDelete(record.approvalStatus)" @click="handleDelete(record)" class="danger-link">删除</a>
            <a v-if="canSubmit(record.approvalStatus)" @click="handleSubmit(record)" class="submit-link">提交</a>
            <a v-if="canReview(record.approvalStatus)" @click="handleReview(record)" class="review-link">审核</a>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 表格列配置抽屉 -->
    <a-drawer title="配置表格列" placement="right" :visible="showTableConfig" @close="toggleTableConfig" width="400px">
      <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnsChange">
        <a-row>
          <a-col :span="12" v-for="col in allColumns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.fixed">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { SettingOutlined, ExportOutlined, PrinterOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 表格配置
const showTableConfig = ref(false);
const toggleTableConfig = () => {
  showTableConfig.value = !showTableConfig.value;
};

// 采购申请专用的物料表格列（去除了物流状态、财务状态等订单相关字段）
const allColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180, fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', width: 150, fixed: 'left' },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '分类', dataIndex: 'category', key: 'category', width: 120 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '单价（¥）', dataIndex: 'unitPrice', key: 'unitPrice', width: 100 },
  { title: '总价（¥）', dataIndex: 'totalPrice', key: 'totalPrice', width: 120 },
  { title: '来源询价单', dataIndex: 'rfqNo', key: 'rfqNo', width: 150 },
  { title: '申请单号', dataIndex: 'soNo', key: 'soNo', width: 180 },
  { title: '审核状态', dataIndex: 'approvalStatus', key: 'approvalStatus', width: 100 },
  { title: '创建人', dataIndex: 'creator', key: 'creator', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 150 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 220, fixed: 'right' },
];

// 当前选中的表格列
const selectedColumns = ref([
  'soNo', 'name', 'model', 'quantity', 'unitPrice', 'totalPrice', 
  'approvalStatus', 'creator', 'createTime', 'action'
]);

// 可见的表格列
const visibleColumns = computed(() => {
  return allColumns.filter((col) => selectedColumns.value.includes(col.dataIndex) || col.fixed);
});

// 列变更处理
const handleColumnsChange = (checkedValues) => {
  selectedColumns.value = checkedValues;
  emit('columnsChange', checkedValues);
};

// 表格数据
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
    }),
  },
  selectedRowKeys: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['tableChange', 'selectChange', 'export', 'print', 'edit', 'delete', 'submit', 'review', 'columnsChange']);

// 计算选中物料的总价
const selectedTotalPrice = computed(() => {
  return parseFloat(
    props.tableData
      .filter((item) => props.selectedRowKeys.includes(item.id))
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0)
      .toFixed(2)
  );
});

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  emit('selectChange', newSelectedRowKeys);
};

// 表格变化处理
const handleTableChange = (pag) => {
  emit('tableChange', pag);
};

// 审核状态颜色和文本
const getApprovalStatusColor = (status) => {
  const map = {
    pending: 'default',
    reviewing: 'processing',
    approved: 'success',
    rejected: 'error',
  };
  return map[status] || 'default';
};

const getApprovalStatusText = (status) => {
  const map = {
    pending: '未审核',
    reviewing: '审核中',
    approved: '审核通过',
    rejected: '审核不通过',
  };
  return map[status] || '未知';
};

// 操作权限控制
const canEdit = (approvalStatus) => {
  return ['pending', 'rejected'].includes(approvalStatus);
};

const canDelete = (approvalStatus) => {
  return ['pending', 'rejected'].includes(approvalStatus);
};

const canSubmit = (approvalStatus) => {
  return ['pending', 'rejected'].includes(approvalStatus);
};

const canReview = (approvalStatus) => {
  return approvalStatus === 'reviewing';
};

// 操作方法
const handleOrderDetail = (record) => {
  // 跳转到申请详情页面，而不是采购单详情
      router.push({ path: '/workspace/purchase/applyDetail', query: { id: record.id, soNo: record.soNo } });
};

const handleEdit = (record) => {
  emit('edit', record);
};

const handleDelete = (record) => {
  emit('delete', record);
};

const handleSubmit = (record) => {
  emit('submit', record);
};

const handleReview = (record) => {
  emit('review', record);
};

const handleExport = () => {
  emit('export');
};

const handlePrint = () => {
  emit('print');
};
</script>

<style scoped>
.table-area {
  position: relative;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.danger-link {
  color: #ff4d4f;
}

.submit-link {
  color: #52c41a;
  font-weight: 500;
}

.review-link {
  color: #1890ff;
  font-weight: 500;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff7e6;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffd591;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    gap: 8px;
  }

  .selection-summary {
    flex-direction: column;
  }
}
</style> 