<template>
  <div class="material-table">
    <!-- 表格操作栏 -->
    <div class="table-operations">
      <a-space>
        <a-dropdown>
          <a-button type="primary">
            批量操作
            <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleBatchOperation">
              <a-menu-item key="startInquiry">启动询价</a-menu-item>
              <a-menu-item key="toPurchaseOrder">转采购单</a-menu-item>
              <a-menu-item key="cancel">取消</a-menu-item>
              <a-menu-item key="delete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button type="primary" class="smart-allocation-btn" @click="handleSmartAllocation">智能分配</a-button>
      </a-space>
      <a-button @click="toggleTableConfig"><setting-outlined /> 列设置</a-button>
    </div>

    <!-- 选中询价单汇总信息 -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
            <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
            <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：<br />订单总金额¥0.00 - ¥499.99，运费¥15.00<br />订单总金额¥500.00 - ¥999.99，运费¥8.00<br />订单总金额¥1000以上，免运费</div>
          </template>
          <span style="color: #666"><InfoCircleFilled style="margin-right: 4px" />价格与运费说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedRowKeys.length }}</a-tag> 个询价单</span
        >
        <span
          >总金额：<a-tag color="red">¥{{ selectedTotalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-tag></span
        >
      </div>
    </div>

    <!-- 物料表格 -->
    <a-table :columns="visibleColumns" :data-source="tableData" size="middle" :loading="loading" :pagination="pagination" @change="handleTableChange" row-key="id" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" bordered :scroll="{ x: 1500 }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a v-if="canConfigSupplier(record.status)" @click="handleConfigSupplier(record)">配置供应商</a>
            <a v-if="canStartInquiry(record.status)" @click="handleStartInquiry(record)">启动询价</a>
            <a v-if="canToPurchaseOrder(record.status)" @click="handleToPurchaseOrder(record)">转采购单</a>
            <a v-if="canReInquiry(record.status)" @click="handleReInquiry(record)">再次询价</a>
            <a v-if="canCancel(record.status)" @click="handleCancel(record)" class="danger-link">取消</a>
            <a-popconfirm v-if="canDelete(record.status)" title="确定要删除此询价单吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
              <a class="danger-link">删除</a>
            </a-popconfirm>
            <a>询价历史</a>
          </a-space>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'quoteProgress'">
          <div class="quote-progress">
            <span>供应商数：{{ getSupplierCount(record) }}，</span>
            <span>已报价：<span class="quoted-count">{{ getQuotedCount(record) }}</span>，</span>
            <span>已拒绝：<span class="rejected-count">{{ getRejectedCount(record) }}</span></span>
          </div>
        </template>
      </template>

      <!-- 嵌套表格 -->
      <template #expandedRowRender="{ record }">
        <div style="margin: 12px">
          <a-table size="small" :columns="getSupplierColumns(record.acceptAlternative)" :data-source="record.suppliers" :pagination="false" row-key="id" bordered>
            <template #bodyCell="{ column, record: supplierRecord }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getSupplierStatusColor(supplierRecord.status)">
                  {{ getSupplierStatusText(supplierRecord.status) }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-button 
                  type="link" 
                  :type="supplierRecord.isSelected ? 'primary' : 'default'" 
                  :disabled="supplierRecord.isSelected || supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' || supplierRecord.quoteType === 'external'" 
                  @click="handleSelectSupplier(supplierRecord, record)"
                >
                  {{ supplierRecord.isSelected ? '已选择' : '选择' }}
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </template>
    </a-table>

    <!-- 表格列配置抽屉 -->
    <a-drawer title="配置表格列" placement="right" :visible="showTableConfig" @close="toggleTableConfig" width="400px">
      <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnsChange">
        <a-row>
          <a-col :span="12" v-for="col in allColumns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.fixed">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-drawer>

    <!-- 供应商选择弹窗 -->
    <SupplierModal :title="'配置供应商'" :visible="supplierModalVisible" :confirm-loading="supplierModalLoading" @update:visible="supplierModalVisible = $event" @ok="handleSupplierSelect" @cancel="handleSupplierModalCancel" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits } from 'vue';
import { DownOutlined, SettingOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
import SupplierModal from './supplierModal.vue';

const props = defineProps({
  searchForm: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['search', 'reset']);

// 表格列配置
const showTableConfig = ref(false);
const toggleTableConfig = () => {
  showTableConfig.value = !showTableConfig.value;
};

// 所有可能的表格列
const allColumns = [
  { title: '商品名称', dataIndex: 'name', key: 'name', width: 180, fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', width: 180, fixed: 'left' },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '期望交期', dataIndex: 'expectedDelivery', key: 'expectedDelivery', width: 100 },
  { 
    title: '接受平替', 
    dataIndex: 'acceptAlternative', 
    key: 'acceptAlternative', 
    width: 100,
    customRender: ({ text }) => text ? '是' : '否'
  },
  { title: '询价状态', dataIndex: 'status', key: 'status', width: 100 },
  {
    title: '报价进度',
    dataIndex: 'quoteProgress',
    key: 'quoteProgress',
    width: 250,
    customRender: ({ record }) => {
      return getQuoteProgressDisplay(record);
    }
  },

  {
    title: '平替品牌',
    dataIndex: 'alternativeBrand',
    key: 'alternativeBrand',
    width: 120,
    customRender: ({ text, record }) => {
      return record.acceptAlternative && text ? text : '-';
    }
  },
  { 
    title: '平替型号', 
    dataIndex: 'alternativeModel', 
    key: 'alternativeModel', 
    width: 120,
    customRender: ({ text, record }) => {
      return record.acceptAlternative && text ? text : '-';
    }
  },
  {
    title: '单价 (¥)',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 100,
    customRender: ({ text, record }) => {
      return record.selectedSupplier ? `¥${record.selectedSupplier.price.toFixed(2)}` : '';
    },
  },
  {
    title: '总价 (¥)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 120,
    customRender: ({ text, record }) => {
      return record.selectedSupplier ? `¥${record.selectedSupplier.totalPrice.toFixed(2)}` : '';
    },
  },
  {
    title: '最小起订量',
    dataIndex: 'minOrderQuantity',
    key: 'minOrderQuantity',
    width: 100,
    customRender: ({ text, record }) => {
      return record.selectedSupplier ? record.selectedSupplier.minOrderQuantity : '';
    },
  },
  {
    title: '交期',
    dataIndex: 'delivery',
    key: 'delivery',
    width: 100,
    customRender: ({ text, record }) => {
      return record.selectedSupplier ? record.selectedSupplier.promisedDelivery : '';
    },
  },
  {
    title: '询价单号',
    dataIndex: 'rfqNo',
    key: 'rfqNo',
    width: 150,
    customRender: ({ text, record }) => {
      return record.status === 'notStarted' ? '-' : text;
    },
  },
  {
    title: '询价时间',
    dataIndex: 'rfqTime',
    key: 'rfqTime',
    width: 150,
    customRender: ({ text, record }) => {
      return record.status === 'notStarted' ? '-' : text;
    },
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    width: 150,
    customRender: ({ text, record }) => {
      return record.status === 'inProgress' ? '-' : text;
    },
  },
  { title: '截止时间', dataIndex: 'deadline', key: 'deadline', width: 150 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 275 },
];

// 当前选中的表格列
const selectedColumns = ref(['name', 'model', 'brand', 'quantity', 'acceptAlternative', 'alternativeBrand', 'alternativeModel', 'unitPrice', 'totalPrice', 'expectedDelivery', 'rfqNo', 'rfqTime', 'deadline', 'status', 'quoteProgress', 'action']);

// 可见的表格列
const visibleColumns = computed(() => {
  return allColumns.filter((col) => selectedColumns.value.includes(col.dataIndex) || col.fixed);
});

// 列变更处理
const handleColumnsChange = (checkedValues) => {
  selectedColumns.value = checkedValues;
};

// 获取供应商表格列定义（根据是否接受平替动态生成）
const getSupplierColumns = (acceptAlternative) => {
  const baseColumns = [];
  
  // 添加供应商列（最前面）
  baseColumns.push({
    title: '供应商',
    dataIndex: 'supplierName',
    key: 'supplierName',
    width: 150,
    customRender: ({ text, record }) => {
      return text || record.name || '-';
    },
  });
  
  // 如果接受平替，添加平替相关列
  if (acceptAlternative) {
    baseColumns.push(
      { 
        title: '平替品牌', 
        dataIndex: 'alternativeBrand', 
        key: 'alternativeBrand', 
        width: 120,
        customRender: ({ text, record }) => {
          return record.status === 'pending' || record.status === 'rejected' ? '-' : (text || '-');
        }
      },
      { 
        title: '平替型号', 
        dataIndex: 'alternativeModel', 
        key: 'alternativeModel', 
        width: 120,
        customRender: ({ text, record }) => {
          return record.status === 'pending' || record.status === 'rejected' ? '-' : (text || '-');
        }
      }
    );
  }
  
  // 添加其他列
  baseColumns.push(
    {
      title: '报价 (¥)',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      customRender: ({ text, record }) => {
        if (record.status === 'pending' || record.status === 'rejected') return '-';
        return text ? `¥${text.toFixed(2)}` : '';
      },
    },
    {
      title: '总价 (¥)',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 100,
      customRender: ({ text, record }) => {
        if (record.status === 'pending' || record.status === 'rejected') return '-';
        return text ? `¥${text.toFixed(2)}` : '';
      },
    },
    {
      title: '最小起订量',
      dataIndex: 'minOrderQuantity',
      key: 'minOrderQuantity',
      width: 150,
    },
    {
      title: '承诺交期',
      dataIndex: 'promisedDelivery',
      key: 'promisedDelivery',
      width: 120,
      customRender: ({ text, record }) => {
        return record.status === 'pending' || record.status === 'rejected' ? '-' : text;
      },
    },
    {
      title: '有效期',
      dataIndex: 'validityPeriod',
      key: 'validityPeriod',
      width: 120,
      customRender: ({ text, record }) => {
        return record.status === 'pending' || record.status === 'rejected' ? '-' : text;
      },
    },
    {
      title: '报价时间',
      dataIndex: 'quoteTime',
      key: 'quoteTime',
      width: 150,
      customRender: ({ text, record }) => {
        return record.status === 'pending' || record.status === 'rejected' ? '-' : text;
      },
    },
    { title: '报价状态', dataIndex: 'status', key: 'status', width: 100 },
    {
      title: '报价类型',
      dataIndex: 'quoteType',
      key: 'quoteType',
      width: 100,
      customRender: ({ text }) => {
        return text === 'platform' ? '平台报价' : '外部报价';
      },
    },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
    }
  );
  
  return baseColumns;
};

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 添加选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

// 计算选中询价单的总价
const selectedTotalPrice = computed(() => {
  return parseFloat(
    tableData.value
      .filter((item) => selectedRowKeys.value.includes(item.id))
      .reduce((sum, item) => {
        // 如果有选中的供应商，使用其总价，否则使用默认的总价或0
        const totalPrice = item.selectedSupplier ? item.selectedSupplier.totalPrice : item.totalPrice || 0;
        return sum + totalPrice;
      }, 0)
      .toFixed(2)
  );
});

// 选择供应商
const handleSelectSupplier = (supplierRecord, parentRecord) => {
  if (parentRecord) {
    // 重置所有供应商的选中状态
    parentRecord.suppliers.forEach((supplier) => {
      supplier.isSelected = false;
    });

    // 设置当前供应商为选中
    const supplier = parentRecord.suppliers.find((s) => s.id === supplierRecord.id);
    if (supplier) {
      supplier.isSelected = true;
      // 设置父记录的选中供应商
      parentRecord.selectedSupplier = supplier;
      
      // 如果接受平替且供应商有平替信息，同步到外层表格
      if (parentRecord.acceptAlternative && supplier.alternativeBrand && supplier.alternativeModel) {
        parentRecord.alternativeBrand = supplier.alternativeBrand;
        parentRecord.alternativeModel = supplier.alternativeModel;
      }
    }
  }
};

// 状态颜色和文本
const getStatusColor = (status) => {
  const map = {
    notStarted: 'default',
    inProgress: 'blue',
    accepted: 'green',
    expired: 'orange',
    invalid: 'red',
    cancelled: 'red',
  };
  return map[status] || 'default';
};

const getStatusText = (status) => {
  const map = {
    notStarted: '未开始',
    inProgress: '询价中',
    accepted: '已采纳',
    expired: '已过期',
    invalid: '已失效',
    cancelled: '已取消',
  };
  return map[status] || '未知';
};

// 供应商状态颜色和文本
const getSupplierStatusColor = (status) => {
  const map = {
    pending: 'blue',
    quoted: 'green',
    rejected: 'red',
    expired: 'orange',
  };
  return map[status] || 'default';
};

const getSupplierStatusText = (status) => {
  const map = {
    pending: '待报价',
    quoted: '已报价',
    rejected: '已拒绝',
    expired: '已过期',
  };
  return map[status] || '未知';
};

// 计算报价进度显示
const getQuoteProgressDisplay = (record) => {
  if (!record.suppliers || record.suppliers.length === 0) {
    return '供应商数：0，已报价：0，已拒绝：0';
  }

  const totalSuppliers = record.suppliers.length;
  const quotedCount = record.suppliers.filter(s => s.status === 'quoted').length;
  const rejectedCount = record.suppliers.filter(s => s.status === 'rejected').length;

  return `供应商数：${totalSuppliers}，已报价：${quotedCount}，已拒绝：${rejectedCount}`;
};

// 获取供应商总数
const getSupplierCount = (record) => {
  return record.suppliers ? record.suppliers.length : 0;
};

// 获取已报价数量
const getQuotedCount = (record) => {
  return record.suppliers ? record.suppliers.filter(s => s.status === 'quoted').length : 0;
};

// 获取已拒绝数量
const getRejectedCount = (record) => {
  return record.suppliers ? record.suppliers.filter(s => s.status === 'rejected').length : 0;
};

// 判断可执行的操作
const canConfigSupplier = (status) => status === 'notStarted';
const canStartInquiry = (status) => status === 'notStarted';
const canToPurchaseOrder = (status) => status === 'accepted';
const canReInquiry = (status) => ['accepted', 'expired', 'invalid', 'cancelled'].includes(status);
const canCancel = (status) => status === 'inProgress';
const canDelete = (status) => ['notStarted', 'expired', 'invalid', 'cancelled'].includes(status);

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 获取数据
const fetchData = () => {
  loading.value = true;

  // 模拟数据请求，实际项目中请替换为真实API调用
  setTimeout(() => {
    // 模拟数据
    tableData.value = Array.from({ length: 10 }).map((_, index) => {
      const quantity = Math.floor(Math.random() * 100 + 10);
      const unitPrice = Math.floor(Math.random() * 1000 + 100);
      const status = index % 6 === 0 ? 'notStarted' : index % 6 === 1 ? 'inProgress' : index % 6 === 2 ? 'accepted' : index % 6 === 3 ? 'expired' : index % 6 === 4 ? 'invalid' : 'cancelled';

      const suppliers = Array.from({ length: 3 }).map((_, sIndex) => {
        const supplierStatus = sIndex % 4 === 0 ? 'pending' : sIndex % 4 === 1 ? 'quoted' : sIndex % 4 === 2 ? 'rejected' : 'expired';
        const supplierPrice = Math.floor(Math.random() * 1000 + 100);
        
        // 模拟平替信息（只有部分供应商有平替）
        const hasAlternative = sIndex === 1; // 第二个供应商有平替
        const alternativeBrands = ['兼容品牌A', '兼容品牌B', '通用品牌C'];
        const alternativeModels = ['ALT-001', 'COMP-002', 'GEN-003'];

        // 模拟报价类型和供应商名称
        const quoteType = sIndex % 2 === 0 ? 'platform' : 'external';
        const supplierNames = {
          platform: ['严选供应商', '深圳市电子科技有限公司', '北京智能制造有限公司'],
          external: ['上海精密器件有限公司', '广州电子元件供应商', '天津工业设备公司']
        };

        return {
          id: `supplier-${index}-${sIndex}`,
          name: `供应商 ${sIndex + 1}`,
          supplierName: supplierNames[quoteType][sIndex % supplierNames[quoteType].length],
          price: supplierPrice,
          totalPrice: supplierPrice * quantity,
          minOrderQuantity: Math.floor(Math.random() * 100 + 10),
          promisedDelivery: `${Math.floor(Math.random() * 30 + 15)}天`,
          validityPeriod: '2023-07-05',
          quoteTime: '2023-06-05',
          status: supplierStatus,
          remark: sIndex % 2 === 0 ? '含税价格' : '',
          isSelected: false,
          quoteType: quoteType, // 'platform' 或 'external'
          // 平替信息
          alternativeBrand: hasAlternative && supplierStatus === 'quoted' ? alternativeBrands[sIndex % alternativeBrands.length] : null,
          alternativeModel: hasAlternative && supplierStatus === 'quoted' ? alternativeModels[sIndex % alternativeModels.length] : null,
        };
      });

      // 如果是已采纳状态，默认选中第一个已报价的供应商
      let selectedSupplier = null;
      let initialAlternativeBrand = null;
      let initialAlternativeModel = null;
      
      if (status === 'accepted') {
        const quotedSupplier = suppliers.find((s) => s.status === 'quoted');
        if (quotedSupplier) {
          quotedSupplier.isSelected = true;
          selectedSupplier = quotedSupplier;
          
          // 如果接受平替且供应商有平替信息，设置初始平替信息
          if (index % 3 === 0 && quotedSupplier.alternativeBrand && quotedSupplier.alternativeModel) {
            initialAlternativeBrand = quotedSupplier.alternativeBrand;
            initialAlternativeModel = quotedSupplier.alternativeModel;
          }
        }
      }

      return {
        id: `rfq-${index}`,
        name: `测试商品 ${index + 1}`,
        model: `MODEL-${100 + index}`,
        brand: index % 3 === 0 ? 'A' : index % 3 === 1 ? 'B' : 'C',
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: quantity * unitPrice,
        expectedDelivery: '30天',
        rfqNo: status === 'notStarted' ? '' : `RFQ-2023-${1000 + index}`,
        rfqTime: status === 'notStarted' ? '' : '2023-06-01',
        endTime: '2023-06-15',
        deadline: '2023-06-10',
        status: status,
        remark: index % 2 === 0 ? '紧急采购' : '',
        suppliers: suppliers,
        selectedSupplier: selectedSupplier,
        // 平替相关字段
        acceptAlternative: index % 3 === 0, // 1/3的记录接受平替
        alternativeBrand: initialAlternativeBrand, // 如果有已选供应商的平替信息，使用该信息
        alternativeModel: initialAlternativeModel, // 如果有已选供应商的平替信息，使用该信息
      };
    });

    pagination.total = 100; // 模拟总数据量
    loading.value = false;
  }, 500);
};

// 操作方法
const handleDelete = (record) => {
  console.log('删除询价单', record);
};

// 配置供应商相关数据
const supplierModalVisible = ref(false);
const supplierModalLoading = ref(false);
const currentRecord = ref(null);

// 处理配置供应商
const handleConfigSupplier = (record) => {
  currentRecord.value = record;
  supplierModalVisible.value = true;
};

// 启动询价
const handleStartInquiry = (record) => {
  console.log('启动询价', record);
};

// 转采购单
const handleToPurchaseOrder = (record) => {
  console.log('转采购单', record);
};

// 再次询价
const handleReInquiry = (record) => {
  console.log('再次询价', record);
};

// 取消
const handleCancel = (record) => {
  console.log('取消', record);
};

// 处理供应商选择
const handleSupplierSelect = (selectedSuppliers) => {
  if (currentRecord.value && selectedSuppliers && selectedSuppliers.length > 0) {
    // 将选中的供应商添加到当前询价单
    selectedSuppliers.forEach((supplier) => {
      // 检查是否已存在相同ID的供应商
      const existingIndex = currentRecord.value.suppliers.findIndex((s) => s.id === supplier.id);

      if (existingIndex === -1) {
        // 转换为询价单供应商格式
        const newSupplier = {
          id: supplier.id,
          name: supplier.name,
          supplierName: supplier.name, // 新添加的供应商名称
          price: null,
          totalPrice: null,
          minOrderQuantity: 1,
          promisedDelivery: '',
          validityPeriod: '',
          quoteTime: '',
          status: 'pending',
          remark: '',
          isSelected: false,
          quoteType: 'platform', // 新添加的供应商默认为平台报价
        };

        currentRecord.value.suppliers.push(newSupplier);
      }
    });

    // 关闭弹窗
    supplierModalVisible.value = false;

    // 可以添加一个提示
    alert(`已成功添加${selectedSuppliers.length}个供应商`);
  }
};

// 关闭供应商选择弹窗
const handleSupplierModalCancel = () => {
  supplierModalVisible.value = false;
};

// 批量操作处理
const handleBatchOperation = ({ key }) => {
  if (selectedRowKeys.value.length === 0) {
    // 如果没有选中行，提示用户
    return alert('请先选择要操作的询价单');
  }

  const selectedRecords = tableData.value.filter((item) => selectedRowKeys.value.includes(item.id));

  switch (key) {
    case 'startInquiry':
      console.log('批量启动询价', selectedRecords);
      break;
    case 'toPurchaseOrder':
      console.log('批量转采购单', selectedRecords);
      break;
    case 'cancel':
      console.log('批量取消', selectedRecords);
      break;
    case 'delete':
      console.log('批量删除', selectedRecords);
      break;
    default:
      break;
  }
};

// 智能分配处理
const handleSmartAllocation = () => {
  if (selectedRowKeys.value.length === 0) {
    return alert('请先选择要分配的询价单');
  }

  const selectedRecords = tableData.value.filter((item) => selectedRowKeys.value.includes(item.id));
  console.log('智能分配供应商', selectedRecords);
};

// 暴露给父组件的方法
defineExpose({
  fetchData,
  selectedRowKeys,
  selectedTotalPrice
});

// 初始化
fetchData();
</script>

<style scoped>
.material-table {
  width: 100%;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.danger-link {
  color: #ff4d4f;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff4f0;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffa39e;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

.quote-progress {
  font-size: 12px;
}

.quoted-count {
  color: #52c41a;
  font-weight: bold;
}

.rejected-count {
  color: #ff4d4f;
  font-weight: bold;
}
</style>
