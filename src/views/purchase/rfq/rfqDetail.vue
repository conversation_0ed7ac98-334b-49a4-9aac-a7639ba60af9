<template>
  <div class="rfq-detail-container">
    <a-page-header 
      :title="'询价单详情 - ' + rfqData.rfqNo" 
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-button v-if="canStartInquiry" type="primary">启动询价</a-button>
          <a-button v-if="canToPurchaseOrder" type="primary">转采购单</a-button>
          <a-button v-if="canReInquiry">再次询价</a-button>
          <a-button v-if="canCancel" danger>取消询价</a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 基本信息 -->
    <a-card title="基本信息" class="detail-card">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">询价单号：</span>
            <span class="value">{{ rfqData.rfqNo }}</span>
          </div>
        </a-col>

        <a-col :span="8">
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(rfqData.createTime) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">询价时间：</span>
            <span class="value">{{ rfqData.status === 'notStarted' ? '-' : formatDateTime(rfqData.rfqTime) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">截止时间：</span>
            <span class="value">{{ formatDateTime(rfqData.deadline) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">结束时间：</span>
            <span class="value">{{ rfqData.status === 'inProgress' ? '-' : formatDateTime(rfqData.endTime) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">创建人：</span>
            <span class="value">{{ rfqData.creator }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ rfqData.contactPhone }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">物料型号数：</span>
            <span class="value">{{ rfqData.materialModelCount }} 种</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">物料总数：</span>
            <span class="value">{{ getTotalQuantity() }} 件</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">物料总价：</span>
            <span class="value important">{{ getSelectedTotalPrice() }}</span>
          </div>
        </a-col>

      </a-row>
    </a-card>

    <!-- 物料信息 -->
    <a-card title="物料信息" class="detail-card">
      <div class="table-operations">
        <a-space>
          <a-dropdown>
            <a-button type="primary">
              批量操作
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleBatchOperation">
                <a-menu-item key="configSupplier" v-if="canConfigSupplier">配置供应商</a-menu-item>
                <a-menu-item key="startInquiry" v-if="canStartInquiry">启动询价</a-menu-item>
                <a-menu-item key="toPurchaseOrder" v-if="canToPurchaseOrder">转采购单</a-menu-item>
                <a-menu-item key="cancel" v-if="canCancel">取消</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button v-if="canConfigSupplier" type="primary" @click="handleSmartAllocation">智能分配</a-button>
        </a-space>
      </div>

      <!-- 选中汇总信息 -->
      <div class="selection-summary">
        <div>
          <a-tooltip placement="top">
            <template #title>
              <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
              <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：<br />订单总金额¥0.00 - ¥499.99，运费¥15.00<br />订单总金额¥500.00 - ¥999.99，运费¥8.00<br />订单总金额¥1000以上，免运费</div>
            </template>
            <span style="color: #666"><info-circle-filled style="margin-right: 4px" />价格与运费说明</span>
          </a-tooltip>
        </div>
        <div class="summary-content">
          <span>已选择：<a-tag color="blue">{{ selectedRowKeys.length }}</a-tag> 个物料</span>
          <span>总金额：<a-tag color="red">{{ getSelectedTotalPrice() }}</a-tag></span>
        </div>
      </div>

      <a-table 
        :columns="materialColumns" 
        :data-source="rfqData.materials" 
        size="middle" 
        :pagination="{ pageSize: 20 }" 
        row-key="id" 
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" 
        bordered 
        :scroll="{ x: 1500 }"
        :expandedRowKeys="expandedRowKeys"
        @expand="onExpand"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'acceptAlternative'">
            {{ record.acceptAlternative ? '是' : '否' }}
          </template>
          <template v-if="column.dataIndex === 'alternativeBrand'">
            {{ record.acceptAlternative && record.alternativeBrand ? record.alternativeBrand : '-' }}
          </template>
          <template v-if="column.dataIndex === 'alternativeModel'">
            {{ record.acceptAlternative && record.alternativeModel ? record.alternativeModel : '-' }}
          </template>
          <template v-if="column.dataIndex === 'unitPrice'">
            {{ record.selectedSupplier ? `¥${record.selectedSupplier.price.toFixed(2)}` : '-' }}
          </template>
          <template v-if="column.dataIndex === 'totalPrice'">
            {{ record.selectedSupplier ? `¥${record.selectedSupplier.totalPrice.toFixed(2)}` : '-' }}
          </template>
          <template v-if="column.dataIndex === 'minOrderQuantity'">
            {{ record.selectedSupplier ? record.selectedSupplier.minOrderQuantity : '-' }}
          </template>
          <template v-if="column.dataIndex === 'delivery'">
            {{ record.selectedSupplier ? record.selectedSupplier.promisedDelivery : '-' }}
          </template>
          <template v-if="column.dataIndex === 'rfqTime'">
            {{ rfqData.status === 'notStarted' ? '-' : formatDateTime(record.rfqTime) }}
          </template>
          <template v-if="column.dataIndex === 'endTime'">
            {{ rfqData.status === 'inProgress' ? '-' : formatDateTime(record.endTime) }}
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'quoteProgress'">
            <div class="quote-progress">
              <span>供应商数：{{ getSupplierCount(record) }}，</span>
              <span>已报价：<span class="quoted-count">{{ getQuotedCount(record) }}</span>，</span>
              <span>已拒绝：<span class="rejected-count">{{ getRejectedCount(record) }}</span></span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a v-if="canConfigSupplier" @click="handleConfigSupplier(record)">配置供应商</a>
              <a v-if="canStartInquiry" @click="handleStartInquiry(record)">启动询价</a>
              <a v-if="canToPurchaseOrder" @click="handleToPurchaseOrder(record)">转采购单</a>
              <a v-if="canReInquiry" @click="handleReInquiry(record)">再次询价</a>
              <a v-if="canCancel" @click="handleCancel(record)" class="danger-link">取消</a>
              <a @click="handleViewHistory(record)">询价历史</a>
            </a-space>
          </template>
        </template>

        <!-- 嵌套表格 - 供应商信息 -->
        <template #expandedRowRender="{ record }">
          <div style="margin: 12px">
            <a-table 
              size="small" 
              :columns="getSupplierColumns(record.acceptAlternative)" 
              :data-source="record.suppliers" 
              :pagination="false" 
              row-key="id" 
              bordered
            >
              <template #bodyCell="{ column, record: supplierRecord }">
                <template v-if="column.dataIndex === 'alternativeBrand'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : (supplierRecord.alternativeBrand || '-') }}
                </template>
                <template v-if="column.dataIndex === 'alternativeModel'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : (supplierRecord.alternativeModel || '-') }}
                </template>
                <template v-if="column.dataIndex === 'price'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : (supplierRecord.price ? `¥${supplierRecord.price.toFixed(2)}` : '') }}
                </template>
                <template v-if="column.dataIndex === 'totalPrice'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : (supplierRecord.totalPrice ? `¥${supplierRecord.totalPrice.toFixed(2)}` : '') }}
                </template>
                <template v-if="column.dataIndex === 'promisedDelivery'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : supplierRecord.promisedDelivery }}
                </template>
                <template v-if="column.dataIndex === 'validityPeriod'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : supplierRecord.validityPeriod }}
                </template>
                <template v-if="column.dataIndex === 'quoteTime'">
                  {{ supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' ? '-' : formatDateTime(supplierRecord.quoteTime) }}
                </template>
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="getSupplierStatusColor(supplierRecord.status)">
                    {{ getSupplierStatusText(supplierRecord.status) }}
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <a-button 
                    type="link" 
                    :type="supplierRecord.isSelected ? 'primary' : 'default'" 
                    :disabled="supplierRecord.isSelected || supplierRecord.status === 'pending' || supplierRecord.status === 'rejected' || supplierRecord.quoteType === 'external'" 
                    @click="handleSelectSupplier(supplierRecord, record)"
                  >
                    {{ supplierRecord.isSelected ? '已选择' : '选择' }}
                  </a-button>
                </template>
              </template>
            </a-table>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- Fixed action buttons at the bottom right -->
    <div class="bottom-actions">
      <a-space>
        <a-button v-if="canStartInquiry" type="primary">启动询价</a-button>
        <a-button v-if="canToPurchaseOrder" type="primary">转采购单</a-button>
        <a-button v-if="canReInquiry">再次询价</a-button>
        <a-button v-if="canCancel" danger>取消询价</a-button>
      </a-space>
    </div>

    <!-- 供应商配置弹窗 -->
    <SupplierModal 
      :title="'配置供应商'" 
      :visible="supplierModalVisible" 
      :confirm-loading="supplierModalLoading" 
      @update:visible="supplierModalVisible = $event" 
      @ok="handleSupplierSelect" 
      @cancel="handleSupplierModalCancel" 
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { DownOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
import SupplierModal from './component/supplierModal.vue';

const route = useRoute();
const router = useRouter();
const rfqNo = ref(route.params.rfqNo);

// 询价单数据
const rfqData = ref({
  id: '',
  rfqNo: 'RFQ-2023-0001',
  status: 'inProgress',
  createTime: '2023-10-15 09:30:00',
  rfqTime: '2023-10-15 10:00:00',
  deadline: '2023-10-25 18:00:00',
  endTime: '2023-10-23 16:30:00',
  creator: '张三',
  contactPhone: '13800138000',
  materialModelCount: 4,
  materials: []
});

// 表格展开状态
const expandedRowKeys = ref([]);
const selectedRowKeys = ref([]);

// 供应商配置相关
const supplierModalVisible = ref(false);
const supplierModalLoading = ref(false);
const currentRecord = ref(null);

// 物料表格列定义
const materialColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180, fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', width: 180, fixed: 'left' },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '接受平替', dataIndex: 'acceptAlternative', key: 'acceptAlternative', width: 100 },
  { title: '平替品牌', dataIndex: 'alternativeBrand', key: 'alternativeBrand', width: 120 },
  { title: '平替型号', dataIndex: 'alternativeModel', key: 'alternativeModel', width: 120 },
  { title: '单价 (¥)', dataIndex: 'unitPrice', key: 'unitPrice', width: 100 },
  { title: '总价 (¥)', dataIndex: 'totalPrice', key: 'totalPrice', width: 120 },
  { title: '最小起订量', dataIndex: 'minOrderQuantity', key: 'minOrderQuantity', width: 100 },
  { title: '交期', dataIndex: 'delivery', key: 'delivery', width: 100 },
  { title: '期望交期', dataIndex: 'expectedDelivery', key: 'expectedDelivery', width: 100 },
  { title: '询价时间', dataIndex: 'rfqTime', key: 'rfqTime', width: 150 },
  { title: '结束时间', dataIndex: 'endTime', key: 'endTime', width: 150 },
  { title: '截止时间', dataIndex: 'deadline', key: 'deadline', width: 150 },
  { title: '询价状态', dataIndex: 'status', key: 'status', width: 100 },
  {
    title: '报价进度',
    dataIndex: 'quoteProgress',
    key: 'quoteProgress',
    width: 150,
    customRender: ({ record }) => {
      return getQuoteProgressDisplay(record);
    }
  },

  { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 275 },
];

// 获取供应商表格列定义
const getSupplierColumns = (acceptAlternative) => {
  const baseColumns = [];
  
  // 添加供应商列（最前面）
  baseColumns.push({
    title: '供应商',
    dataIndex: 'supplierName',
    key: 'supplierName',
    width: 150,
    customRender: ({ text, record }) => {
      return text || record.name || '-';
    },
  });
  
  if (acceptAlternative) {
    baseColumns.push(
      { title: '平替品牌', dataIndex: 'alternativeBrand', key: 'alternativeBrand', width: 120 },
      { title: '平替型号', dataIndex: 'alternativeModel', key: 'alternativeModel', width: 120 }
    );
  }
  
  baseColumns.push(
    { title: '报价 (¥)', dataIndex: 'price', key: 'price', width: 100 },
    { title: '总价 (¥)', dataIndex: 'totalPrice', key: 'totalPrice', width: 100 },
    { title: '最小起订量', dataIndex: 'minOrderQuantity', key: 'minOrderQuantity', width: 150 },
    { title: '承诺交期', dataIndex: 'promisedDelivery', key: 'promisedDelivery', width: 120 },
    { title: '有效期', dataIndex: 'validityPeriod', key: 'validityPeriod', width: 120 },
    { title: '报价时间', dataIndex: 'quoteTime', key: 'quoteTime', width: 150 },
    { title: '报价状态', dataIndex: 'status', key: 'status', width: 100 },
    {
      title: '报价类型',
      dataIndex: 'quoteType',
      key: 'quoteType',
      width: 100,
      customRender: ({ text }) => {
        return text === 'platform' ? '平台报价' : '外部报价';
      },
    },
    { title: '操作', dataIndex: 'action', key: 'action', width: 100 }
  );
  
  return baseColumns;
};

// 权限计算
const canConfigSupplier = computed(() => rfqData.value.status === 'notStarted');
const canStartInquiry = computed(() => rfqData.value.status === 'notStarted');
const canToPurchaseOrder = computed(() => rfqData.value.status === 'accepted');
const canReInquiry = computed(() => ['accepted', 'expired', 'invalid', 'cancelled'].includes(rfqData.value.status));
const canCancel = computed(() => rfqData.value.status === 'inProgress');

// 工具方法
const goBack = () => {
  router.go(-1);
};

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return dateTimeStr;
};

const getStatusText = (status) => {
  const statusMap = {
    notStarted: '未开始',
    inProgress: '询价中',
    accepted: '已采纳',
    expired: '已过期',
    invalid: '已失效',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    notStarted: 'default',
    inProgress: 'blue',
    accepted: 'green',
    expired: 'orange',
    invalid: 'red',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

const getSupplierStatusColor = (status) => {
  const map = {
    pending: 'blue',
    quoted: 'green',
    rejected: 'red',
    expired: 'orange',
  };
  return map[status] || 'default';
};

const getSupplierStatusText = (status) => {
  const map = {
    pending: '待报价',
    quoted: '已报价',
    rejected: '已拒绝',
    expired: '已过期',
  };
  return map[status] || '未知';
};

// 计算报价进度显示
const getQuoteProgressDisplay = (record) => {
  if (!record.suppliers || record.suppliers.length === 0) {
    return '供应商数：0，已报价：0，已拒绝：0';
  }

  const totalSuppliers = record.suppliers.length;
  const quotedCount = record.suppliers.filter(s => s.status === 'quoted').length;
  const rejectedCount = record.suppliers.filter(s => s.status === 'rejected').length;

  return `供应商数：${totalSuppliers}，已报价：${quotedCount}，已拒绝：${rejectedCount}`;
};

// 获取供应商总数
const getSupplierCount = (record) => {
  return record.suppliers ? record.suppliers.length : 0;
};

// 获取已报价数量
const getQuotedCount = (record) => {
  return record.suppliers ? record.suppliers.filter(s => s.status === 'quoted').length : 0;
};

// 获取已拒绝数量
const getRejectedCount = (record) => {
  return record.suppliers ? record.suppliers.filter(s => s.status === 'rejected').length : 0;
};

const getTotalQuantity = () => {
  return rfqData.value.materials.reduce((total, item) => total + item.quantity, 0);
};

const getSelectedTotalPrice = () => {
  const selectedMaterials = rfqData.value.materials.filter((item) => selectedRowKeys.value.includes(item.id));
  const totalPrice = selectedMaterials.reduce((sum, item) => {
    const price = item.selectedSupplier ? item.selectedSupplier.totalPrice : 0;
    return sum + price;
  }, 0);
  return `¥${totalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// 表格事件处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value.push(record.id);
  } else {
    const index = expandedRowKeys.value.indexOf(record.id);
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1);
    }
  }
};

// 供应商选择
const handleSelectSupplier = (supplierRecord, parentRecord) => {
  if (parentRecord) {
    parentRecord.suppliers.forEach((supplier) => {
      supplier.isSelected = false;
    });

    const supplier = parentRecord.suppliers.find((s) => s.id === supplierRecord.id);
    if (supplier) {
      supplier.isSelected = true;
      parentRecord.selectedSupplier = supplier;
      
      if (parentRecord.acceptAlternative && supplier.alternativeBrand && supplier.alternativeModel) {
        parentRecord.alternativeBrand = supplier.alternativeBrand;
        parentRecord.alternativeModel = supplier.alternativeModel;
      }
      
      message.success(`已选择供应商：${supplier.name}`);
    }
  }
};

// 操作方法
const handleConfigSupplier = (record) => {
  currentRecord.value = record;
  supplierModalVisible.value = true;
};

const handleStartInquiry = (record) => {
  message.info(`启动询价：${record.name}`);
};

const handleToPurchaseOrder = (record) => {
  message.info(`转采购单：${record.name}`);
};

const handleReInquiry = (record) => {
  message.info(`再次询价：${record.name}`);
};

const handleCancel = (record) => {
  message.info(`取消询价：${record.name}`);
};

const handleViewHistory = (record) => {
  message.info(`查看询价历史：${record.name}`);
};

const handleBatchOperation = ({ key }) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的物料');
    return;
  }

  const selectedRecords = rfqData.value.materials.filter((item) => selectedRowKeys.value.includes(item.id));

  switch (key) {
    case 'configSupplier':
      message.info(`批量配置供应商：${selectedRecords.length}个物料`);
      break;
    case 'startInquiry':
      message.info(`批量启动询价：${selectedRecords.length}个物料`);
      break;
    case 'toPurchaseOrder':
      message.info(`批量转采购单：${selectedRecords.length}个物料`);
      break;
    case 'cancel':
      message.info(`批量取消：${selectedRecords.length}个物料`);
      break;
  }
};

const handleSmartAllocation = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要分配的物料');
    return;
  }
  message.info('智能分配供应商');
};

const handleSupplierSelect = (selectedSuppliers) => {
  if (currentRecord.value && selectedSuppliers && selectedSuppliers.length > 0) {
    selectedSuppliers.forEach((supplier) => {
      const existingIndex = currentRecord.value.suppliers.findIndex((s) => s.id === supplier.id);

      if (existingIndex === -1) {
        const newSupplier = {
          id: supplier.id,
          name: supplier.name,
          supplierName: supplier.name, // 新添加的供应商名称
          price: null,
          totalPrice: null,
          minOrderQuantity: 1,
          promisedDelivery: '',
          validityPeriod: '',
          quoteTime: '',
          status: 'pending',
          isSelected: false,
          quoteType: 'platform', // 新添加的供应商默认为平台报价
        };

        currentRecord.value.suppliers.push(newSupplier);
      }
    });

    supplierModalVisible.value = false;
    message.success(`已成功添加${selectedSuppliers.length}个供应商`);
  }
};

const handleSupplierModalCancel = () => {
  supplierModalVisible.value = false;
};

// 获取询价单详情
const fetchRfqDetails = async () => {
  try {
    // 模拟API调用，实际项目中根据 rfqNo 获取对应的询价单数据
    console.log('获取询价单详情，询价单号：', rfqNo.value);
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 更新询价单基本信息（实际项目中这里应该从API获取）
    rfqData.value.rfqNo = rfqNo.value;

    // 模拟物料数据
    rfqData.value.materials = Array.from({ length: 4 }).map((_, index) => {
      const quantity = Math.floor(Math.random() * 100 + 10);
      const status = index % 4 === 0 ? 'notStarted' : index % 4 === 1 ? 'inProgress' : index % 4 === 2 ? 'accepted' : 'expired';

      const suppliers = Array.from({ length: 3 }).map((_, sIndex) => {
        const supplierStatus = sIndex % 4 === 0 ? 'pending' : sIndex % 4 === 1 ? 'quoted' : sIndex % 4 === 2 ? 'rejected' : 'expired';
        const supplierPrice = Math.floor(Math.random() * 1000 + 100);
        
        const hasAlternative = sIndex === 1;
        const alternativeBrands = ['兼容品牌A', '兼容品牌B', '通用品牌C'];
        const alternativeModels = ['ALT-001', 'COMP-002', 'GEN-003'];

        // 模拟报价类型和供应商名称
        const quoteType = sIndex % 2 === 0 ? 'platform' : 'external';
        const supplierNames = {
          platform: ['严选供应商', '深圳市电子科技有限公司', '北京智能制造有限公司'],
          external: ['上海精密器件有限公司', '广州电子元件供应商', '天津工业设备公司']
        };

        return {
          id: `supplier-${index}-${sIndex}`,
          name: `供应商 ${sIndex + 1}`,
          supplierName: supplierNames[quoteType][sIndex % supplierNames[quoteType].length],
          price: supplierPrice,
          totalPrice: supplierPrice * quantity,
          minOrderQuantity: Math.floor(Math.random() * 100 + 10),
          promisedDelivery: `${Math.floor(Math.random() * 30 + 15)}天`,
          validityPeriod: '2023-11-05',
          quoteTime: '2023-10-16 10:30:00',
          status: supplierStatus,
          isSelected: false,
          quoteType: quoteType, // 'platform' 或 'external'
          alternativeBrand: hasAlternative && supplierStatus === 'quoted' ? alternativeBrands[sIndex % alternativeBrands.length] : null,
          alternativeModel: hasAlternative && supplierStatus === 'quoted' ? alternativeModels[sIndex % alternativeModels.length] : null,
        };
      });

      let selectedSupplier = null;
      let initialAlternativeBrand = null;
      let initialAlternativeModel = null;
      
      if (status === 'accepted') {
        const quotedSupplier = suppliers.find((s) => s.status === 'quoted');
        if (quotedSupplier) {
          quotedSupplier.isSelected = true;
          selectedSupplier = quotedSupplier;
          
          if (index % 3 === 0 && quotedSupplier.alternativeBrand && quotedSupplier.alternativeModel) {
            initialAlternativeBrand = quotedSupplier.alternativeBrand;
            initialAlternativeModel = quotedSupplier.alternativeModel;
          }
        }
      }

      return {
        id: `material-${index}`,
        name: `测试物料 ${index + 1}`,
        model: `MODEL-${100 + index}`,
        brand: index % 3 === 0 ? 'A' : index % 3 === 1 ? 'B' : 'C',
        quantity: quantity,
        expectedDelivery: '30天',
        rfqTime: status === 'notStarted' ? '' : '2023-10-15 10:00:00',
        endTime: '2023-10-23 16:30:00',
        deadline: '2023-10-25 18:00:00',
        status: status,
        suppliers: suppliers,
        selectedSupplier: selectedSupplier,
        acceptAlternative: index % 3 === 0,
        alternativeBrand: initialAlternativeBrand,
        alternativeModel: initialAlternativeModel,
      };
    });

  } catch (error) {
    message.error('获取询价单详情失败');
  }
};

// 生命周期
onMounted(() => {
  fetchRfqDetails();
});
</script>

<style lang="less" scoped>
.rfq-detail-container {
  min-height: 100vh;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 4px;

  .ant-card-head {
    background-color: #fafafa;
  }
}

.info-item {
  display: flex;
  margin-bottom: 16px;

  .label {
    color: rgba(0, 0, 0, 0.65);
    min-width: 90px;
    flex-shrink: 0;
  }

  .value {
    flex: 1;
    font-weight: 500;

    &.important {
      color: #f94c30;
      font-weight: 600;
    }
  }
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff4f0;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffa39e;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

.danger-link {
  color: #ff4d4f;
}

.quote-progress {
  font-size: 12px;
}

.quoted-count {
  color: #52c41a;
  font-weight: 500;
}

.rejected-count {
  color: #ff4d4f;
  font-weight: 500;
}

.bottom-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  margin-top: 24px;
  border-top: 1px solid #e8e8e8;
}
</style>
