<template>
  <div class="supplier-index-container">
    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-card">
        <!-- All Filters Panel -->
        <div class="filters-panel">
          <a-row :gutter="16">
            <!-- Location Filters -->

            <a-col :span="6">
              <a-form-item label="供应商名称">
                <a-input v-model:value="searchForm.keyword" placeholder="请输入供应商名称" allow-clear />
              </a-form-item>
            </a-col>
            <!-- Location Filters -->
            <a-col :span="6">
              <a-form-item label="地区筛选">
                <a-cascader v-model:value="searchForm.location" :options="locationOptions" placeholder="选择省份/城市" change-on-select allow-clear />
              </a-form-item>
            </a-col>

            <!-- Industry Category -->
            <a-col :span="6">
              <a-form-item label="行业类别">
                <a-select v-model:value="searchForm.industry" placeholder="选择行业类别" allow-clear mode="multiple">
                  <a-select-option v-for="industry in industryOptions" :key="industry.value" :value="industry.value">
                    {{ industry.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- Company Size -->
            <a-col :span="6">
              <a-form-item label="企业规模">
                <a-select v-model:value="searchForm.companySize" placeholder="按注册资本" allow-clear>
                  <a-select-option v-for="size in companySizeOptions" :key="size.value" :value="size.value">
                    {{ size.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- Establishment Year -->
            <a-col :span="6">
              <a-form-item label="成立年限">
                <a-range-picker v-model:value="searchForm.establishmentYear" picker="year" placeholder="选择年份范围" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="产品类别">
                <a-tree-select v-model:value="searchForm.productCategories" :tree-data="productCategoryTree" placeholder="选择产品类别" multiple tree-checkable allow-clear :max-tag-count="2" />
              </a-form-item>
            </a-col>
            <!-- Supplier Type -->
            <a-col :span="6">
              <a-form-item label="供应商类型">
                <a-radio-group v-model:value="searchForm.supplierType">
                  <a-radio value="">全部</a-radio>
                  <a-radio value="recommended">推荐品牌商</a-radio>
                  <a-radio value="normal">普通供应商</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col>
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="loading">
                  <i class="fas fa-search"></i>
                  搜索
                </a-button>
                <a-button @click="handleResetFilters">
                  <i class="fas fa-undo"></i>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>

    <!-- Table Operations -->
    <div class="table-operations">
      <a-space>
        <a-button type="primary" @click="handleExportSuppliers" :disabled="selectedSuppliers.length === 0">
          <i class="fas fa-download"></i>
          导出
          <span v-if="selectedSuppliers.length > 0">({{ selectedSuppliers.length }})</span>
        </a-button>
        <a-button @click="handleStartComparison" :disabled="selectedSuppliers.length < 2 || selectedSuppliers.length > 3">
          <i class="fas fa-balance-scale"></i>
          对比
          <span v-if="selectedSuppliers.length > 0">({{ selectedSuppliers.length }})</span>
        </a-button>
      </a-space>

      <a-space>
        <!-- View Mode Toggle -->
        <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
          <a-radio-button value="list">
            <i class="fas fa-list"></i>
            列表
          </a-radio-button>
          <a-radio-button value="card">
            <i class="fas fa-th-large"></i>
            卡片
          </a-radio-button>
          <a-radio-button value="map">
            <i class="fas fa-map"></i>
            地图
          </a-radio-button>
        </a-radio-group>
      </a-space>
    </div>

    <!-- Selection Summary -->
    <div class="selection-summary">
      <div></div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedSuppliers.length }}</a-tag> 家供应商</span
        >
      </div>
    </div>

    <!-- Active Filters Display -->
    <div class="active-filters" v-if="activeFilters.length > 0">
      <span class="active-filters-label">当前筛选：</span>
      <a-tag v-for="filter in activeFilters" :key="filter.key" closable @close="handleRemoveFilter(filter.key)" color="#f94c30">
        {{ filter.label }}
      </a-tag>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Right Content Area -->
      <div class="right-content">
        <!-- List View -->
        <div v-if="viewMode === 'list'" class="list-view">
          <a-table :columns="tableColumns" :data-source="supplierList" :pagination="tablePagination" :loading="loading" :row-selection="rowSelection" row-key="id" @change="handleTableChange" class="supplier-table" size="small" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="supplier-name-cell">
                  <a @click="handleViewSupplierDetail(record)" style="color: #f94c30">{{ record.companyName }}</a>
                  <a-tag v-if="record.supplierType === 'recommended'" color="gold">
                    <i class="fas fa-crown"></i>
                    推荐品牌商
                  </a-tag>
                  <i v-if="record.isBookmarked" class="fas fa-heart bookmark-icon" title="已收藏"></i>
                </div>
              </template>

              <template v-if="column.key === 'location'">
                {{ record.region }}
              </template>

              <template v-if="column.key === 'registeredCapital'">
                {{ record.registeredCapital }}
              </template>

              <template v-if="column.key === 'establishmentYear'"> {{ formatTimestamp(record.establishmentTime) }}年 </template>

              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="handleViewSupplierDetail(record)">
                    <i class="fas fa-eye"></i>
                    查看详情
                  </a-button>
                  <a-dropdown>
                    <a-button type="link" size="small">
                      更多
                      <i class="fas fa-caret-down"></i>
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="handleAddToComparison(record)">
                          <i class="fas fa-balance-scale"></i>
                          对比
                        </a-menu-item>
                        <a-menu-item @click="handleToggleBookmark(record)">
                          <i :class="record.isBookmarked ? 'fas fa-heart' : 'far fa-heart'"></i>
                          {{ record.isBookmarked ? '取消收藏' : '收藏' }}
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>

        <!-- Card View -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <div class="supplier-cards-container">
            <div v-for="supplier in supplierList" :key="supplier.id" class="supplier-card-wrapper">
              <a-card class="supplier-card" :class="{ selected: selectedSuppliers.includes(supplier.id) }" @click="handleSelectSupplier(supplier.id)">
                <div class="card-content">
                  <!-- Header with logo, name, and actions -->
                  <div class="supplier-header">
                    <a-checkbox :checked="selectedSuppliers.includes(supplier.id)" @click.stop="handleSelectSupplier(supplier.id)" class="card-checkbox" />

                    <div class="supplier-logo">
                      <img v-if="supplier.logoUrl" :src="supplier.logoUrl" :alt="supplier.companyName" @error="handleLogoError" />
                      <div v-else class="default-logo">
                        <i class="fas fa-building"></i>
                      </div>
                    </div>

                    <div class="supplier-name-section">
                      <div class="supplier-name">{{ supplier.companyName }}</div>
                      <div class="supplier-tags">
                        <a-tag v-if="supplier.supplierType === 'recommended'" color="gold" class="recommended-tag" size="small">
                          <i class="fas fa-crown"></i>
                          推荐品牌商
                        </a-tag>
                        <a-tag color="blue" size="small">{{ supplier.factoryLevel }}</a-tag>
                      </div>
                    </div>

                    <div class="header-actions">
                      <a-button type="text" size="small" @click.stop="handleToggleBookmark(supplier)" class="bookmark-btn" :class="{ bookmarked: supplier.isBookmarked }" :title="supplier.isBookmarked ? '取消收藏' : '收藏'">
                        <!-- <i :class="supplier.isBookmarked ? 'fas fa-heart' : 'far fa-heart'"></i> -->
                        <StarOutlined v-if="!supplier.isBookmarked"/>
                        <StarFilled v-else/>
                      </a-button>
                    </div>
                  </div>

                  <!-- Location -->
                  <div class="supplier-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>{{ supplier.region }}</span>
                  </div>

                  <!-- 详细信息区域 -->
                  <div class="supplier-details">
                    <div class="detail-row">
                      <span class="detail-label">行业类别</span>
                      <span class="detail-value">{{ supplier.industry }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">成立时间</span>
                      <span class="detail-value">{{ formatTimestamp(supplier.establishmentTime) }}年</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">注册资本</span>
                      <span class="detail-value">{{ supplier.registeredCapital }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">员工规模</span>
                      <span class="detail-value">{{ supplier.socialSecurityCount }}人</span>
                    </div>
                  </div>

                  <!-- 产品关键词 -->
                  <div class="supplier-products">
                    <div class="products-label">主营产品</div>
                    <div class="products-tags">
                      <a-tag v-for="keyword in JSON.parse(supplier.searchKeywords).slice(0, 3)" :key="keyword" size="small" color="blue">
                        {{ keyword }}
                      </a-tag>
                      <span v-if="JSON.parse(supplier.searchKeywords).length > 3" class="more-products"> +{{ JSON.parse(supplier.searchKeywords).length - 3 }} </span>
                    </div>
                  </div>

                  <!-- Action buttons -->
                  <div class="card-actions">
                    <a-button type="primary" @click.stop="handleViewSupplierDetail(supplier)" class="details-btn">
                      <i class="fas fa-eye"></i>
                      查看详情
                    </a-button>
                  </div>
                </div>
              </a-card>
            </div>
          </div>

          <!-- Card View Pagination -->
          <div class="card-pagination">
            <a-pagination v-model:current="pagination.current" v-model:page-size="pagination.pageSize" :total="pagination.total" :show-size-changer="true" :show-quick-jumper="true" :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`" @change="handlePaginationChange" />
          </div>
        </div>

        <!-- Map View -->
        <div v-else-if="viewMode === 'map'" class="map-view">
          <div class="map-container">
            <div id="supplier-map" class="map-canvas"></div>

            <!-- Map Controls -->
            <div class="map-controls">
              <a-card size="small" class="map-legend">
                <div class="legend-item">
                  <span class="legend-dot recommended"></span>
                  <span>推荐品牌商</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot normal"></span>
                  <span>普通供应商</span>
                </div>
              </a-card>

              <a-card size="small" class="map-stats">
                <a-statistic title="当前区域供应商" :value="mapStats.currentRegion" />
                <a-statistic title="推荐品牌商" :value="mapStats.recommended" />
                <a-statistic title="普通供应商" :value="mapStats.normal" />
              </a-card>
            </div>

            <!-- Map Supplier Info Panel -->
            <div v-if="selectedMapSupplier" class="map-supplier-panel">
              <a-card size="small">
                <template #title>
                  {{ selectedMapSupplier.name }}
                  <a-button type="text" size="small" @click="selectedMapSupplier = null" style="float: right">
                    <i class="fas fa-times"></i>
                  </a-button>
                </template>

                <div class="map-supplier-info">
                  <p><strong>地址：</strong>{{ selectedMapSupplier.address }}</p>
                  <p><strong>行业：</strong>{{ selectedMapSupplier.industry }}</p>
                  <p><strong>成立时间：</strong>{{ selectedMapSupplier.establishmentYear }}年</p>
                  <p><strong>注册资本：</strong>{{ formatCurrency(selectedMapSupplier.registeredCapital) }}</p>
                </div>

                <template #actions>
                  <a-button type="primary" size="small" @click="handleViewSupplierDetail(selectedMapSupplier)"> 查看详情 </a-button>
                  <a-button size="small" @click="handleToggleBookmark(selectedMapSupplier)">
                    <i :class="selectedMapSupplier.isBookmarked ? 'fas fa-heart' : 'far fa-heart'"></i>
                    {{ selectedMapSupplier.isBookmarked ? '取消收藏' : '收藏' }}
                  </a-button>
                </template>
              </a-card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- Supplier Detail Modal -->
    <a-modal v-model:open="showSupplierDetailModal" title="供应商详情" width="800px" :footer="null" class="supplier-detail-modal">
      <div v-if="selectedSupplierDetail" class="supplier-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="企业名称">{{ selectedSupplierDetail.companyName }}</a-descriptions-item>
          <a-descriptions-item label="供应商类型">
            <a-tag v-if="selectedSupplierDetail.supplierType === 'recommended'" color="gold">
              <i class="fas fa-crown"></i>
              推荐品牌商
            </a-tag>
            <span v-else>普通供应商</span>
          </a-descriptions-item>
          <a-descriptions-item label="卖家昵称">{{ selectedSupplierDetail.sellerNickname }}</a-descriptions-item>
          <a-descriptions-item label="店铺名称">{{ selectedSupplierDetail.shopName }}</a-descriptions-item>
          <a-descriptions-item label="所在地区">{{ selectedSupplierDetail.region }}</a-descriptions-item>
          <a-descriptions-item label="行业类别">{{ selectedSupplierDetail.industry }}</a-descriptions-item>
          <a-descriptions-item label="工厂等级">{{ selectedSupplierDetail.factoryLevel }}</a-descriptions-item>
          <a-descriptions-item label="等级分数">{{ selectedSupplierDetail.levelScore }}分</a-descriptions-item>
          <a-descriptions-item label="成立时间">{{ formatTimestamp(selectedSupplierDetail.establishmentTime) }}年</a-descriptions-item>
          <a-descriptions-item label="注册资本">{{ selectedSupplierDetail.registeredCapital }}</a-descriptions-item>
          <a-descriptions-item label="社保人数">{{ selectedSupplierDetail.socialSecurityCount }}人</a-descriptions-item>
          <a-descriptions-item label="服务年限">{{ selectedSupplierDetail.serviceYears }}年</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ selectedSupplierDetail.phone }}</a-descriptions-item>
          <a-descriptions-item label="企业邮箱">{{ selectedSupplierDetail.email }}</a-descriptions-item>
          <a-descriptions-item label="企业网站">
            <a :href="selectedSupplierDetail.website" target="_blank">{{ selectedSupplierDetail.website }}</a>
          </a-descriptions-item>
          <a-descriptions-item label="法定代表人">{{ selectedSupplierDetail.legalRepresentative }}</a-descriptions-item>
          <a-descriptions-item label="注册地址" :span="2">{{ selectedSupplierDetail.registeredAddress }}</a-descriptions-item>
          <a-descriptions-item label="经营范围" :span="2">{{ selectedSupplierDetail.businessScope }}</a-descriptions-item>
          <a-descriptions-item label="搜索关键词" :span="2">
            <a-tag v-for="keyword in JSON.parse(selectedSupplierDetail.searchKeywords)" :key="keyword">
              {{ keyword }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="公司标签" :span="2">
            <a-tag v-for="tag in JSON.parse(selectedSupplierDetail.companyTags)" :key="tag" color="blue">
              {{ tag }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <div class="detail-actions">
          <a-space>
            <a-button @click="handleAddToComparison(selectedSupplierDetail)">
              <i class="fas fa-balance-scale"></i>
              对比
            </a-button>
            <a-button @click="handleToggleBookmark(selectedSupplierDetail)">
              <i :class="selectedSupplierDetail.isBookmarked ? 'fas fa-heart' : 'far fa-heart'"></i>
              {{ selectedSupplierDetail.isBookmarked ? '取消收藏' : '收藏' }}
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- Comparison Modal -->
    <a-modal v-model:open="showComparisonModal" title="供应商对比分析" width="1000px" :footer="null" class="comparison-modal">
      <div class="comparison-content">
        <a-table :columns="comparisonColumns" :data-source="comparisonData" :pagination="false" bordered />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleFilled, StarFilled, StarOutlined } from '@ant-design/icons-vue';

// ==================== Reactive Data ====================

// Loading states
const loading = ref(false);

// View mode
const viewMode = ref('card'); // 'list', 'card', 'map'
const sortBy = ref('relevance');

// Search and filter form
const searchForm = reactive({
  keyword: '',
  location: [],
  industry: [],
  companySize: '',
  establishmentYear: [],
  productCategories: [],
  businessType: [],
  supplierType: '',
  quickFilter: '',
});

// UI states
const showSupplierDetailModal = ref(false);
const showComparisonModal = ref(false);

// Selection states
const selectedSuppliers = ref([]);
const selectedSupplierDetail = ref(null);
const selectedMapSupplier = ref(null);

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0,
});

// Table pagination (for list view)
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
}));

// Quick filters
const quickFilters = ref([
  { key: 'all', label: '全部', icon: 'fas fa-list' },
  { key: 'recommended', label: '推荐品牌商', icon: 'fas fa-crown' },
  { key: 'mechanical', label: '机械零件', icon: 'fas fa-cog' },
  { key: 'electrical', label: '电气元件', icon: 'fas fa-bolt' },
  { key: 'hydraulic', label: '液压组件', icon: 'fas fa-tint' },
]);

// Filter options
const locationOptions = ref([
  {
    value: 'guangdong',
    label: '广东省',
    children: [
      { value: 'shenzhen', label: '深圳市' },
      { value: 'guangzhou', label: '广州市' },
      { value: 'dongguan', label: '东莞市' },
    ],
  },
  {
    value: 'jiangsu',
    label: '江苏省',
    children: [
      { value: 'suzhou', label: '苏州市' },
      { value: 'nanjing', label: '南京市' },
      { value: 'wuxi', label: '无锡市' },
    ],
  },
  {
    value: 'zhejiang',
    label: '浙江省',
    children: [
      { value: 'hangzhou', label: '杭州市' },
      { value: 'ningbo', label: '宁波市' },
      { value: 'wenzhou', label: '温州市' },
    ],
  },
]);

const industryOptions = ref([
  { value: 'mechanical', label: '机械制造' },
  { value: 'electronics', label: '电子电气' },
  { value: 'automation', label: '自动化设备' },
  { value: 'precision', label: '精密加工' },
  { value: 'materials', label: '新材料' },
  { value: 'hydraulic', label: '液压气动' },
]);

const companySizeOptions = ref([
  { value: 'small', label: '小型企业 (< 500万)' },
  { value: 'medium', label: '中型企业 (500万-5000万)' },
  { value: 'large', label: '大型企业 (> 5000万)' },
]);

const productCategoryTree = ref([
  {
    title: '机械零件',
    value: 'mechanical',
    key: 'mechanical',
    children: [
      { title: '轴承', value: 'bearing', key: 'bearing' },
      { title: '齿轮', value: 'gear', key: 'gear' },
      { title: '联轴器', value: 'coupling', key: 'coupling' },
    ],
  },
  {
    title: '电气元件',
    value: 'electrical',
    key: 'electrical',
    children: [
      { title: '传感器', value: 'sensor', key: 'sensor' },
      { title: '电机', value: 'motor', key: 'motor' },
      { title: '控制器', value: 'controller', key: 'controller' },
    ],
  },
  {
    title: '液压组件',
    value: 'hydraulic',
    key: 'hydraulic',
    children: [
      { title: '液压泵', value: 'pump', key: 'pump' },
      { title: '液压缸', value: 'cylinder', key: 'cylinder' },
      { title: '液压阀', value: 'valve', key: 'valve' },
    ],
  },
]);

// Supplier data
const supplierList = ref([]);

// Mock supplier data - 更新为新的字段结构
const mockSupplierData = [
  {
    id: '1',
    supplierId: 'SUP001',
    companyName: '深圳精密机械有限公司',
    sellerId: 'SELLER001',
    sellerNickname: '精密机械专家',
    shopName: '深圳精密机械旗舰店',
    region: '广东省深圳市',
    imageUrl: 'https://example.com/supplier1.jpg',
    logoUrl: 'https://via.placeholder.com/80x80/1890ff/ffffff?text=深圳精密', // 公司logo
    factoryLevel: 'A级',
    levelScore: 95,
    serviceYears: 8,
    wangwangResponseScore: 92,
    goodRatingScore: 98,
    complianceScore: 96,
    buyerIntention: '高',
    categoryName: '机械零件',
    homepageLink: 'https://shop.example.com/supplier1',
    searchKeywords: JSON.stringify(['精密轴承', '齿轮箱', '联轴器']),
    tianyanchaCompanyId: 'TYC001',
    tianyanchaCompanyName: '深圳精密机械有限公司',
    registrationNumber: '91440300123456789X',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91440300123456789X',
    establishmentTime: 1420070400000, // 2015-01-01的时间戳
    registeredCapital: '500万人民币',
    companyType: '有限责任公司',
    organizationCode: '12345678-9',
    legalRepresentative: '张三',
    businessScope: '精密机械零件制造、销售；机械设备技术开发',
    registeredAddress: '深圳市南山区科技园南区',
    phone: '0755-12345678',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.szjmjx.com',
    approvalDate: 1420070400000,
    businessTermFrom: 1420070400000,
    businessTermTo: 1735660800000, // 2025-01-01
    socialSecurityCount: 120,
    formerName: '',
    industry: '机械制造',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91440300123456789X',
    companyAbbreviation: '深圳精密',
    district: '南山区',
    companyTags: JSON.stringify(['精密制造', '高新技术', '质量认证']),
    supplierType: 'recommended', // 推荐品牌商
    isBookmarked: true, // 收藏状态
  },
  {
    id: '2',
    supplierId: 'SUP002',
    companyName: '苏州电子科技股份有限公司',
    sellerId: 'SELLER002',
    sellerNickname: '电子科技先锋',
    shopName: '苏州电子科技专营店',
    region: '江苏省苏州市',
    imageUrl: 'https://example.com/supplier2.jpg',
    logoUrl: 'https://via.placeholder.com/80x80/52c41a/ffffff?text=苏州电子', // 公司logo
    factoryLevel: 'B级',
    levelScore: 88,
    serviceYears: 11,
    wangwangResponseScore: 89,
    goodRatingScore: 94,
    complianceScore: 91,
    buyerIntention: '中',
    categoryName: '电子元件',
    homepageLink: 'https://shop.example.com/supplier2',
    searchKeywords: JSON.stringify(['传感器', '控制模块', '电机驱动器']),
    tianyanchaCompanyId: 'TYC002',
    tianyanchaCompanyName: '苏州电子科技股份有限公司',
    registrationNumber: '91320500987654321A',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91320500987654321A',
    establishmentTime: 1325376000000, // 2012-01-01
    registeredCapital: '800万人民币',
    companyType: '股份有限公司',
    organizationCode: '98765432-1',
    legalRepresentative: '李四',
    businessScope: '电子产品研发、制造、销售；自动化设备技术服务',
    registeredAddress: '苏州市工业园区星海街',
    phone: '0512-87654321',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.szdzkj.com',
    approvalDate: 1325376000000,
    businessTermFrom: 1325376000000,
    businessTermTo: 1735660800000,
    socialSecurityCount: 200,
    formerName: '苏州电子有限公司',
    industry: '电子电气',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91320500987654321A',
    companyAbbreviation: '苏州电子',
    district: '工业园区',
    companyTags: JSON.stringify(['电子制造', '技术创新', 'ISO认证']),
    supplierType: 'normal', // 普通供应商
    isBookmarked: false,
  },
  {
    id: '3',
    supplierId: 'SUP003',
    companyName: '上海智能制造有限公司',
    sellerId: 'SELLER003',
    sellerNickname: '智能制造专家',
    shopName: '上海智能制造旗舰店',
    region: '上海市浦东新区',
    imageUrl: 'https://example.com/supplier3.jpg',
    logoUrl: '', // 没有logo，将显示默认头像
    factoryLevel: 'A级',
    levelScore: 92,
    serviceYears: 6,
    wangwangResponseScore: 95,
    goodRatingScore: 96,
    complianceScore: 94,
    buyerIntention: '高',
    categoryName: '自动化设备',
    homepageLink: 'https://shop.example.com/supplier3',
    searchKeywords: JSON.stringify(['工业机器人', '自动化产线', '智能控制系统']),
    tianyanchaCompanyId: 'TYC003',
    tianyanchaCompanyName: '上海智能制造有限公司',
    registrationNumber: '91310115123456789Y',
    registrationStatus: '存续',
    unifiedSocialCreditCode: '91310115123456789Y',
    establishmentTime: 1483200000000, // 2017-01-01
    registeredCapital: '1200万人民币',
    companyType: '有限责任公司',
    organizationCode: '12345678-0',
    legalRepresentative: '王五',
    businessScope: '智能制造设备研发、生产、销售；工业自动化技术服务',
    registeredAddress: '上海市浦东新区张江高科技园区',
    phone: '021-12345678',
    email: '<EMAIL>',
    emailList: JSON.stringify(['<EMAIL>', '<EMAIL>']),
    website: 'https://www.shznzz.com',
    approvalDate: 1483200000000,
    businessTermFrom: 1483200000000,
    businessTermTo: 1735660800000,
    socialSecurityCount: 180,
    formerName: '',
    industry: '自动化设备',
    stockName: '',
    stockCode: '',
    formerStockName: '',
    taxpayerIdentificationNumber: '91310115123456789Y',
    companyAbbreviation: '上海智能',
    district: '浦东新区',
    companyTags: JSON.stringify(['智能制造', '工业4.0', '技术领先']),
    supplierType: 'recommended', // 推荐品牌商
    isBookmarked: true,
  },
];

// Comparison
const comparisonList = ref([]);

// Map stats
const mapStats = reactive({
  currentRegion: 0,
  recommended: 0,
  normal: 0,
});

// ==================== Computed Properties ====================

// Active filters count
const activeFiltersCount = computed(() => {
  let count = 0;
  if (searchForm.keyword) count++;
  if (searchForm.location.length > 0) count++;
  if (searchForm.industry.length > 0) count++;
  if (searchForm.companySize) count++;
  if (searchForm.establishmentYear.length > 0) count++;
  if (searchForm.productCategories.length > 0) count++;
  if (searchForm.businessType.length > 0) count++;
  if (searchForm.auditStatus) count++;
  return count;
});

// Active filters display
const activeFilters = computed(() => {
  const filters = [];

  if (searchForm.keyword) {
    filters.push({ key: 'keyword', label: `关键词: ${searchForm.keyword}` });
  }

  if (searchForm.location.length > 0) {
    const locationLabels = searchForm.location.map((loc) => {
      // Find location label from options
      const province = locationOptions.value.find((p) => p.value === loc[0]);
      if (province && loc[1]) {
        const city = province.children.find((c) => c.value === loc[1]);
        return city ? `${province.label} ${city.label}` : province.label;
      }
      return province ? province.label : loc;
    });
    filters.push({ key: 'location', label: `地区: ${locationLabels.join(', ')}` });
  }

  if (searchForm.industry.length > 0) {
    const industryLabels = searchForm.industry.map((ind) => {
      const industry = industryOptions.value.find((i) => i.value === ind);
      return industry ? industry.label : ind;
    });
    filters.push({ key: 'industry', label: `行业: ${industryLabels.join(', ')}` });
  }

  if (searchForm.companySize) {
    const sizeOption = companySizeOptions.value.find((s) => s.value === searchForm.companySize);
    filters.push({ key: 'companySize', label: `规模: ${sizeOption ? sizeOption.label : searchForm.companySize}` });
  }

  if (searchForm.supplierType) {
    const typeLabel = searchForm.supplierType === 'recommended' ? '推荐品牌商' : '普通供应商';
    filters.push({ key: 'supplierType', label: `类型: ${typeLabel}` });
  }

  return filters;
});

// Row selection for table
const rowSelection = computed(() => ({
  selectedRowKeys: selectedSuppliers.value,
  onChange: (selectedRowKeys) => {
    selectedSuppliers.value = selectedRowKeys;
  },
}));

// Table columns
const tableColumns = ref([
  {
    title: '供应商名称',
    dataIndex: 'companyName',
    key: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '所在地区',
    key: 'location',
    width: 120,
  },
  {
    title: '行业类别',
    dataIndex: 'industry',
    key: 'industry',
    width: 100,
  },
  {
    title: '工厂等级',
    dataIndex: 'factoryLevel',
    key: 'factoryLevel',
    width: 100,
  },
  {
    title: '成立时间',
    dataIndex: 'establishmentTime',
    key: 'establishmentYear',
    width: 100,
    sorter: true,
  },
  {
    title: '注册资本',
    key: 'registeredCapital',
    width: 120,
    sorter: true,
  },
  {
    title: '社保人数',
    dataIndex: 'socialSecurityCount',
    key: 'employeeCount',
    width: 100,
    sorter: true,
  },
  {
    title: '服务年限',
    dataIndex: 'serviceYears',
    key: 'serviceYears',
    width: 100,
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
  },
]);

// Comparison columns
const comparisonColumns = ref([]);
const comparisonData = ref([]);

// ==================== Methods ====================

// Handle search
const handleSearch = async () => {
  loading.value = true;
  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Filter mock data based on search criteria
    let filteredData = [...mockSupplierData];

    // Keyword search
    if (searchForm.keyword) {
      const keyword = searchForm.keyword.toLowerCase();
      filteredData = filteredData.filter((supplier) => supplier.companyName.toLowerCase().includes(keyword) || supplier.sellerNickname.toLowerCase().includes(keyword) || supplier.shopName.toLowerCase().includes(keyword) || JSON.parse(supplier.searchKeywords).some((keyword_item) => keyword_item.toLowerCase().includes(keyword)));
    }

    // Location filter
    if (searchForm.location.length > 0) {
      filteredData = filteredData.filter((supplier) => {
        return searchForm.location.some((loc) => {
          if (loc.length === 1) {
            return supplier.region.includes(loc[0]);
          } else {
            return supplier.region.includes(loc[0]) && supplier.region.includes(loc[1]);
          }
        });
      });
    }

    // Industry filter
    if (searchForm.industry.length > 0) {
      filteredData = filteredData.filter((supplier) => searchForm.industry.includes(supplier.industry));
    }

    // Supplier type filter
    if (searchForm.supplierType) {
      filteredData = filteredData.filter((supplier) => supplier.supplierType === searchForm.supplierType);
    }

    // Business type filter
    if (searchForm.businessType.length > 0) {
      filteredData = filteredData.filter((supplier) => searchForm.businessType.includes(supplier.businessType));
    }

    // Quick filter
    if (searchForm.quickFilter) {
      switch (searchForm.quickFilter) {
        case 'recommended':
          filteredData = filteredData.filter((s) => s.supplierType === 'recommended');
          break;
        case 'mechanical':
          filteredData = filteredData.filter((s) => s.industry === '机械制造');
          break;
        case 'electrical':
          filteredData = filteredData.filter((s) => s.industry === '电子电气');
          break;
        case 'hydraulic':
          filteredData = filteredData.filter((s) => s.industry === '液压气动');
          break;
      }
    }

    // Sort data
    if (sortBy.value !== 'relevance') {
      filteredData.sort((a, b) => {
        switch (sortBy.value) {
          case 'establishmentYear':
            return b.establishmentTime - a.establishmentTime;
          case 'registeredCapital':
            // 解析注册资本字符串进行比较
            const getCapitalValue = (capital) => {
              const match = capital.match(/(\d+)/);
              return match ? parseInt(match[1]) : 0;
            };
            return getCapitalValue(b.registeredCapital) - getCapitalValue(a.registeredCapital);
          case 'employeeCount':
            return b.socialSecurityCount - a.socialSecurityCount;
          default:
            return 0;
        }
      });
    }

    // Update pagination
    pagination.total = filteredData.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;

    supplierList.value = filteredData.slice(startIndex, endIndex);
  } catch (error) {
    message.error('搜索失败，请重试');
  } finally {
    loading.value = false;
  }
};

// Handle quick filter
const handleQuickFilter = (filterKey) => {
  searchForm.quickFilter = searchForm.quickFilter === filterKey ? '' : filterKey;
  handleSearch();
};

// Handle reset filters
const handleResetFilters = () => {
  Object.keys(searchForm).forEach((key) => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = '';
    }
  });
  pagination.current = 1;
  handleSearch();
};

// Handle save filter
const handleSaveFilter = () => {
  message.success('筛选条件已保存');
};

// Handle remove filter
const handleRemoveFilter = (filterKey) => {
  switch (filterKey) {
    case 'keyword':
      searchForm.keyword = '';
      break;
    case 'location':
      searchForm.location = [];
      break;
    case 'industry':
      searchForm.industry = [];
      break;
    case 'companySize':
      searchForm.companySize = '';
      break;
    case 'supplierType':
      searchForm.supplierType = '';
      break;
  }
  handleSearch();
};

// Handle pagination change
const handlePaginationChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  handleSearch();
};

// Handle table change
const handleTableChange = (pag, _filters, sorter) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;

  if (sorter.field) {
    sortBy.value = sorter.field;
  }

  handleSearch();
};

// Handle supplier selection
const handleSelectSupplier = (supplierId) => {
  const index = selectedSuppliers.value.indexOf(supplierId);
  if (index > -1) {
    selectedSuppliers.value.splice(index, 1);
  } else {
    selectedSuppliers.value.push(supplierId);
  }
};

// Handle view supplier detail
const handleViewSupplierDetail = (supplier) => {
  selectedSupplierDetail.value = supplier;
  showSupplierDetailModal.value = true;
};

// Handle toggle bookmark
const handleToggleBookmark = async (supplier) => {
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 更新本地状态
    supplier.isBookmarked = !supplier.isBookmarked;

    if (supplier.isBookmarked) {
      message.success(`已收藏 ${supplier.companyName}`);
    } else {
      message.success(`已取消收藏 ${supplier.companyName}`);
    }
  } catch (error) {
    message.error('操作失败，请重试');
  }
};

// Handle logo error
const handleLogoError = (event) => {
  // 当logo加载失败时，隐藏img标签，显示默认logo
  event.target.style.display = 'none';
  const defaultLogo = event.target.nextElementSibling;
  if (defaultLogo) {
    defaultLogo.style.display = 'flex';
  }
};

// Handle add to comparison
const handleAddToComparison = (supplier) => {
  if (comparisonList.value.length >= 3) {
    message.warning('最多只能对比3家供应商');
    return;
  }

  if (comparisonList.value.find((s) => s.id === supplier.id)) {
    message.warning('该供应商已在对比列表中');
    return;
  }

  comparisonList.value.push(supplier);
  message.success(`已将 ${supplier.name} 添加到对比列表`);
};

// Handle remove from comparison
const handleRemoveFromComparison = (supplierId) => {
  const index = comparisonList.value.findIndex((s) => s.id === supplierId);
  if (index > -1) {
    comparisonList.value.splice(index, 1);
  }
};

// Handle start comparison
const handleStartComparison = () => {
  if (selectedSuppliers.value.length < 2) {
    message.warning('至少需要选择2家供应商才能进行对比');
    return;
  }

  if (selectedSuppliers.value.length > 3) {
    message.warning('最多只能对比3家供应商');
    return;
  }

  // 获取选中的供应商数据
  const selectedData = supplierList.value.filter((supplier) => selectedSuppliers.value.includes(supplier.id));

  // Prepare comparison data
  comparisonColumns.value = [
    { title: '对比项目', dataIndex: 'item', key: 'item', fixed: 'left', width: 120 },
    ...selectedData.map((supplier) => ({
      title: supplier.companyName,
      dataIndex: supplier.id,
      key: supplier.id,
      width: 200,
    })),
  ];

  comparisonData.value = [
    {
      key: 'location',
      item: '所在地区',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.region;
        return acc;
      }, {}),
    },
    {
      key: 'industry',
      item: '行业类别',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.industry;
        return acc;
      }, {}),
    },
    {
      key: 'establishmentYear',
      item: '成立时间',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = `${formatTimestamp(supplier.establishmentTime)}年`;
        return acc;
      }, {}),
    },
    {
      key: 'registeredCapital',
      item: '注册资本',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.registeredCapital;
        return acc;
      }, {}),
    },
    {
      key: 'employeeCount',
      item: '员工数量',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = `${supplier.socialSecurityCount}人`;
        return acc;
      }, {}),
    },
    {
      key: 'supplierType',
      item: '供应商类型',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.supplierType === 'recommended' ? '推荐品牌商' : '普通供应商';
        return acc;
      }, {}),
    },
    {
      key: 'factoryLevel',
      item: '工厂等级',
      ...selectedData.reduce((acc, supplier) => {
        acc[supplier.id] = supplier.factoryLevel;
        return acc;
      }, {}),
    },
  ];

  showComparisonModal.value = true;
};

// Handle export suppliers
const handleExportSuppliers = () => {
  if (selectedSuppliers.value.length === 0) {
    message.warning('请先选择要导出的供应商');
    return;
  }

  const selectedData = supplierList.value.filter((supplier) => selectedSuppliers.value.includes(supplier.id));

  // 模拟导出功能
  message.success(`正在导出 ${selectedData.length} 家供应商数据...`);

  // 这里可以实现实际的导出逻辑
  console.log('导出数据:', selectedData);
};

// Utility functions
const formatCurrency = (amount) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万元`;
  }
  return `${amount.toLocaleString()}元`;
};

const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.getFullYear();
};

// ==================== Lifecycle ====================

onMounted(() => {
  // Initialize supplier list with mock data
  supplierList.value = mockSupplierData.slice(0, pagination.pageSize);
  pagination.total = mockSupplierData.length;

  // Initialize map if needed
  if (viewMode.value === 'map') {
    // Map initialization would go here
  }
});

// Watch for view mode changes
watch(viewMode, (newMode) => {
  if (newMode === 'map') {
    // Initialize map
    setTimeout(() => {
      // Map initialization code would go here
      mapStats.currentRegion = supplierList.value.length;
      mapStats.recommended = supplierList.value.filter((s) => s.supplierType === 'recommended').length;
      mapStats.normal = supplierList.value.filter((s) => s.supplierType === 'normal').length;
    }, 100);
  }
});
</script>

<style lang="less" scoped>
.supplier-index-container {
  min-height: 100vh;

  .search-filter-section {
    margin-bottom: 16px;

    .search-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .search-input-section {
        margin-bottom: 20px;

        .main-search-input {
          width: 100%;
          max-width: 600px;
        }
      }

      .quick-filters {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;

        .filter-label {
          font-weight: 500;
          color: #333;
          margin-right: 8px;
        }

        .quick-filter-tag {
          cursor: pointer;
          transition: all 0.3s;
          border: 1px solid #d9d9d9;

          &:hover {
            border-color: #f94c30;
            color: #f94c30;
          }

          i {
            margin-right: 4px;
          }
        }
      }

      .filters-panel {
        .filter-actions {
          margin-top: 20px;
          padding-top: 20px;
          border-top: 1px solid #f0f0f0;
        }

        :deep(.ant-form-item) {
          margin-bottom: 16px;
        }

        :deep(.ant-form-item-label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  // Table operations styling similar to orderTable
  .table-operations {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
  }

  // Selection summary styling similar to orderTable
  .selection-summary {
    position: sticky;
    bottom: 0;
    margin-bottom: 16px;
    background-color: #fff4f0;
    padding: 12px 16px;
    border-radius: 4px;
    border: 1px solid #ffa39e;
    display: flex;
    justify-content: space-between;
    z-index: 1;

    .summary-content {
      display: flex;
      gap: 16px;
    }
  }

  .active-filters {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .active-filters-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }

  .main-content {
    display: flex;
    gap: 16px;

    .left-sidebar {
      width: 280px;
      flex-shrink: 0;

      .bookmarks-card,
      .comparison-card,
      .quick-actions-card {
        margin-bottom: 16px;

        :deep(.ant-card-body) {
          padding: 12px;
        }
      }

      .bookmark-folders {
        .bookmark-folder {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 4px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #f94c30;
            color: white;
          }

          i {
            margin-right: 8px;
            color: #f94c30;
          }

          &.active i {
            color: white;
          }

          span {
            flex: 1;
            font-size: 12px;
          }
        }
      }

      .comparison-items {
        margin-bottom: 12px;

        .comparison-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 8px;
          margin-bottom: 4px;
          background-color: #f5f5f5;
          border-radius: 4px;

          .supplier-name {
            font-size: 12px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .right-content {
      flex: 1;

      .list-view {
        .supplier-table {
          background: white;
          border-radius: 6px;
          overflow: hidden;

          .supplier-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .bookmark-icon {
              color: #f94c30;
              font-size: 14px;
            }
          }

          :deep(.ant-table-thead > tr > th) {
            background-color: #fafafa;
            font-weight: 600;
          }
        }
      }

      .card-view {
        .supplier-cards-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
          gap: 20px;

          .supplier-card-wrapper {
            display: flex;
          }
        }

        .supplier-card {
          border-radius: 12px;
          transition: all 0.3s;
          cursor: pointer;
          border: 1px solid #e8e8e8;
          overflow: hidden;
          width: 100%;
          height: 100%;

          &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-4px);
            border-color: #d9d9d9;
          }

          &.selected {
            border-color: #f94c30;
            box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.2);
          }

          :deep(.ant-card-body) {
            padding: 16px;
            height: 100%;
          }

          .card-content {
            display: flex;
            flex-direction: column;
            height: 100%;

            .supplier-header {
              display: flex;
              align-items: flex-start;
              gap: 12px;
              margin-bottom: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid #f0f0f0;

              .card-checkbox {
                margin-top: 4px;
                flex-shrink: 0;
              }

              .supplier-logo {
                width: 50px;
                height: 50px;
                flex-shrink: 0;
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid #e8e8e8;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }

                .default-logo {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;

                  i {
                    font-size: 20px;
                  }
                }
              }

              .supplier-name-section {
                flex: 1;
                min-width: 0;

                .supplier-name {
                  font-weight: 600;
                  color: #333;
                  font-size: 16px;
                  line-height: 1.4;
                  margin-bottom: 6px;
                  word-break: break-word;
                }

                .supplier-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;

                  .recommended-tag {
                    font-weight: 500;
                    border: none;

                    i {
                      margin-right: 4px;
                    }
                  }
                }
              }

              .header-actions {
                flex-shrink: 0;

                .bookmark-btn {
                  padding: 6px;
                  border-radius: 6px;
                  transition: all 0.3s;

                  &:hover {
                    background-color: #f5f5f5;
                  }

                  &.bookmarked {
                    color: #f94c30;

                    &:hover {
                      background-color: rgba(249, 76, 48, 0.1);
                    }
                  }

                  i {
                    font-size: 16px;
                  }
                }
              }
            }

            .supplier-location {
              display: flex;
              align-items: center;
              color: #666;
              font-size: 13px;
              margin-bottom: 16px;

              i {
                margin-right: 6px;
                color: #f94c30;
              }
            }

            .supplier-details {
              margin-bottom: 16px;
              flex: 1;

              .detail-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 13px;

                &:last-child {
                  margin-bottom: 0;
                }

                .detail-label {
                  color: #666;
                  font-weight: 400;
                }

                .detail-value {
                  color: #333;
                  font-weight: 500;
                  text-align: right;
                }
              }
            }

            .supplier-products {
              margin-bottom: 16px;

              .products-label {
                font-size: 12px;
                color: #666;
                margin-bottom: 8px;
                font-weight: 500;
              }

              .products-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                align-items: center;

                .more-products {
                  font-size: 11px;
                  color: #999;
                  background: #f5f5f5;
                  padding: 2px 6px;
                  border-radius: 10px;
                }
              }
            }

            .card-actions {
              margin-top: auto;
              padding-top: 12px;
              border-top: 1px solid #f0f0f0;

              .details-btn {
                width: 100%;
                height: 36px;
                font-weight: 500;
                border-radius: 6px;

                i {
                  margin-right: 6px;
                }
              }
            }
          }
        }

        .card-pagination {
          margin-top: 24px;
          text-align: center;
        }
      }

      .map-view {
        .map-container {
          position: relative;
          height: 600px;
          background: white;
          border-radius: 6px;
          overflow: hidden;

          .map-canvas {
            width: 100%;
            height: 100%;
            background: #f0f2f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;

            &::before {
              content: '地图功能开发中...';
            }
          }

          .map-controls {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .map-legend,
            .map-stats {
              min-width: 160px;

              .legend-item {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                font-size: 12px;

                .legend-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  margin-right: 8px;

                  &.recommended {
                    background-color: #faad14;
                  }

                  &.normal {
                    background-color: #1890ff;
                  }
                }
              }
            }
          }

          .map-supplier-panel {
            position: absolute;
            bottom: 16px;
            left: 16px;
            width: 300px;

            .map-supplier-info {
              p {
                margin-bottom: 8px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  // Modal styles
  .supplier-detail-modal {
    .supplier-detail-content {
      .detail-actions {
        margin-top: 24px;
        text-align: center;
      }
    }
  }

  .comparison-modal {
    .comparison-content {
      :deep(.ant-table-thead > tr > th) {
        background-color: #fafafa;
        font-weight: 600;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td) {
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td:first-child) {
        background-color: #fafafa;
        font-weight: 600;
        text-align: left;
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .supplier-index-container {
    .main-content {
      .left-sidebar {
        width: 240px;
      }
    }
  }
}

@media (max-width: 992px) {
  .supplier-index-container {
    padding: 16px;

    .main-content {
      flex-direction: column;

      .left-sidebar {
        width: 100%;
        order: 2;

        .bookmarks-card,
        .comparison-card,
        .quick-actions-card {
          display: inline-block;
          width: calc(33.333% - 8px);
          margin-right: 12px;
          margin-bottom: 0;
          vertical-align: top;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .right-content {
        order: 1;
      }
    }

    .table-operations {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .selection-summary {
      flex-direction: column;
      gap: 12px;

      .summary-content {
        flex-wrap: wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .supplier-index-container {
    .search-filter-section {
      .search-card {
        padding: 16px;

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              margin-bottom: 12px;
            }
          }

          .filter-actions {
            text-align: center;

            .ant-space {
              width: 100%;
              justify-content: center;
            }
          }
        }
      }
    }

    .main-content {
      .left-sidebar {
        .bookmarks-card,
        .comparison-card,
        .quick-actions-card {
          width: 100%;
          margin-right: 0;
          margin-bottom: 12px;
        }
      }

      .right-content {
        .card-view {
          .supplier-cards-container {
            grid-template-columns: 1fr;
            gap: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .supplier-index-container {
    .search-filter-section {
      .search-card {
        padding: 12px;

        .quick-filters {
          .filter-label {
            width: 100%;
            margin-bottom: 8px;
          }
        }

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              width: 100% !important;
              max-width: 100% !important;
              flex: 0 0 100% !important;
            }
          }
        }
      }
    }

    .main-content {
      .right-content {
        .card-view {
          .supplier-cards-container {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .supplier-card {
            .card-content {
              .supplier-header {
                .supplier-name-section {
                  .supplier-name {
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .supplier-index-container {
    .main-content {
      .right-content {
        .card-view {
          .supplier-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          }
        }
      }
    }
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .supplier-index-container {
    .main-content {
      .right-content {
        .card-view {
          .supplier-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          }
        }
      }
    }
  }
}

@media (min-width: 1441px) {
  .supplier-index-container {
    .main-content {
      .right-content {
        .card-view {
          .supplier-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
          }
        }
      }
    }
  }
}
</style>
