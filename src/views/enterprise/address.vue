<template>
  <div class="address-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">地址管理</h1>
        <p class="page-description">管理您的收货地址信息</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" size="large" @click="handleAddAddress" class="add-btn">
          <template #icon>
            <PlusOutlined />
          </template>
          新增地址
        </a-button>
      </div>
    </div>

    <!-- 地址列表 -->
    <div class="address-list">
      <div v-if="addressList.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <EnvironmentOutlined />
          </div>
          <h3>暂无地址信息</h3>
          <p>您还没有添加任何地址，点击"新增地址"开始添加</p>
          <a-button type="primary" @click="handleAddAddress">
            <template #icon>
              <PlusOutlined />
            </template>
            新增地址
          </a-button>
        </div>
      </div>

      <a-row v-else :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="address in addressList" :key="address.id">
          <a-card class="address-card" :class="{ 'default-address': address.isDefault }">
            <!-- 默认标签 -->
            <div v-if="address.isDefault" class="default-tag">
              <a-tag color="success">默认地址</a-tag>
            </div>

            <!-- 地址信息 -->
            <div class="address-info">
              <div class="contact-info">
                <h3 class="contact-name">{{ address.contactName }}</h3>
                <div class="contact-phone">
                  <PhoneOutlined />
                  <span>{{ address.phone }}</span>
                </div>
              </div>

              <div class="location-info">
                <div class="region">
                  <EnvironmentOutlined />
                  <span>{{ address.region }}</span>
                </div>
                <div class="detail-address">{{ address.detailAddress }}</div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="address-actions">
              <a-space>
                <a-button 
                  v-if="!address.isDefault" 
                  type="link" 
                  @click="handleSetDefault(address.id)"
                  class="action-btn"
                >
                  设为默认
                </a-button>
                <a-button 
                  type="link" 
                  @click="handleEditAddress(address)"
                  class="action-btn"
                >
                  编辑
                </a-button>
                <a-button 
                  type="link" 
                  danger
                  @click="handleDeleteAddress(address.id)"
                  class="action-btn"
                >
                  删除
                </a-button>
              </a-space>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 地址表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑地址' : '新增地址'"
      width="600px"
      :maskClosable="false"
      @ok="handleSubmit"
      @cancel="handleCancel"
      class="address-modal"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="address-form"
      >
        <a-form-item label="联系人" name="contactName">
          <a-input 
            v-model:value="formData.contactName" 
            placeholder="请输入联系人姓名"
            size="large"
          />
        </a-form-item>

        <a-form-item label="联系方式" name="phone">
          <a-input-group compact>
            <a-select 
              v-model:value="formData.phoneType" 
              style="width: 110px"
              size="large"
              @change="handlePhoneTypeChange"
            >
              <a-select-option value="mobile">
                <MobileOutlined /> 手机
              </a-select-option>
              <a-select-option value="telephone">
                <PhoneOutlined /> 座机
              </a-select-option>
            </a-select>
            
            <!-- 手机号输入 -->
            <a-input 
              v-if="formData.phoneType === 'mobile'"
              v-model:value="formData.phone" 
              placeholder="请输入手机号码"
              style="width: calc(100% - 110px)"
              size="large"
            />
            
            <!-- 座机号输入 -->
            <template v-else>
              <a-input 
                v-model:value="formData.areaCode" 
                placeholder="区号"
                style="width: 80px"
                size="large"
              />
              <a-input 
                v-model:value="formData.phoneNumber" 
                placeholder="电话号码"
                style="width: calc(100% - 300px)"
                size="large"
              />
              <a-input 
                v-model:value="formData.extension" 
                placeholder="分机(可选)"
                style="width: 110px"
                size="large"
              />
            </template>
          </a-input-group>
        </a-form-item>

        <a-form-item label="所在地区" name="region">
          <a-cascader
            v-model:value="formData.regionCodes"
            :options="regionOptions"
            placeholder="请选择省/市/区"
            size="large"
            @change="handleRegionChange"
          />
        </a-form-item>

        <a-form-item label="详细地址" name="detailAddress">
          <a-textarea
            v-model:value="formData.detailAddress"
            placeholder="请输入详细地址（街道、门牌号等）"
            :rows="3"
            size="large"
          />
        </a-form-item>

        <a-form-item name="isDefault" v-if="!isEdit || !formData.isDefault">
          <a-checkbox v-model:checked="formData.isDefault">
            设为默认地址
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MobileOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const addressList = ref([])

// 表单数据
const formData = reactive({
  id: null,
  contactName: '',
  phone: '',
  phoneType: 'mobile',
  areaCode: '',
  phoneNumber: '',
  extension: '',
  regionCodes: [],
  region: '',
  detailAddress: '',
  isDefault: false
})

// 地区选项数据
const regionOptions = ref([
  {
    value: '110000',
    label: '北京市',
    children: [
      {
        value: '110100',
        label: '北京市',
        children: [
          { value: '110101', label: '东城区' },
          { value: '110102', label: '西城区' },
          { value: '110105', label: '朝阳区' },
          { value: '110106', label: '丰台区' },
          { value: '110107', label: '石景山区' },
          { value: '110108', label: '海淀区' },
          { value: '110109', label: '门头沟区' },
          { value: '110111', label: '房山区' },
          { value: '110112', label: '通州区' },
          { value: '110113', label: '顺义区' },
          { value: '110114', label: '昌平区' },
          { value: '110115', label: '大兴区' },
          { value: '110116', label: '怀柔区' },
          { value: '110117', label: '平谷区' },
          { value: '110118', label: '密云区' },
          { value: '110119', label: '延庆区' }
        ]
      }
    ]
  },
  {
    value: '310000',
    label: '上海市',
    children: [
      {
        value: '310100',
        label: '上海市',
        children: [
          { value: '310101', label: '黄浦区' },
          { value: '310104', label: '徐汇区' },
          { value: '310105', label: '长宁区' },
          { value: '310106', label: '静安区' },
          { value: '310107', label: '普陀区' },
          { value: '310109', label: '虹口区' },
          { value: '310110', label: '杨浦区' },
          { value: '310112', label: '闵行区' },
          { value: '310113', label: '宝山区' },
          { value: '310114', label: '嘉定区' },
          { value: '310115', label: '浦东新区' },
          { value: '310116', label: '金山区' },
          { value: '310117', label: '松江区' },
          { value: '310118', label: '青浦区' },
          { value: '310120', label: '奉贤区' },
          { value: '310151', label: '崇明区' }
        ]
      }
    ]
  },
  {
    value: '440000',
    label: '广东省',
    children: [
      {
        value: '440100',
        label: '广州市',
        children: [
          { value: '440103', label: '荔湾区' },
          { value: '440104', label: '越秀区' },
          { value: '440105', label: '海珠区' },
          { value: '440106', label: '天河区' },
          { value: '440111', label: '白云区' },
          { value: '440112', label: '黄埔区' },
          { value: '440113', label: '番禺区' },
          { value: '440114', label: '花都区' },
          { value: '440115', label: '南沙区' },
          { value: '440117', label: '从化区' },
          { value: '440118', label: '增城区' }
        ]
      },
      {
        value: '440300',
        label: '深圳市',
        children: [
          { value: '440303', label: '罗湖区' },
          { value: '440304', label: '福田区' },
          { value: '440305', label: '南山区' },
          { value: '440306', label: '宝安区' },
          { value: '440307', label: '龙岗区' },
          { value: '440308', label: '盐田区' },
          { value: '440309', label: '龙华区' },
          { value: '440310', label: '坪山区' },
          { value: '440311', label: '光明区' }
        ]
      }
    ]
  }
])

// 表单验证规则
const formRules = {
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '联系人姓名长度为2-20个字符', trigger: 'blur' }
  ],
  phone: [
    { 
      validator: (rule, value) => {
        if (formData.phoneType === 'mobile') {
          if (!formData.phone) return Promise.reject('请输入手机号码')
          const mobileRegex = /^1[3-9]\d{9}$/
          if (!mobileRegex.test(formData.phone)) {
            return Promise.reject('请输入正确的手机号码')
          }
        } else {
          if (!formData.areaCode) return Promise.reject('请输入区号')
          if (!formData.phoneNumber) return Promise.reject('请输入电话号码')
          
          const areaCodeRegex = /^0\d{2,3}$/
          if (!areaCodeRegex.test(formData.areaCode)) {
            return Promise.reject('区号格式不正确，应为3-4位数字且以0开头')
          }
          
          const phoneRegex = /^\d{7,8}$/
          if (!phoneRegex.test(formData.phoneNumber)) {
            return Promise.reject('电话号码格式不正确，应为7-8位数字')
          }
          
          if (formData.extension && !/^\d{1,6}$/.test(formData.extension)) {
            return Promise.reject('分机号格式不正确，应为1-6位数字')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  regionCodes: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ],
  detailAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 100, message: '详细地址长度为5-100个字符', trigger: 'blur' }
  ]
}

// 生成唯一ID
const generateId = () => {
  return Date.now() + Math.random().toString(36).substr(2, 9)
}

// 初始化示例数据
const initData = () => {
  addressList.value = [
    {
      id: '1',
      contactName: '张三',
      phone: '13800138000',
      phoneType: 'mobile',
      regionCodes: ['110000', '110100', '110105'],
      region: '北京市北京市朝阳区',
      detailAddress: '建国门外大街1号国贸大厦A座2001室',
      isDefault: true
    },
    {
      id: '2',
      contactName: '李四',
      phone: '021-12345678',
      phoneType: 'telephone',
      regionCodes: ['310000', '310100', '310115'],
      region: '上海市上海市浦东新区',
      detailAddress: '陆家嘴环路1000号恒生银行大厦50楼',
      isDefault: false
    }
  ]
}

// 处理地区选择变化
const handleRegionChange = (value, selectedOptions) => {
  if (selectedOptions && selectedOptions.length > 0) {
    formData.region = selectedOptions.map(option => option.label).join('')
  }
}

// 处理联系方式类型切换
const handlePhoneTypeChange = (value) => {
  // 清空所有联系方式相关字段
  formData.phone = ''
  formData.areaCode = ''
  formData.phoneNumber = ''
  formData.extension = ''
  
  // 清除验证错误信息
  formRef.value?.clearValidate(['phone'])
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    contactName: '',
    phone: '',
    phoneType: 'mobile',
    areaCode: '',
    phoneNumber: '',
    extension: '',
    regionCodes: [],
    region: '',
    detailAddress: '',
    isDefault: false
  })
  formRef.value?.resetFields()
}

// 新增地址
const handleAddAddress = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑地址
const handleEditAddress = (address) => {
  isEdit.value = true
  Object.assign(formData, { ...address })
  
  // 如果是座机号，需要解析区号、电话号码和分机号
  if (address.phoneType === 'telephone' && address.phone) {
    const phoneParts = address.phone.split('-')
    if (phoneParts.length >= 2) {
      formData.areaCode = phoneParts[0]
      formData.phoneNumber = phoneParts[1]
      formData.extension = phoneParts[2] || ''
      formData.phone = '' // 清空统一的phone字段
    }
  }
  
  modalVisible.value = true
}

// 设为默认地址
const handleSetDefault = (id) => {
  // 取消其他地址的默认状态
  addressList.value.forEach(address => {
    address.isDefault = address.id === id
  })
  message.success('已设为默认地址')
}

// 删除地址
const handleDeleteAddress = (id) => {
  const address = addressList.value.find(item => item.id === id)
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除联系人"${address.contactName}"的地址吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      const index = addressList.value.findIndex(item => item.id === id)
      if (index > -1) {
        const isDefault = addressList.value[index].isDefault
        addressList.value.splice(index, 1)
        
        // 如果删除的是默认地址，将第一个地址设为默认
        if (isDefault && addressList.value.length > 0) {
          addressList.value[0].isDefault = true
        }
        
        message.success('地址删除成功')
      }
    }
  })
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 处理联系方式显示
    let displayPhone = ''
    if (formData.phoneType === 'mobile') {
      displayPhone = formData.phone
    } else {
      displayPhone = formData.areaCode + '-' + formData.phoneNumber
      if (formData.extension) {
        displayPhone += '-' + formData.extension
      }
    }
    
    const addressData = {
      ...formData,
      phone: displayPhone
    }
    
    if (isEdit.value) {
      // 编辑地址
      const index = addressList.value.findIndex(item => item.id === formData.id)
      if (index > -1) {
        // 如果设为默认，先取消其他地址的默认状态
        if (formData.isDefault) {
          addressList.value.forEach(address => {
            if (address.id !== formData.id) {
              address.isDefault = false
            }
          })
        }
        addressList.value[index] = { ...addressData }
        message.success('地址修改成功')
      }
    } else {
      // 新增地址
      const newAddress = {
        ...addressData,
        id: generateId()
      }
      
      // 如果设为默认，先取消其他地址的默认状态
      if (newAddress.isDefault) {
        addressList.value.forEach(address => {
          address.isDefault = false
        })
      } else if (addressList.value.length === 0) {
        // 如果是第一个地址，自动设为默认
        newAddress.isDefault = true
      }
      
      addressList.value.push(newAddress)
      message.success('地址添加成功')
    }
    
    modalVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 组件挂载
onMounted(() => {
  initData()
})
</script>

<style scoped>
.address-management-container {
  min-height: 100vh;
  background: #ffffff;
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content h1.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  background: linear-gradient(135deg, #f94c30, #ff7559);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content .page-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.add-btn {
  height: 48px;
  padding: 0 24px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(249, 76, 48, 0.3);
  border: none;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(249, 76, 48, 0.4);
}

.address-list {
  margin-top: 24px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.empty-content {
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-content h3 {
  font-size: 18px;
  color: #333;
  margin: 16px 0 8px 0;
}

.empty-content p {
  color: #666;
  margin-bottom: 24px;
}

.address-card {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.address-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #f94c30;
}

.address-card.default-address {
  border: 2px solid #52c41a;
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.2);
}

.default-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.address-info {
  padding: 8px 0;
}

.contact-info {
  margin-bottom: 16px;
}

.contact-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.contact-phone {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.contact-phone .anticon {
  margin-right: 6px;
  color: #f94c30;
}

.location-info .region {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.location-info .region .anticon {
  margin-right: 6px;
  color: #f94c30;
}

.detail-address {
  color: #333;
  font-size: 14px;
  line-height: 1.6;
  padding-left: 20px;
}

.address-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  padding: 4px 0;
  height: auto;
  font-size: 14px;
}

.action-btn:hover {
  background: none;
}

:deep(.address-modal .ant-modal-header) {
  border-radius: 8px 8px 0 0;
  background: linear-gradient(135deg, #f94c30, #ff7559);
}

:deep(.address-modal .ant-modal-title) {
  color: white;
  font-weight: 600;
}

:deep(.address-modal .ant-modal-close) {
  color: white;
}

:deep(.address-modal .ant-modal-close:hover) {
  background-color: rgba(255, 255, 255, 0.1);
}

.address-form {
  padding-top: 16px;
}

:deep(.address-form .ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
}

:deep(.address-form .ant-input),
:deep(.address-form .ant-select-selector),
:deep(.address-form .ant-cascader-picker),
:deep(.address-form .ant-input-group) {
  border-radius: 8px;
}

:deep(.address-form .ant-input:focus),
:deep(.address-form .ant-select-focused .ant-select-selector),
:deep(.address-form .ant-cascader-picker-focused) {
  border-color: #f94c30;
  box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.1);
}

@media (max-width: 768px) {
  .address-management-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
  }
  
  .add-btn {
    width: 100%;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
}
</style>
