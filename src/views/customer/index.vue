<template>
  <div class="customer-service-container">

    <!-- 客户经理区域 -->
    <div class="service-section">
      <div class="section-header">
        <h2 class="section-title">客户经理</h2>
      </div>
      <div class="service-card">
        <div class="service-info">
          <div class="avatar-section">
            <a-avatar :size="80" :src="customerManager.avatar" v-if="customerManager.avatar">
              {{ customerManager.name?.charAt(0) }}
            </a-avatar>
            <a-avatar :size="80" v-else>
              {{ customerManager.name?.charAt(0) }}
            </a-avatar>
          </div>
          <div class="info-section">
            <div class="info-row">
              <div class="info-item">
                <UserOutlined class="info-icon" />
                <span class="info-label">姓名：</span>
                <span class="info-value">{{ customerManager.name }}</span>
              </div>
              <div class="info-item">
                <PhoneOutlined class="info-icon" />
                <span class="info-label">手机号：</span>
                <span class="info-value">{{ customerManager.phone }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <MailOutlined class="info-icon" />
                <span class="info-label">邮箱：</span>
                <span class="info-value">{{ customerManager.email }}</span>
              </div>
              <div class="info-item">
                <BankOutlined class="info-icon" />
                <span class="info-label">公司：</span>
                <span class="info-value">{{ customerManager.company }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <IdcardOutlined class="info-icon" />
                <span class="info-label">职位：</span>
                <span class="info-value">{{ customerManager.position }}</span>
              </div>
              <div class="info-item">
                <SafetyCertificateOutlined class="info-icon" />
                <span class="info-label">工作证照：</span>
                <div class="certification-photo" @click="showCertification('customerManager')">
                  <a-image
                    :src="customerManager.certificationPhoto"
                    :preview="false"
                    width="60"
                    height="40"
                    style="border-radius: 4px; cursor: pointer; object-fit: cover;"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专属客服区域 -->
    <div class="service-section">
      <div class="section-header">
        <h2 class="section-title">专属客服</h2>
      </div>
      <div class="service-card">
        <div class="service-info">
          <div class="avatar-section">
            <a-avatar :size="80" :src="dedicatedService.avatar" v-if="dedicatedService.avatar">
              {{ dedicatedService.name?.charAt(0) }}
            </a-avatar>
            <a-avatar :size="80" v-else>
              {{ dedicatedService.name?.charAt(0) }}
            </a-avatar>
          </div>
          <div class="info-section">
            <div class="info-row">
              <div class="info-item">
                <UserOutlined class="info-icon" />
                <span class="info-label">姓名：</span>
                <span class="info-value">{{ dedicatedService.name }}</span>
              </div>
              <div class="info-item">
                <PhoneOutlined class="info-icon" />
                <span class="info-label">手机号：</span>
                <span class="info-value">{{ dedicatedService.phone }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <MailOutlined class="info-icon" />
                <span class="info-label">邮箱：</span>
                <span class="info-value">{{ dedicatedService.email }}</span>
              </div>
              <div class="info-item">
                <BankOutlined class="info-icon" />
                <span class="info-label">公司：</span>
                <span class="info-value">{{ dedicatedService.company }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <IdcardOutlined class="info-icon" />
                <span class="info-label">职位：</span>
                <span class="info-value">{{ dedicatedService.position }}</span>
              </div>
              <div class="info-item">
                <SafetyCertificateOutlined class="info-icon" />
                <span class="info-label">工作证照：</span>
                <div class="certification-photo" @click="showCertification('dedicatedService')">
                  <a-image
                    :src="dedicatedService.certificationPhoto"
                    :preview="false"
                    width="60"
                    height="40"
                    style="border-radius: 4px; cursor: pointer; object-fit: cover;"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 授权说明 -->
        <div class="authorization-info">
          <div class="auth-description">
            <InfoCircleOutlined class="auth-icon" />
            <div class="auth-text">
              <p><strong>授权说明：</strong></p>
              <p>当对专属客服进行授权之后，默认情况下，专属客服仅能够帮助您进行物料询价，无法执行其他任何操作。</p>
              <p>如果您希望专属客服帮助您执行更多采购工作，可以在<router-link to="/workspace/role" class="role-link">角色</router-link>中为其配置相应的权限。（仅管理员可操作）</p>
            </div>
          </div>
        </div>

        <!-- 授权按钮 -->
        <div class="authorization-action">
          <a-button 
            type="primary"
            style="background-color: #f94c30; border-color: #f94c30;" 
            size="large"
            :loading="authLoading"
            @click="handleAuthorizationClick"
            :class="{ 'cancel-btn': isAuthorized }"
            :style="!isAuthorized ? { backgroundColor: '#f94c30', borderColor: '#f94c30' } : {}"
          >
            <template #icon>
              <CheckCircleOutlined v-if="isAuthorized" />
              <UnlockOutlined v-else />
            </template>
            {{ isAuthorized ? '取消授权' : '授权' }}
          </a-button>
        </div>
      </div>
    </div>

    <!-- 授权确认对话框 -->
    <a-modal
      v-model:open="confirmModalVisible"
      :title="modalTitle"
      @ok="handleConfirmAuth"
      @cancel="confirmModalVisible = false"
      :ok-button-props="{ loading: authLoading }"
      ok-text="确认"
      cancel-text="取消"
    >
      <div class="confirm-content">
        <ExclamationCircleOutlined class="confirm-icon" />
        <div class="confirm-text">
          <p v-if="!isAuthorized">
            您确定要将工作台的功能授权给您的企业专属客服 <strong>{{ dedicatedService.name }}</strong> 吗？
          </p>
          <p v-else>
            您确定要取消对 <strong>{{ dedicatedService.name }}</strong> 专属客服的授权吗？
          </p>
          <p class="confirm-note">
            {{ !isAuthorized ? '授权后，该客服将能够帮助您进行物料询价。' : '取消授权后，该客服将无法访问您的相关信息。' }}
          </p>
        </div>
      </div>
    </a-modal>

    <!-- 工作证照预览对话框 -->
    <a-modal
      v-model:open="certificationModalVisible"
      :title="currentCertificationTitle"
      :footer="null"
      width="600px"
      centered
    >
      <div class="certification-preview">
        <a-image
          :src="currentCertificationImage"
          style="width: 100%; max-height: 400px; object-fit: contain;"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  BankOutlined,
  IdcardOutlined,
  SafetyCertificateOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  UnlockOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 客户经理信息
const customerManager = ref({
  name: '张明',
  phone: '138-0000-1234',
  email: '<EMAIL>',
  company: 'SelecTech科技有限公司',
  position: '高级客户经理',
  certification: 'CM20240001',
  certificationPhoto: 'https://via.placeholder.com/300x200/f94c30/white?text=Customer+Manager+Certificate',
  avatar: null
})

// 专属客服信息
const dedicatedService = ref({
  name: '李小雅',
  phone: '139-0000-5678',
  email: '<EMAIL>',
  company: 'SelecTech科技有限公司',
  position: '专属客服专员',
  certification: 'CS20240002',
  certificationPhoto: 'https://via.placeholder.com/300x200/f94c30/white?text=Service+Specialist+Certificate',
  avatar: null
})

// 授权状态
const isAuthorized = ref(false)
const authLoading = ref(false)
const confirmModalVisible = ref(false)
const certificationModalVisible = ref(false)
const currentCertificationImage = ref('')
const currentCertificationTitle = ref('')

// 计算属性
const modalTitle = computed(() => {
  return isAuthorized.value ? '取消授权确认' : '授权确认'
})

// 处理授权按钮点击
const handleAuthorizationClick = () => {
  confirmModalVisible.value = true
}

// 确认授权操作
const handleConfirmAuth = async () => {
  authLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    isAuthorized.value = !isAuthorized.value
    confirmModalVisible.value = false
    
    if (isAuthorized.value) {
      message.success('授权成功！专属客服现在可以帮助您进行物料询价。')
    } else {
      message.success('已取消授权！')
    }
  } catch (error) {
    message.error('操作失败，请重试。')
  } finally {
    authLoading.value = false
  }
}

// 显示工作证照
const showCertification = (type) => {
  if (type === 'customerManager') {
    currentCertificationImage.value = customerManager.value.certificationPhoto
    currentCertificationTitle.value = `${customerManager.value.name} - 工作证照`
  } else {
    currentCertificationImage.value = dedicatedService.value.certificationPhoto
    currentCertificationTitle.value = `${dedicatedService.value.name} - 工作证照`
  }
  certificationModalVisible.value = true
}
</script>

<style scoped>
.customer-service-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  color: #1a1a2b;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.service-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  color: #1a1a2b;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  padding-left: 12px;
  border-left: 4px solid #f94c30;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.service-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

.info-section {
  flex: 1;
}

.info-row {
  display: flex;
  gap: 32px;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
  flex: 1;
}

.info-icon {
  color: #f94c30;
  font-size: 16px;
  flex-shrink: 0;
}

.info-label {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.info-value {
  color: #1a1a2b;
  font-size: 14px;
  font-weight: 500;
  word-break: break-all;
}

.authorization-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
}

.auth-description {
  display: flex;
  gap: 12px;
}

.auth-icon {
  color: #52c41a;
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.auth-text {
  flex: 1;
}

.auth-text p {
  margin: 0 0 8px 0;
  color: #1a1a2b;
  font-size: 14px;
  line-height: 1.5;
}

.auth-text p:last-child {
  margin-bottom: 0;
}

.authorization-action {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.cancel-btn:hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
}

.certification-photo {
  cursor: pointer;
  transition: all 0.3s ease;
}

.certification-photo:hover {
  transform: scale(1.05);
}

.certification-preview {
  text-align: center;
}

.confirm-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.confirm-icon {
  color: #faad14;
  font-size: 22px;
  flex-shrink: 0;
  margin-top: 2px;
}

.confirm-text {
  flex: 1;
}

.confirm-text p {
  margin: 0 0 8px 0;
  color: #1a1a2b;
  font-size: 14px;
  line-height: 1.5;
}

.confirm-text p:last-child {
  margin-bottom: 0;
}

.confirm-note {
  color: #666 !important;
  font-size: 13px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-service-container {
    padding: 16px;
  }
  
  .service-info {
    flex-direction: column;
    gap: 16px;
  }
  
  .info-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .auth-description {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .service-card {
    padding: 16px;
  }
}
</style>