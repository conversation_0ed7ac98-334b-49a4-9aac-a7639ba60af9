<template>
  <div class="project-container">
    <div class="search-filter-area">
      <a-input
        v-model:value="searchText"
        placeholder="搜索项目名称/编号/负责人"
        style="width: 280px"
      />
      <a-select
        v-model:value="selectedStatus"
        style="width: 150px"
        placeholder="所有阶段"
      >
        <a-select-option value="">所有阶段</a-select-option>
        <a-select-option value="未开始">未开始</a-select-option>
        <a-select-option value="进行中">进行中</a-select-option>
        <a-select-option value="已关闭">已关闭</a-select-option>
      </a-select>
      <a-select
        v-model:value="selectedWarning"
        style="width: 150px"
        placeholder="所有预警"
      >
        <a-select-option value="">所有预警</a-select-option>
        <a-select-option value="交期延误">交期延误</a-select-option>
        <a-select-option value="预算超支">预算超支</a-select-option>
        <a-select-option value="独家供应">独家供应</a-select-option>
      </a-select>
      <a-button type="primary" @click="handleSearch">搜索</a-button>
      <a-button @click="handleReset">重置</a-button>
    </div>

    <div class="project-header">
      <a-button type="primary" @click="showModal">
        <template #icon><plus-outlined /></template>新建
      </a-button>
      <a-button type="primary" style="margin-left: 8px">
        <template #icon><download-outlined /></template>导出
      </a-button>
    </div>

    <a-table :dataSource="projects" :columns="columns" :pagination="pagination">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div style="font-weight: bold">{{ record.name }}</div>
          <div style="font-size: 12px; color: #888">{{ record.code }}</div>
        </template>

        <template v-if="column.key === 'status'">
          <a-tag :class="['status-tag', getStatusClass(record.status)]">
            {{ record.status }}
          </a-tag>
          <div class="phase-tooltip">
            <a-steps :current="getPhaseIndex(record.phases.current)" size="small" progress-dot>
              <a-step v-for="(phase, index) in phases" :key="index" :title="phase" />
            </a-steps>
          </div>
        </template>

        <template v-if="column.key === 'budget'">
          <div>预算: ¥{{ formatNumber(record.budget) }}</div>
          <div>已支出: ¥{{ formatNumber(record.spent) }}</div>
          <div :class="['budget-progress', getBudgetClass(record.spent, record.budget)]">
            <div
              class="budget-progress-inner"
              :style="{ width: `${Math.min((record.spent / record.budget) * 100, 100)}%` }"
            ></div>
          </div>
          <div
            :style="{
              fontSize: '12px',
              color: record.spent > record.budget ? '#f5222d' : '#888',
              textAlign: 'right'
            }"
          >
            {{ Math.round((record.spent / record.budget) * 100) }}%
            {{ record.spent > record.budget ? '(超支)' : '' }}
          </div>
        </template>

        <template v-if="column.key === 'warning'">
          <div v-if="record.warnings.length" class="risk-warning">
            <exclamation-circle-outlined /> {{ record.warnings.join(', ') }}
          </div>
          <div v-else>-</div>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a @click="viewProject(record)">查看</a>
            <a @click="editProject(record)">编辑</a>
            <a @click="manageProject(record)">
              {{ record.status === '未开始' ? '删除' : '管理' }}
            </a>
          </a-space>
        </template>
      </template>
    </a-table>

    <a-modal
      v-model:visible="modalVisible"
      title="新建项目"
      @ok="createProject"
      @cancel="modalVisible = false"
      okText="确定"
      cancelText="取消"
    >
      <a-form :model="form" :rules="rules" ref="formRef" layout="vertical">
        <a-form-item label="项目名称" name="name" required>
          <a-input v-model:value="form.name" />
        </a-form-item>
        <a-form-item label="负责人" name="manager" required>
          <a-select v-model:value="form.manager">
            <a-select-option value="">请选择</a-select-option>
            <a-select-option value="张工">张工</a-select-option>
            <a-select-option value="李工">李工</a-select-option>
            <a-select-option value="王工">王工</a-select-option>
            <a-select-option value="赵工">赵工</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止日期" name="deadline" required>
          <a-date-picker v-model:value="form.deadline" style="width: 100%" />
        </a-form-item>
        <a-form-item label="预算 (¥)" name="budget" required>
          <a-input-number v-model:value="form.budget" style="width: 100%" />
        </a-form-item>
        <a-form-item label="项目描述" name="description">
          <a-textarea v-model:value="form.description" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
const searchText = ref('');
const selectedStatus = ref('');
const selectedWarning = ref('');
const modalVisible = ref(false);
const formRef = ref(null);
const router = useRouter();
const form = reactive({
  name: '',
  manager: '',
  deadline: null,
  budget: null,
  description: ''
});

const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  manager: [{ required: true, message: '请选择负责人' }],
  deadline: [{ required: true, message: '请选择截止日期' }],
  budget: [{ required: true, message: '请输入预算' }]
};

const phases = ['未开始', '进行中', '已关闭'];

const columns = [
  { title: '项目名称/编号', dataIndex: 'name', key: 'name' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '负责人', dataIndex: 'manager', key: 'manager' },
  { title: '当前阶段', dataIndex: 'status', key: 'status' },
  { title: '成本监控', key: 'budget' },
  { title: '截止日期', dataIndex: 'deadline', key: 'deadline' },
  { title: '风险预警', key: 'warning' },
  { title: '操作', key: 'action' }
];

const projects = ref([
  {
    key: '1',
    name: '高精度数控机床升级',
    code: 'PRJ-2024-001',
    createTime: '2024-05-10',
    manager: '张工',
    status: '进行中',
    budget: 1500000,
    spent: 980000,
    deadline: '2024-09-30',
    warnings: ['交期延误'],
    phases: {
      completed: ['未开始'],
      current: '进行中'
    }
  },
  {
    key: '2',
    name: '智能AGV物流系统',
    code: 'PRJ-2024-002',
    createTime: '2024-05-05',
    manager: '李工',
    status: '进行中',
    budget: 800000,
    spent: 120000,
    deadline: '2024-11-15',
    warnings: ['独家供应风险'],
    phases: {
      completed: ['未开始'],
      current: '进行中'
    }
  },
  {
    key: '3',
    name: '自动化包装生产线',
    code: 'PRJ-2024-003',
    createTime: '2024-04-20',
    manager: '王工',
    status: '已关闭',
    budget: 2000000,
    spent: 2150000,
    deadline: '2024-06-10',
    warnings: ['预算超支'],
    phases: {
      completed: ['未开始', '进行中'],
      current: '已关闭'
    }
  },
  {
    key: '4',
    name: '新型材料测试设备',
    code: 'PRJ-2024-004',
    createTime: '2024-05-18',
    manager: '赵工',
    status: '进行中',
    budget: 500000,
    spent: 0,
    deadline: '2024-12-30',
    warnings: [],
    phases: {
      completed: ['未开始'],
      current: '进行中'
    }
  },
  {
    key: '5',
    name: '机械臂一期项目',
    code: 'PRJ-2024-005',
    createTime: '2024-05-10',
    manager: '张工',
    status: '未开始',
    budget: 500000,
    spent: 0,
    deadline: '2024-09-30',
    warnings: [],
    phases: {
      completed: [],
      current: '未开始'
    }
  }
]);

const pagination = {
  pageSize: 10,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
};

const getStatusClass = (status) => {
  const statusMap = {
    '未开始': 'status-not-started',
    '进行中': 'status-in-progress',
    '已关闭': 'status-closed'
  };
  return statusMap[status] || '';
};

const getPhaseIndex = (currentPhase) => {
  return phases.indexOf(currentPhase);
};

const getBudgetClass = (spent, budget) => {
  const ratio = spent / budget;
  if (ratio > 1) return 'budget-danger';
  if (ratio > 0.8) return 'budget-warning';
  return '';
};

const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const handleSearch = () => {
  // 实现搜索功能
  message.success('搜索功能待实现');
};

const handleReset = () => {
  searchText.value = '';
  selectedStatus.value = '';
  selectedWarning.value = '';
};

const showModal = () => {
  modalVisible.value = true;
};

const createProject = () => {
  formRef.value.validate().then(() => {
    // 实现创建项目的功能
    message.success('项目创建成功');
    modalVisible.value = false;
  }).catch(error => {
    console.log('验证失败:', error);
  });
};

const viewProject = (record) => {
  message.info(`查看项目: ${record.name}`);
};

const editProject = (record) => {
  message.info(`编辑项目: ${record.name}`);
};

const manageProject = (record) => {
  if (record.status === '未开始') {
    message.info(`删除项目: ${record.name}`);
  } else {
          router.push(`/workspace/project/detail?id=${record.key}`);
  }
};
</script>

<style scoped>

.search-filter-area {
  display: flex;
  margin-bottom: 20px;
  gap: 16px;
}

.project-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
}

.status-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.status-not-started {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-in-progress {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-closed {
  background-color: #f6ffed;
  color: #52c41a;
}

.risk-warning {
  color: #f5222d;
  font-weight: bold;
}

.budget-progress {
  width: 100%;
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.budget-progress-inner {
  height: 100%;
  background-color: #52c41a;
}

.budget-warning .budget-progress-inner {
  background-color: #faad14;
}

.budget-danger .budget-progress-inner {
  background-color: #f5222d;
}

.phase-tooltip {
  display: none;
  position: absolute;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  width: 600px;
  left: 0;
  top: 100%;
}

.phase-tooltip .ant-steps-item {
  min-width: 80px;
}

td:hover .phase-tooltip {
  display: block;
}
</style>
