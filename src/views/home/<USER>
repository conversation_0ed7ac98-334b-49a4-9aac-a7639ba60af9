<template>
  <div class="home-container">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img src="../../assets/images/logo.png" alt="研选工场" />
        </div>
        <div class="header-right">
          <a-button 
            v-if="!isLoggedIn"
            type="primary" 
            size="large" 
            @click="handleLogin"
            class="login-btn"
          >
            登录
          </a-button>
          <div v-else class="user-info">
            <a-button 
              type="primary" 
              size="large" 
              @click="handleWorkspaceClick"
              class="workspace-btn"
            >
              前往工作台
            </a-button>
            <a-avatar size="large" :src="userInfo.avatar">
              {{ userInfo.name?.charAt(0) }}
            </a-avatar>
            <span class="user-name">{{ userInfo.name }}</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">研选零部件库</h1>
          <!-- Search Bar -->
          <div class="search-container">
            <div class="search-bar">
              <a-button 
                type="default" 
                size="large" 
                class="category-btn"
                @click="handleCategoryClick"
              >
                全部分类
              </a-button>
              <a-input-search
                v-model:value="searchValue"
                placeholder="搜索零部件、供应商、品牌..."
                size="large"
                class="search-input"
                @search="handleSearch"
              >
                <template #enterButton>
                  <a-button type="primary" size="large">搜索</a-button>
                </template>
              </a-input-search>
            </div>
          </div>
        </div>
      </section>

      <!-- Brands Section -->
      <section class="brands-section">
        <div class="section-header">
          <h3>合作品牌</h3>
        </div>
        <div class="brands-grid">
          <div 
            v-for="brand in brands" 
            :key="brand.id"
            class="brand-card"
            @click="handleBrandClick(brand)"
          >
            <!-- 推荐标识 -->
            <div v-if="brand.isRecommended" class="recommended-badge">
              <StarFilled />
              推荐
            </div>
            
            <div class="brand-logo">
              <img v-if="brand.logo" :src="brand.logo" :alt="brand.name" />
              <div v-else class="brand-initial">{{ brand.initial }}</div>
            </div>
          </div>
        </div>
        
        <!-- 品牌加入引导 -->
        <div class="brand-join-section">
          <div class="join-content">
            <h4>想要成为我们的合作伙伴？</h4>
            <p>加入研选工场，让更多客户发现您的优质产品</p>
            <a-button 
              type="primary" 
              size="large" 
              class="join-btn"
              @click="handleJoinBrand"
            >
              了解详情
            </a-button>
          </div>
        </div>
        
        <!-- 品牌统计信息 -->
        <!-- <div class="brands-stats">
          <div class="stat-item">
            <div class="stat-number">{{ totalBrands }}+</div>
            <div class="stat-label">合作品牌</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ totalProducts }}+</div>
            <div class="stat-label">产品型号</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ totalCategories }}+</div>
            <div class="stat-label">产品类别</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98%</div>
            <div class="stat-label">客户满意度</div>
          </div>
        </div> -->
      </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>关于我们</h4>
          <p>致力于为自动化装备制造行业提供智能化、高效率的供应链解决方案</p>
        </div>
        <div class="footer-section">
          <h4>联系方式</h4>
          <p>400-888-9999</p>
          <p><EMAIL></p>
        </div>
        <div class="footer-section">
          <h4>服务支持</h4>
          <p>用户协议</p>
          <p>隐私政策</p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>Copyright©2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { StarFilled } from '@ant-design/icons-vue'

const router = useRouter()

// 搜索
const searchValue = ref('')

// 用户状态
const isLoggedIn = ref(false)
const userInfo = reactive({
  name: '张工程师',
  avatar: ''
})

// 品牌数据
const brands = ref([
  {
    id: 1,
    name: 'Siemens 西门子',
    initial: 'S',
    logo: '', // 暂时为空，只有logo信息
    isRecommended: true
  },
  {
    id: 2,
    name: 'ABB',
    initial: 'A',
    logo: '',
    isRecommended: true
  },
  {
    id: 3,
    name: 'Schneider Electric',
    initial: 'SE',
    logo: '',
    isRecommended: false
  },
  {
    id: 4,
    name: 'Omron 欧姆龙',
    initial: 'O',
    logo: '',
    isRecommended: true
  },
  {
    id: 5,
    name: 'Mitsubishi 三菱',
    initial: 'M',
    logo: '',
    isRecommended: false
  },
  {
    id: 6,
    name: 'Rockwell Automation',
    initial: 'RA',
    logo: '',
    isRecommended: false
  },
  {
    id: 7,
    name: 'FESTO 费斯托',
    initial: 'F',
    logo: '',
    isRecommended: true
  },
  {
    id: 8,
    name: 'SMC',
    initial: 'SMC',
    logo: '',
    isRecommended: false
  },
  {
    id: 9,
    name: 'Bosch Rexroth',
    initial: 'BR',
    logo: '',
    isRecommended: false
  },
  {
    id: 10,
    name: 'Phoenix Contact',
    initial: 'PC',
    logo: '',
    isRecommended: true
  },
  {
    id: 11,
    name: 'Pepperl+Fuchs',
    initial: 'PF',
    logo: '',
    isRecommended: false
  },
  {
    id: 12,
    name: 'Turck',
    initial: 'T',
    logo: '',
    isRecommended: false
  }
])

// 统计数据
const totalBrands = computed(() => brands.value.length)

// 事件处理
const handleLogin = () => {
  // 模拟登录
  isLoggedIn.value = true
}

const handleSearch = (value) => {
  console.log('搜索:', value)
  // 这里可以添加搜索逻辑
}

const handleCategoryClick = () => {
  console.log('点击全部分类')
  // 这里可以添加分类选择逻辑
}

const handleBrandClick = (brand) => {
  console.log('点击品牌:', brand.name)
  // 这里可以跳转到品牌详情页
}

const handleWorkspaceClick = () => {
  router.push('/workspace')
}

const handleJoinBrand = () => {
  console.log('品牌入驻申请')
  // 这里可以跳转到品牌入驻申请页面或打开申请表单
  // router.push('/brand/apply') 或者打开弹窗等
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1e 0%, #1a1a2b 50%, #0f0f1e 100%);
  background-attachment: fixed;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
}

.home-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at 30% 20%, rgba(249, 76, 48, 0.03) 0%, transparent 50%),
              radial-gradient(ellipse at 70% 80%, rgba(249, 76, 48, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(26, 26, 43, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(249, 76, 48, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 48px;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo img {
  width: 160px;
  object-fit: contain;
  filter: drop-shadow(0 0 8px rgba(249, 76, 48, 0.1));
}

.logo-text {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 20px rgba(249, 76, 48, 0.2);
}

.header-right {
  display: flex;
  align-items: center;
}

.login-btn {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border: none;
  height: 44px;
  padding: 0 28px;
  font-weight: 600;
  border-radius: 22px;
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.login-btn:hover {
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(249, 76, 48, 0.4);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.workspace-btn {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border: none;
  height: 44px;
  padding: 0 24px;
  font-weight: 600;
  border-radius: 22px;
  margin-right: 20px;
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.workspace-btn:hover {
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(249, 76, 48, 0.4);
}

.user-name {
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-top: 88px;
  position: relative;
  z-index: 1;
}

/* Hero Section */
.hero-section {
  padding: 120px 48px 100px;
  text-align: center;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center top, rgba(249, 76, 48, 0.08) 0%, transparent 60%);
  pointer-events: none;
}

.hero-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 64px;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 60px;
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(249, 76, 48, 0.1);
}

.hero-subtitle {
  font-size: 28px;
  font-weight: 600;
  color: #f94c30;
  margin-bottom: 20px;
  line-height: 1.3;
}

.hero-description {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Search Container in Hero */
.search-container {
  max-width: 720px;
  margin: 0 auto;
  position: relative;
}

.search-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 32px;
  filter: blur(20px);
  z-index: -1;
}

.search-bar {
  display: flex;
  gap: 0;
  align-items: stretch;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 28px;
  border: 1px solid rgba(249, 76, 48, 0.2);
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3),
              0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-bar:hover {
  border-color: rgba(249, 76, 48, 0.4);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),
              0 8px 30px rgba(249, 76, 48, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.category-btn {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.9);
  height: 56px;
  padding: 0 24px;
  font-weight: 600;
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-btn:hover {
  background: rgba(249, 76, 48, 0.15);
  color: #ffffff;
  transform: translateY(-1px);
}

.category-btn:hover::before {
  opacity: 1;
}

.search-input {
  flex: 1;
}

.search-input :deep(.ant-input-group-wrapper) {
  display: flex;
}

.search-input :deep(.ant-input-wrapper) {
  flex: 1;
}

.search-input :deep(.ant-input) {
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  height: 56px;
  padding: 0 24px;
  border-radius: 0;
}

.search-input :deep(.ant-input::placeholder) {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.search-input :deep(.ant-input:focus) {
  border: none;
  box-shadow: none;
  outline: none;
}

.search-input :deep(.ant-btn) {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border: none;
  height: 56px;
  padding: 0 32px;
  font-weight: 600;
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.search-input :deep(.ant-btn::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-input :deep(.ant-btn:hover) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.3);
}

.search-input :deep(.ant-btn:hover::before) {
  opacity: 1;
}

/* Brands Section */
.brands-section {
  padding: 100px 48px 120px;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.brands-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(249, 76, 48, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
  z-index: 1;
}

.section-header h3 {
  font-size: 42px;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 20px;
  letter-spacing: -0.01em;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.85) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.brands-grid {
  max-width: 1400px;
  margin: 0 auto 80px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  position: relative;
  z-index: 1;
}

.brand-card {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 20px;
  padding: 32px 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  min-height: 200px;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12),
              0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.brand-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.brand-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(249, 76, 48, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25),
              0 8px 30px rgba(249, 76, 48, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.brand-card:hover::before {
  opacity: 1;
}

.recommended-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #f94c30, #ff6b47);
  color: #ffffff;
  font-size: 11px;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(249, 76, 48, 0.3);
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recommended-badge .anticon {
  font-size: 10px;
}

.brand-logo {
  margin-bottom: 20px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.brand-logo img {
  width: 88px;
  height: 88px;
  border-radius: 16px;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.brand-card:hover .brand-logo img {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.brand-initial {
  width: 88px;
  height: 88px;
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  font-weight: 800;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: -0.5px;
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.25);
  transition: all 0.3s ease;
}

.brand-card:hover .brand-initial {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(249, 76, 48, 0.35);
}

.brand-name {
  text-align: center;
  position: relative;
  z-index: 1;
}

.brand-name h4 {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.brand-card:hover .brand-name h4 {
  color: #ffffff;
}

/* 品牌加入引导区域 */
.brand-join-section {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
}

.join-content {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(249, 76, 48, 0.2);
  border-radius: 28px;
  padding: 60px 48px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.join-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
}

.join-content:hover {
  border-color: rgba(249, 76, 48, 0.3);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2),
              0 8px 30px rgba(249, 76, 48, 0.1),
              0 0 0 1px rgba(255, 255, 255, 0.08) inset;
  transform: translateY(-4px);
}

.join-content h4 {
  font-size: 28px;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
  position: relative;
  z-index: 1;
}

.join-content p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 40px 0;
  line-height: 1.6;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

.join-btn {
  background: linear-gradient(135deg, #f94c30 0%, #ff6b47 100%);
  border: none;
  height: 52px;
  padding: 0 40px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 26px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(249, 76, 48, 0.3);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.join-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b47 0%, #f94c30 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.join-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(249, 76, 48, 0.4);
}

.join-btn:hover::before {
  opacity: 1;
}

/* 品牌统计区域 */
.brands-stats {
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 32px;
  padding: 40px;
  background: rgba(42, 42, 53, 0.5);
  border-radius: 20px;
  border: 1px solid rgba(249, 76, 48, 0.2);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #f94c30;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Footer */
.footer {
  background: rgba(15, 15, 30, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border-top: 1px solid rgba(249, 76, 48, 0.1);
  position: relative;
  margin-top: 100px;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 80px 48px 60px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 60px;
  position: relative;
  z-index: 1;
}

.footer-section h4 {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 24px;
  letter-spacing: -0.01em;
}

.footer-section p {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.7;
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.footer-section p:hover {
  color: rgba(255, 255, 255, 0.9);
}

.footer-bottom {
  text-align: center;
  padding: 30px 48px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  z-index: 1;
}

.footer-bottom p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    padding: 0 32px;
  }
  
  .hero-section {
    padding: 100px 32px 80px;
  }
  
  .hero-title {
    font-size: 56px;
  }
  
  .brands-section {
    padding: 80px 32px 100px;
  }
  
  .brands-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .footer-content {
    padding: 60px 32px 40px;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 24px;
    height: 72px;
  }
  
  .logo img {
    width: 140px;
  }
  
  .hero-section {
    padding: 80px 24px 60px;
  }
  
  .hero-title {
    font-size: 42px;
    margin-bottom: 48px;
  }
  
  .search-container {
    max-width: 100%;
  }
  
  .search-bar {
    flex-direction: column;
    border-radius: 20px;
  }
  
  .category-btn {
    width: 100%;
    height: 48px;
    border-radius: 20px 20px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .search-input :deep(.ant-input) {
    height: 48px;
    border-radius: 0;
  }
  
  .search-input :deep(.ant-btn) {
    height: 48px;
    border-radius: 0 0 20px 20px;
  }
  
  .brands-section {
    padding: 60px 24px 80px;
  }
  
  .section-header h3 {
    font-size: 32px;
  }
  
  .section-header {
    margin-bottom: 60px;
  }
  
  .brands-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
    margin-bottom: 60px;
  }
  
  .brand-card {
    padding: 24px 20px;
    min-height: 180px;
    border-radius: 16px;
  }
  
  .brand-logo img,
  .brand-initial {
    width: 72px;
    height: 72px;
  }
  
  .brand-initial {
    font-size: 22px;
  }
  
  .brand-name h4 {
    font-size: 15px;
  }
  
  .join-content {
    padding: 40px 32px;
    border-radius: 20px;
  }
  
  .join-content h4 {
    font-size: 24px;
  }
  
  .join-content p {
    font-size: 16px;
  }
  
  .join-btn {
    height: 48px;
    padding: 0 32px;
  }
  
  .footer-content {
    padding: 50px 24px 30px;
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .footer-bottom {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .main-content {
    margin-top: 72px;
  }
  
  .header-content {
    padding: 0 20px;
  }
  
  .logo img {
    width: 120px;
  }
  
  .hero-section {
    padding: 60px 20px 40px;
  }
  
  .hero-title {
    font-size: 32px;
    margin-bottom: 40px;
  }
  
  .search-container::before {
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
  }
  
  .search-bar {
    border-radius: 16px;
  }
  
  .category-btn {
    height: 44px;
    border-radius: 16px 16px 0 0;
    font-size: 15px;
  }
  
  .search-input :deep(.ant-input) {
    height: 44px;
    font-size: 15px;
  }
  
  .search-input :deep(.ant-btn) {
    height: 44px;
    border-radius: 0 0 16px 16px;
    font-size: 15px;
  }
  
  .brands-section {
    padding: 50px 20px 60px;
  }
  
  .section-header h3 {
    font-size: 28px;
  }
  
  .brands-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .brand-card {
    padding: 20px 16px;
    min-height: 160px;
    border-radius: 16px;
  }
  
  .brand-logo img,
  .brand-initial {
    width: 64px;
    height: 64px;
  }
  
  .brand-initial {
    font-size: 18px;
  }
  
  .brand-name h4 {
    font-size: 14px;
  }
  
  .join-content {
    padding: 32px 24px;
  }
  
  .join-content h4 {
    font-size: 22px;
  }
  
  .join-content p {
    font-size: 15px;
    margin-bottom: 32px;
  }
  
  .join-btn {
    height: 44px;
    padding: 0 28px;
    font-size: 15px;
  }
  
  .footer-content {
    padding: 40px 20px 24px;
    gap: 32px;
  }
  
  .footer-section h4 {
    font-size: 18px;
  }
  
  .footer-section p {
    font-size: 14px;
  }
  
  .footer-bottom {
    padding: 20px;
  }
}
</style>