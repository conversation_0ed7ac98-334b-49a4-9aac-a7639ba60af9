<template>
  <div class="fav-supplier-container">
    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-card">
        <div class="filters-panel">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="供应商名称">
                <a-input v-model:value="searchForm.keyword" placeholder="搜索收藏的供应商" allow-clear @pressEnter="handleSearch" />
              </a-form-item>
            </a-col>

            <!-- <a-col :span="6">
              <a-form-item label="供应商类型">
                <a-select v-model:value="searchForm.supplierType" placeholder="筛选类型" allow-clear>
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="recommended">推荐品牌商</a-select-option>
                </a-select>
              </a-form-item>
            </a-col> -->

            <a-col :span="6">
              <a-form-item style="margin-bottom: 0">
                <a-space>
                  <a-button type="primary" @click="handleSearch" :loading="loading">
                    <i class="fas fa-search"></i>
                    搜索
                  </a-button>
                  <a-button @click="handleResetFilters">
                    <i class="fas fa-undo"></i>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>

    <!-- Tab Section -->
    <div class="tab-section">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange" class="favorites-tabs">
        <a-tab-pane key="my" tab="我的收藏">
          <template #tab>
            <span>
              <i class="fas fa-heart"></i>
              我的收藏 ({{ myFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="public" tab="公开的收藏">
          <template #tab>
            <span>
              <i class="fas fa-share-alt"></i>
              公开的收藏 ({{ publicFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="all" tab="全部">
          <template #tab>
            <span>
              <i class="fas fa-list"></i>
              全部 ({{ totalFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions-bar">
      <div class="actions-left">
        <a-space v-if="activeTab === 'my' && selectedSuppliers.length > 0">
          <!-- <span class="selection-info">已选择 {{ selectedSuppliers.length }} 个供应商</span>
          <a-button @click="handleBatchEdit">
            <i class="fas fa-edit"></i>
            批量编辑备注
          </a-button>
          <a-button @click="handleBatchSetPublic">
            <i class="fas fa-share-alt"></i>
            批量设为公开
          </a-button>
          <a-button danger @click="handleBatchRemove">
            <i class="fas fa-heart-broken"></i>
            批量取消收藏
          </a-button> -->
        </a-space>
      </div>

      <div class="actions-right">
        <a-space>
          <!-- View Mode Toggle -->
          <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
            <a-radio-button value="card">
              <i class="fas fa-th-large"></i>
              卡片
            </a-radio-button>
            <a-radio-button value="list">
              <i class="fas fa-list"></i>
              列表
            </a-radio-button>
          </a-radio-group>
        </a-space>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Card View -->
      <div v-if="viewMode === 'card'" class="card-view">
        <div v-if="displayedSuppliers.length === 0" class="empty-state">
          <a-empty :description="getEmptyDescription()">
            <template #image>
              <i class="fas fa-heart-broken" style="font-size: 48px; color: #d9d9d9"></i>
            </template>
                            <a-button type="primary" @click="$router.push('/workspace/supplier/index')">
              <i class="fas fa-search"></i>
              去发现供应商
            </a-button>
          </a-empty>
        </div>

        <div v-else class="supplier-cards-container">
          <div v-for="supplier in displayedSuppliers" :key="supplier.id" class="supplier-card-wrapper">
            <a-card class="supplier-card" :class="{ selected: selectedSuppliers.includes(supplier.id) }">
              <div class="card-content">
                <!-- Header with logo, name, and actions -->
                <div class="supplier-header">
                  <!-- Checkbox for my favorites only -->
                  <a-checkbox 
                    v-if="activeTab === 'my' || (activeTab === 'all' && supplier.isMyFavorite)"
                    :checked="selectedSuppliers.includes(supplier.id)"
                    @change="() => handleSelectSupplier(supplier.id)"
                    class="card-checkbox"
                  />

                  <div class="supplier-logo">
                    <img v-if="supplier.logoUrl" :src="supplier.logoUrl" :alt="supplier.companyName" @error="handleLogoError" />
                    <div v-else class="default-logo">
                      <i class="fas fa-building"></i>
                    </div>
                  </div>

                  <div class="supplier-name-section">
                    <div class="supplier-name">{{ supplier.companyName }}</div>
                    <div class="supplier-tags">
                      <!-- <a-tag v-if="supplier.supplierType === 'recommended'" color="gold" class="recommended-tag" size="small">
                        <i class="fas fa-crown"></i>
                        推荐品牌商
                      </a-tag> -->
                      <a-tag color="blue" size="small">{{ supplier.factoryLevel }}</a-tag>
                      <a-tag v-if="supplier.isPublic && supplier.isMyFavorite" color="green" size="small">
                        <i class="fas fa-share-alt"></i>
                        已公开
                      </a-tag>
                    </div>
                  </div>

                  <div class="header-actions">
                    <!-- 操作菜单 -->
                    <a-dropdown v-if="supplier.isMyFavorite" :trigger="['click']" placement="bottomRight">
                      <a-button type="text" size="small" class="action-menu-btn">
                        <MoreOutlined />                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="edit-note" @click="handleAddNote(supplier)">
                            <i class="fas fa-edit"></i>
                            编辑备注
                          </a-menu-item>
                          <a-menu-item key="toggle-public" @click="handleTogglePublic(supplier)">
                            <i :class="supplier.isPublic ? 'fas fa-eye-slash' : 'fas fa-share-alt'"></i>
                            {{ supplier.isPublic ? '取消公开' : '设为公开' }}
                          </a-menu-item>
                          <a-menu-item key="remove" @click="handleRemoveFromFavorites(supplier)" class="danger-item">
                            <i class="fas fa-heart-broken"></i>
                            取消收藏
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                    
                    <!-- 状态指示器 -->
                    <div class="status-indicators">
                      <a-tooltip v-if="supplier.isPublic && supplier.isMyFavorite" title="已公开">
                        <i class="fas fa-share-alt status-icon public"></i>
                      </a-tooltip>
                      <a-tooltip v-if="supplier.isMyFavorite" title="我的收藏">
                        <i class="fas fa-heart status-icon favorite"></i>
                      </a-tooltip>
                    </div>
                  </div>
                </div>

                <!-- Location -->
                <div class="supplier-location">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>{{ supplier.region }}</span>
                </div>

                <!-- 详细信息区域 -->
                <div class="supplier-details">
                  <div class="detail-row">
                    <span class="detail-label">行业类别</span>
                    <span class="detail-value">{{ supplier.industry }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">成立时间</span>
                    <span class="detail-value">{{ formatTimestamp(supplier.establishmentTime) }}年</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">注册资本</span>
                    <span class="detail-value">{{ supplier.registeredCapital }}</span>
                  </div>
                </div>
                <a-divider />
                <div class="supplier-details">
                  <div class="detail-row">
                    <span class="detail-label">收藏时间</span>
                    <div class="time-value">{{ formatDate(supplier.favoriteTime) }}</div>
                  </div>
                  <div class="detail-row" v-if="!supplier.isMyFavorite">
                    <span class="detail-label">收藏人</span>
                    <div class="favoriter-info">
                      {{ supplier.favoriterName }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">备注</span>
                    <div class="note-content">{{ supplier.note || '暂无备注' }}</div>
                  </div>
                </div>

                <!-- Action buttons -->
                <div class="card-actions">
                  <a-button type="primary" @click.stop="handleViewSupplierDetail(supplier)" block>
                    <i class="fas fa-eye"></i>
                    查看详情
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>
        </div>

        <!-- Card View Pagination -->
        <div class="card-pagination" v-if="displayedSuppliers.length > 0">
          <a-pagination v-model:current="pagination.current" v-model:page-size="pagination.pageSize" :total="pagination.total" :show-size-changer="true" :show-quick-jumper="true" :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`" @change="handlePaginationChange" />
        </div>
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="list-view">
        <a-table :columns="getTableColumns()" :data-source="displayedSuppliers" :pagination="tablePagination" :loading="loading" :row-selection="getRowSelection()" row-key="id" @change="handleTableChange" class="supplier-table" size="small" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="supplier-name-cell">
                <a @click="handleViewSupplierDetail(record)" style="color: #f94c30">{{ record.companyName }}</a>
                <div class="name-tags">
                  <a-tag v-if="record.isPublic && record.isMyFavorite" color="green" size="small">
                    <i class="fas fa-share-alt"></i>
                    已公开
                  </a-tag>
                </div>
              </div>
            </template>

            <template v-if="column.key === 'favoriter'">
              <div v-if="!record.isMyFavorite" class="favoriter-cell">
                <a-avatar :size="24" style="margin-right: 8px">{{ record.favoriterName?.[0] }}</a-avatar>
                {{ record.favoriterName }}
              </div>
              <span v-else class="self-favorited">我的收藏</span>
            </template>

            <template v-if="column.key === 'favoriteTime'">
              {{ formatDate(record.favoriteTime) }}
            </template>

            <template v-if="column.key === 'note'">
              <div class="note-cell">
                <span v-if="record.note" class="note-text">{{ record.note }}</span>
                <span v-else class="no-note">暂无备注</span>
                <a-button v-if="record.isMyFavorite" type="link" size="small" @click="handleAddNote(record)">
                  <i class="fas fa-edit"></i>
                </a-button>
              </div>
            </template>

            <template v-if="column.key === 'actions'">
              <a-space>
                <template v-if="record.isMyFavorite">
                  <a-button type="link" size="small" @click="handleAddNote(record)">
                    <i class="fas fa-edit"></i>
                    编辑备注
                  </a-button>
                  <a-button type="link" size="small" @click="handleTogglePublic(record)">
                    <i :class="record.isPublic ? 'fas fa-eye-slash' : 'fas fa-share-alt'"></i>
                    {{ record.isPublic ? '取消公开' : '设为公开' }}
                  </a-button>
                  <a-button type="link" size="small" danger @click="handleRemoveFromFavorites(record)">
                    <i class="fas fa-heart-broken"></i>
                    取消收藏
                  </a-button>
                </template>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- Modals -->
    <!-- Supplier Detail Modal -->
    <a-modal v-model:open="showSupplierDetailModal" title="供应商详情" width="800px" :footer="null" class="supplier-detail-modal">
      <div v-if="selectedSupplierDetail" class="supplier-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="企业名称">{{ selectedSupplierDetail.companyName }}</a-descriptions-item>
          <!-- <a-descriptions-item label="供应商类型">
            <a-tag v-if="selectedSupplierDetail.supplierType === 'recommended'" color="gold">
              <i class="fas fa-crown"></i>
              推荐品牌商
            </a-tag>
            <span v-else>普通供应商</span>
          </a-descriptions-item> -->
          <a-descriptions-item label="所在地区">{{ selectedSupplierDetail.region }}</a-descriptions-item>
          <a-descriptions-item label="行业类别">{{ selectedSupplierDetail.industry }}</a-descriptions-item>
          <a-descriptions-item label="工厂等级">{{ selectedSupplierDetail.factoryLevel }}</a-descriptions-item>
          <a-descriptions-item label="等级分数">{{ selectedSupplierDetail.levelScore }}分</a-descriptions-item>
          <a-descriptions-item label="成立时间">{{ formatTimestamp(selectedSupplierDetail.establishmentTime) }}年</a-descriptions-item>
          <a-descriptions-item label="注册资本">{{ selectedSupplierDetail.registeredCapital }}</a-descriptions-item>
          <a-descriptions-item label="收藏时间">{{ formatDate(selectedSupplierDetail.favoriteTime) }}</a-descriptions-item>
          <a-descriptions-item v-if="!selectedSupplierDetail.isMyFavorite" label="收藏人">
            {{ selectedSupplierDetail.favoriterName }}
          </a-descriptions-item>
          <a-descriptions-item label="收藏状态">
            <a-space>
              <a-tag v-if="selectedSupplierDetail.isMyFavorite" color="red">
                <i class="fas fa-heart"></i>
                我的收藏
              </a-tag>
              <a-tag v-if="selectedSupplierDetail.isPublic && selectedSupplierDetail.isMyFavorite" color="green">
                <i class="fas fa-share-alt"></i>
                已公开
              </a-tag>
            </a-space>
          </a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">
            {{ selectedSupplierDetail.note || '暂无备注' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- <div class="detail-actions" v-if="selectedSupplierDetail.isMyFavorite">
          <a-space>
            <a-button @click="handleAddNote(selectedSupplierDetail)">
              <i class="fas fa-edit"></i>
              编辑备注
            </a-button>
            <a-button @click="handleTogglePublic(selectedSupplierDetail)" :type="selectedSupplierDetail.isPublic ? 'default' : 'primary'">
              <i :class="selectedSupplierDetail.isPublic ? 'fas fa-eye-slash' : 'fas fa-share-alt'"></i>
              {{ selectedSupplierDetail.isPublic ? '取消公开' : '设为公开' }}
            </a-button>
            <a-button danger @click="handleRemoveFromFavorites(selectedSupplierDetail)">
              <i class="fas fa-heart-broken"></i>
              取消收藏
            </a-button>
          </a-space>
        </div> -->
      </div>
    </a-modal>

    <!-- Note Modal -->
    <a-modal v-model:open="showNoteModal" title="编辑备注" width="500px" @ok="handleSaveNote">
      <a-form layout="vertical">
        <a-form-item label="备注信息">
          <a-textarea v-model:value="noteForm.note" placeholder="请输入备注信息..." :rows="4" show-count :maxlength="200" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Batch Edit Modal -->
    <a-modal v-model:open="showBatchEditModal" title="批量编辑备注" width="500px" @ok="handleSaveBatchEdit">
      <a-form layout="vertical">
        <a-form-item label="备注信息">
          <a-textarea v-model:value="batchEditForm.note" placeholder="请输入备注信息..." :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Batch Public Modal -->
    <a-modal v-model:open="showBatchPublicModal" title="批量设为公开" width="500px" @ok="handleSaveBatchPublic">
      <p>确定要将选中的 {{ selectedSuppliers.length }} 个供应商设为公开吗？公开后，同企业的其他成员将能够看到这些收藏。</p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { MoreOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// ==================== Reactive Data ====================

// Loading states
const loading = ref(false);

// View mode
const viewMode = ref('card'); // 'list', 'card'
const sortBy = ref('addTime');

// Search and filter form
const searchForm = reactive({
  keyword: '',
  supplierType: '',
});

// UI states
const showSupplierDetailModal = ref(false);
const showNoteModal = ref(false);
const showBatchEditModal = ref(false);
const showBatchPublicModal = ref(false);

// Tab state
const activeTab = ref('my');

// Selection states
const selectedSuppliers = ref([]);
const selectedSupplierDetail = ref(null);
const currentEditSupplier = ref(null);

// Form states
const noteForm = reactive({
  note: '',
});

const batchEditForm = reactive({
  note: '',
});

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0,
});

// Table pagination (for list view)
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
}));

// Statistics
const favStats = reactive({
  total: 34,
  recommended: 12,
  thisMonth: 5,
});

// Supplier data
const supplierList = ref([]); // This will hold all suppliers, regardless of tab
const myFavoritesCount = ref(0);
const publicFavoritesCount = ref(0);
const totalFavoritesCount = ref(0);

// Mock supplier data for favorites
const mockFavoriteData = [
  {
    id: '1',
    companyName: '深圳精密机械有限公司',
    region: '广东省深圳市',
    logoUrl: 'https://via.placeholder.com/80x80/1890ff/ffffff?text=深圳精密',
    factoryLevel: 'A级',
    levelScore: 95,
    industry: '机械制造',
    establishmentTime: 1420070400000,
    registeredCapital: '500万人民币',
    supplierType: 'recommended',
    favoriteTime: 1640995200000, // 2022-01-01
    note: '优质供应商，产品质量稳定，交期准时',
    isFavorited: true, // Added for new functionality
    isMyFavorite: true, // Added for new functionality
    isPublic: false, // Added for new functionality
    favoriterName: '张三', // Added for new functionality
  },
  {
    id: '2',
    companyName: '苏州电子科技股份有限公司',
    region: '江苏省苏州市',
    logoUrl: 'https://via.placeholder.com/80x80/52c41a/ffffff?text=苏州电子',
    factoryLevel: 'B级',
    levelScore: 88,
    industry: '电子电气',
    establishmentTime: 1325376000000,
    registeredCapital: '800万人民币',
    supplierType: 'normal',
    favoriteTime: 1641081600000,
    note: '',
    isFavorited: true, // Added for new functionality
    isMyFavorite: true, // Added for new functionality
    isPublic: false, // Added for new functionality
    favoriterName: '李四', // Added for new functionality
  },
  {
    id: '3',
    companyName: '上海智能制造有限公司',
    region: '上海市浦东新区',
    logoUrl: '',
    factoryLevel: 'A级',
    levelScore: 92,
    industry: '自动化设备',
    establishmentTime: 1483200000000,
    registeredCapital: '1200万人民币',
    supplierType: 'recommended',
    favoriteTime: 1641168000000,
    note: '技术实力强，适合长期合作',
    isFavorited: true, // Added for new functionality
    isMyFavorite: true, // Added for new functionality
    isPublic: false, // Added for new functionality
    favoriterName: '王五', // Added for new functionality
  },
  {
    id: '4',
    companyName: '北京新材料科技有限公司',
    region: '北京市海淀区',
    logoUrl: 'https://via.placeholder.com/80x80/ffc107/ffffff?text=北京新材料',
    factoryLevel: 'A级',
    levelScore: 90,
    industry: '新材料',
    establishmentTime: 1500000000000,
    registeredCapital: '1000万人民币',
    supplierType: 'normal',
    favoriteTime: 1641254400000,
    note: '服务态度好，价格合理',
    isFavorited: true, // Added for new functionality
    isMyFavorite: false, // Added for new functionality
    isPublic: true, // Added for new functionality
    favoriterName: '赵六', // Added for new functionality
  },
  {
    id: '5',
    companyName: '广州化工有限公司',
    region: '广东省广州市',
    logoUrl: 'https://via.placeholder.com/80x80/007bff/ffffff?text=广州化工',
    factoryLevel: 'B级',
    levelScore: 85,
    industry: '化工',
    establishmentTime: 1450000000000,
    registeredCapital: '500万人民币',
    supplierType: 'recommended',
    favoriteTime: 1641340800000,
    note: '产品质量稳定，交期准时',
    isFavorited: true, // Added for new functionality
    isMyFavorite: false, // Added for new functionality
    isPublic: false, // Added for new functionality
    favoriterName: '钱七', // Added for new functionality
  },
];

// ==================== Computed Properties ====================

// Row selection for table
const rowSelection = computed(() => ({
  selectedRowKeys: selectedSuppliers.value,
  onChange: (selectedRowKeys) => {
    selectedSuppliers.value = selectedRowKeys;
  },
}));

// Table columns
const tableColumns = ref([
  {
    title: '供应商名称',
    dataIndex: 'companyName',
    key: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '所在地区',
    dataIndex: 'region',
    key: 'location',
    width: 140,
  },
  {
    title: '行业类别',
    dataIndex: 'industry',
    key: 'industry',
    width: 120,
  },
  {
    title: '收藏时间',
    key: 'favoriteTime',
    width: 120,
    sorter: true,
  },
  {
    title: '备注',
    key: 'note',
    width: 200,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
  },
]);

// Get columns based on active tab
const getTableColumns = () => {
  if (activeTab.value === 'my') {
    return [
      {
        title: '供应商名称',
        dataIndex: 'companyName',
        key: 'name',
        width: 200,
        fixed: 'left',
      },
      {
        title: '所在地区',
        dataIndex: 'region',
        key: 'location',
        width: 140,
      },
      {
        title: '行业类别',
        dataIndex: 'industry',
        key: 'industry',
        width: 120,
      },
      {
        title: '收藏时间',
        key: 'favoriteTime',
        width: 120,
        sorter: true,
      },
      {
        title: '备注',
        key: 'note',
        width: 200,
      },
      {
        title: '收藏人',
        key: 'favoriter',
        width: 100,
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right',
      },
    ];
  } else {
    return [
      {
        title: '供应商名称',
        dataIndex: 'companyName',
        key: 'name',
        width: 200,
        fixed: 'left',
      },
      {
        title: '所在地区',
        dataIndex: 'region',
        key: 'location',
        width: 140,
      },
      {
        title: '行业类别',
        dataIndex: 'industry',
        key: 'industry',
        width: 120,
      },
      {
        title: '收藏时间',
        key: 'favoriteTime',
        width: 120,
        sorter: true,
      },
      {
        title: '备注',
        key: 'note',
        width: 200,
      },
      {
        title: '收藏人',
        key: 'favoriter',
        width: 100,
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right',
      },
    ];
  }
};

// Get row selection based on active tab
const getRowSelection = () => {
  if (activeTab.value === 'my') {
    return rowSelection.value;
  } else {
    return null; // No row selection for public tab
  }
};

// Get displayed suppliers based on active tab
const displayedSuppliers = computed(() => {
  let filteredData = [...mockFavoriteData];

  // Filter by active tab
  if (activeTab.value === 'my') {
    filteredData = filteredData.filter(supplier => supplier.isMyFavorite);
  } else if (activeTab.value === 'public') {
    filteredData = filteredData.filter(supplier => supplier.isPublic);
  }

  // Keyword search
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase();
    filteredData = filteredData.filter((supplier) => supplier.companyName.toLowerCase().includes(keyword));
  }

  // Supplier type filter
  if (searchForm.supplierType) {
    filteredData = filteredData.filter((supplier) => supplier.supplierType === searchForm.supplierType);
  }

  // Sort data
  if (sortBy.value === 'addTime') {
    filteredData.sort((a, b) => b.favoriteTime - a.favoriteTime);
  } else if (sortBy.value === 'companyName') {
    filteredData.sort((a, b) => a.companyName.localeCompare(b.companyName));
  }

  // Update pagination
  pagination.total = filteredData.length;
  const startIndex = (pagination.current - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;

  return filteredData.slice(startIndex, endIndex);
});

// Get empty description based on active tab
const getEmptyDescription = () => {
  if (activeTab.value === 'my') {
    return '暂无我的收藏供应商';
  } else if (activeTab.value === 'public') {
    return '暂无公开的收藏供应商';
  } else {
    return '暂无收藏的供应商';
  }
};

// ==================== Methods ====================

// Handle search
const handleSearch = async () => {
  loading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 500));

    let filteredData = [...mockFavoriteData];

    // Filter by active tab
    if (activeTab.value === 'my') {
      filteredData = filteredData.filter(supplier => supplier.isMyFavorite);
    } else if (activeTab.value === 'public') {
      filteredData = filteredData.filter(supplier => supplier.isPublic);
    }

    // Keyword search
    if (searchForm.keyword) {
      const keyword = searchForm.keyword.toLowerCase();
      filteredData = filteredData.filter((supplier) => supplier.companyName.toLowerCase().includes(keyword));
    }

    // Supplier type filter
    if (searchForm.supplierType) {
      filteredData = filteredData.filter((supplier) => supplier.supplierType === searchForm.supplierType);
    }

    // Sort data
    if (sortBy.value === 'addTime') {
      filteredData.sort((a, b) => b.favoriteTime - a.favoriteTime);
    } else if (sortBy.value === 'companyName') {
      filteredData.sort((a, b) => a.companyName.localeCompare(b.companyName));
    }

    // Update pagination
    pagination.total = filteredData.length;
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;

    supplierList.value = filteredData.slice(startIndex, endIndex);
  } catch (error) {
    message.error('搜索失败，请重试');
  } finally {
    loading.value = false;
  }
};

// Handle reset filters
const handleResetFilters = () => {
  searchForm.keyword = '';
  searchForm.supplierType = '';
  pagination.current = 1;
  handleSearch();
};

// Handle tab change
const handleTabChange = (key) => {
  activeTab.value = key;
  selectedSuppliers.value = [];
  pagination.current = 1;
  handleSearch();
};

// Handle pagination change
const handlePaginationChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  handleSearch();
};

// Handle table change
const handleTableChange = (pag, _filters, sorter) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;

  if (sorter.field) {
    if (sorter.field === 'favoriteTime') {
      sortBy.value = 'addTime';
    }
  }

  handleSearch();
};

// Handle supplier selection
const handleSelectSupplier = (supplierId) => {
  const index = selectedSuppliers.value.indexOf(supplierId);
  if (index > -1) {
    selectedSuppliers.value.splice(index, 1);
  } else {
    selectedSuppliers.value.push(supplierId);
  }
};

// Handle view supplier detail
const handleViewSupplierDetail = (supplier) => {
  selectedSupplierDetail.value = supplier;
  showSupplierDetailModal.value = true;
};

// Handle logo error
const handleLogoError = (event) => {
  event.target.style.display = 'none';
  const defaultLogo = event.target.nextElementSibling;
  if (defaultLogo) {
    defaultLogo.style.display = 'flex';
  }
};
// Handle add note
const handleAddNote = (supplier) => {
  currentEditSupplier.value = supplier;
  noteForm.note = supplier.note || '';
  showNoteModal.value = true;
};

// Handle save note
const handleSaveNote = () => {
  if (currentEditSupplier.value) {
    currentEditSupplier.value.note = noteForm.note;
    message.success('备注保存成功');
    showNoteModal.value = false;
  }
};

// Handle remove from favorites
const handleRemoveFromFavorites = (supplier) => {
  // 只改变收藏状态，不从列表中移除
  supplier.isFavorited = false;
  supplier.isMyFavorite = false; // Ensure it's marked as my favorite
  supplier.isPublic = false; // Ensure it's not public
  supplier.favoriterName = null; // Clear favoriter name
  message.success(`已将 ${supplier.companyName} 从收藏夹移除`);

  // Update stats
  favStats.total--;
  myFavoritesCount.value--;
  if (supplier.supplierType === 'recommended') {
    favStats.recommended--;
  }

  // Close modal if open
  if (showSupplierDetailModal.value) {
    showSupplierDetailModal.value = false;
  }
};

// Handle add to favorites (重新收藏)
const handleAddToFavorites = (supplier) => {
  supplier.isFavorited = true;
  supplier.isMyFavorite = true; // Mark as my favorite
  supplier.isPublic = false; // Ensure it's not public
  supplier.favoriterName = '张三'; // Placeholder favoriter name
  supplier.favoriteTime = Date.now(); // 更新收藏时间
  message.success(`已将 ${supplier.companyName} 添加到收藏夹`);

  // Update stats
  favStats.total++;
  myFavoritesCount.value++;
  if (supplier.supplierType === 'recommended') {
    favStats.recommended++;
  }
};

// Handle batch edit
const handleBatchEdit = () => {
  if (selectedSuppliers.value.length === 0) {
    message.warning('请先选择要编辑的供应商');
    return;
  }
  showBatchEditModal.value = true;
};

// Handle save batch edit
const handleSaveBatchEdit = () => {
  const selectedData = supplierList.value.filter((s) => selectedSuppliers.value.includes(s.id));

  selectedData.forEach((supplier) => {
    supplier.note = batchEditForm.note;
  });

  message.success(`已批量添加 ${selectedData.length} 家供应商的备注`);
  selectedSuppliers.value = [];
  showBatchEditModal.value = false;
};

// Handle batch remove
const handleBatchRemove = () => {
  if (selectedSuppliers.value.length === 0) {
    message.warning('请先选择要移除的供应商');
    return;
  }

  const selectedData = supplierList.value.filter((s) => selectedSuppliers.value.includes(s.id));
  const names = selectedData.map((s) => s.companyName).join('、');

  selectedSuppliers.value.forEach((id) => {
    const index = supplierList.value.findIndex((s) => s.id === id);
    if (index > -1) {
      supplierList.value.splice(index, 1);
    }
  });

  message.success(`已批量取消收藏：${names}`);
  selectedSuppliers.value = [];

  // Update stats
  favStats.total -= selectedData.length;
      myFavoritesCount.value -= selectedData.filter(s => s.isMyFavorite).length;
    const recommendedCount = selectedData.filter((s) => s.supplierType === 'recommended').length;
    favStats.recommended -= recommendedCount;
};

// Handle batch set public
const handleBatchSetPublic = () => {
  if (selectedSuppliers.value.length === 0) {
    message.warning('请先选择要设为公开的供应商');
    return;
  }
  showBatchPublicModal.value = true;
};

// Handle save batch public
const handleSaveBatchPublic = () => {
  const selectedData = supplierList.value.filter((s) => selectedSuppliers.value.includes(s.id));

  selectedData.forEach((supplier) => {
    supplier.isPublic = true;
  });

  message.success(`已批量将 ${selectedData.length} 家供应商设为公开`);
  selectedSuppliers.value = [];
  showBatchPublicModal.value = false;
};

// Handle toggle public
const handleTogglePublic = (supplier) => {
  supplier.isPublic = !supplier.isPublic;
  message.success(`已将 ${supplier.companyName} 设为${supplier.isPublic ? '公开' : '私密'}`);
};

// Utility functions
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN');
};

const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.getFullYear();
};

// ==================== Lifecycle ====================

onMounted(() => {
  // Initialize supplier list with mock data, only show favorited suppliers
  const favoritedData = mockFavoriteData.filter(supplier => supplier.isFavorited);
  supplierList.value = favoritedData.slice(0, pagination.pageSize);
  pagination.total = favoritedData.length;
  
  // Update stats based on favorited suppliers
  favStats.total = favoritedData.length;
  favStats.recommended = favoritedData.filter(s => s.supplierType === 'recommended').length;
  const thisMonth = new Date();
  thisMonth.setMonth(thisMonth.getMonth() - 1);
  favStats.thisMonth = favoritedData.filter(s => s.favoriteTime > thisMonth.getTime()).length;

  // Initialize counts for tabs
  myFavoritesCount.value = favoritedData.filter(s => s.isMyFavorite).length;
  publicFavoritesCount.value = favoritedData.filter(s => s.isPublic).length;
  totalFavoritesCount.value = favoritedData.length;
});
</script>

<style lang="less" scoped>
.fav-supplier-container {
  min-height: 100vh;

  .search-filter-section {
    .search-card {
      background: white;
      border-radius: 8px;

      .filters-panel {
        :deep(.ant-form-item) {
          margin-bottom: 16px;
        }

        :deep(.ant-form-item-label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .tab-section {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;

    .favorites-tabs {
      :deep(.ant-tabs-nav-wrap) {
        margin-bottom: 0;
      }
      :deep(.ant-tabs-nav) {
        margin-bottom: 0;
      }
      :deep(.ant-tabs-tab) {
        font-weight: 500;
        color: #333;
      }
      :deep(.ant-tabs-tab-active) {
        color: #f94c30;
      }
      :deep(.ant-tabs-ink-bar) {
        background-color: #f94c30;
      }
    }
  }

  .quick-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;

    .actions-left {
      .selection-info {
        color: #666;
        font-size: 14px;
        margin-right: 8px;
      }
    }
  }

  .selection-summary {
    position: sticky;
    bottom: 0;
    margin-bottom: 16px;
    background-color: #fff4f0;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #ffa39e;
    display: flex;
    justify-content: space-between;
    z-index: 1;

    .summary-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .main-content {
    .empty-state {
      background: white;
      border-radius: 8px;
      padding: 60px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .card-view {
      .supplier-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;

        .supplier-card-wrapper {
          display: flex;
        }
      }

              .supplier-card {
          border-radius: 12px;
          transition: all 0.3s ease;
          border: 1px solid #e8e8e8;
          overflow: hidden;
          width: 100%;
          height: 100%;
          min-height: 380px;

          &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
            border-color: #d9d9d9;
          }

          &.selected {
            border-color: #f94c30;
            box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.2);
          }

          :deep(.ant-card-body) {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
          }

        .card-content {
          display: flex;
          flex-direction: column;
          height: 100%;

          .supplier-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;

            .card-checkbox {
              margin-top: 4px;
              flex-shrink: 0;
            }

            .supplier-logo {
              width: 50px;
              height: 50px;
              flex-shrink: 0;
              border-radius: 8px;
              overflow: hidden;
              border: 1px solid #e8e8e8;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .default-logo {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;

                i {
                  font-size: 20px;
                }
              }
            }

            .supplier-name-section {
              flex: 1;
              min-width: 0;

              .supplier-name {
                font-weight: 600;
                color: #333;
                font-size: 16px;
                line-height: 1.4;
                margin-bottom: 6px;
                word-break: break-word;
              }

              .supplier-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;

                .recommended-tag {
                  font-weight: 500;
                  border: none;

                  i {
                    margin-right: 4px;
                  }
                }
              }
            }

            .header-actions {
              flex-shrink: 0;
              display: flex;
              align-items: center;
              gap: 8px;

              .action-menu-btn {
                color: #666;
                transition: all 0.2s ease;
                
                &:hover {
                  color: #f94c30;
                  background-color: #f5f5f5;
                }
              }

              .status-indicators {
                display: flex;
                gap: 4px;
                align-items: center;

                .status-icon {
                  font-size: 12px;
                  padding: 2px;
                  border-radius: 50%;

                  &.favorite {
                    color: #f94c30;
                  }

                  &.public {
                    color: #52c41a;
                  }
                }
              }
            }
          }

          .supplier-location {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 13px;
            margin-bottom: 16px;

            i {
              margin-right: 6px;
              color: #f94c30;
            }
          }

          .supplier-details {
            margin-bottom: 16px;
            flex: 1;

            .detail-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              font-size: 13px;

              &:last-child {
                margin-bottom: 0;
              }

              .detail-label {
                color: #666;
                font-weight: 400;
              }

              .detail-value {
                color: #333;
                font-weight: 500;
                text-align: right;
              }

              .time-value {
                font-size: 13px;
                color: #333;
                font-weight: 500;
              }

              .favoriter-info {
                display: flex;
                align-items: center;
                font-size: 13px;
                color: #333;
              }

              .note-content {
                font-size: 13px;
                color: #333;
                line-height: 1.4;
                background: #f8f9fa;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #e8e8e8;
                max-height: 60px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }

          .favorite-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            border-left: 3px solid #f94c30;

            .favorite-time {
              margin-bottom: 8px;

              .time-label {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
              }

              .time-value {
                font-size: 13px;
                color: #333;
                font-weight: 500;
              }
            }

            .favorite-note {
              .note-label {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
              }

              .note-content {
                font-size: 13px;
                color: #333;
                line-height: 1.4;
                background: white;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #e8e8e8;
              }
            }
          }

          .card-actions {
            margin-top: auto;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;

            .ant-btn-primary {
              border-radius: 8px;
              font-weight: 500;
              height: 36px;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(249, 76, 48, 0.3);
                transition: all 0.2s ease;
              }
            }
          }
        }
      }

      .card-pagination {
        margin-top: 24px;
        text-align: right;
      }
    }

    .list-view {
      .supplier-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .supplier-name-cell {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .name-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
          }
        }

        .favoriter-cell {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .self-favorited {
          color: #f94c30;
          font-weight: 500;
        }

        .note-cell {
          display: flex;
          align-items: center;
          gap: 8px;

          .note-text {
            flex: 1;
            font-size: 12px;
            color: #333;
          }

          .no-note {
            flex: 1;
            font-size: 12px;
            color: #999;
            font-style: italic;
          }
        }

        :deep(.ant-table-thead > tr > th) {
          background-color: #fafafa;
          font-weight: 600;
        }
      }
    }
  }

  // Modal styles
.supplier-detail-modal {
  .supplier-detail-content {
    .detail-actions {
      margin-top: 24px;
      text-align: center;
    }
  }
}

// Dropdown menu styles
:deep(.ant-dropdown-menu) {
  .danger-item {
    color: #ff4d4f;
    
    &:hover {
      background-color: #fff2f0;
      color: #ff4d4f;
    }
  }
}
}

// Responsive design
@media (max-width: 1200px) {
  .fav-supplier-container {
    .stats-bar {
      gap: 20px;

      .stats-item {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 992px) {
  .fav-supplier-container {
    padding: 16px;

    .quick-actions-bar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .stats-bar {
      flex-direction: column;
      gap: 12px;
    }

    .main-content {
      .card-view {
        .supplier-cards-container {
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .fav-supplier-container {
    .search-filter-section {
      .search-card {
        padding: 16px;

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              margin-bottom: 12px;
            }
          }
        }
      }
    }

    .main-content {
      .card-view {
        .supplier-cards-container {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .fav-supplier-container {
    .search-filter-section {
      .search-card {
        padding: 12px;

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              width: 100% !important;
              max-width: 100% !important;
              flex: 0 0 100% !important;
            }
          }
        }
      }
    }

    .stats-bar {
      padding: 12px 16px;
    }

    .quick-actions-bar {
      padding: 12px 16px;
    }
  }
}
</style>
