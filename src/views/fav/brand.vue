<template>
  <div class="fav-brand-container">
    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-card">
        <div class="filters-panel">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="品牌名称">
                <a-input v-model:value="searchForm.keyword" placeholder="搜索收藏的品牌" allow-clear @pressEnter="handleSearch" />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="品牌类型">
                <a-select v-model:value="searchForm.brandType" placeholder="筛选类型" allow-clear>
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="recommended">推荐品牌</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item style="margin-bottom: 0">
                <a-space>
                  <a-button type="primary" @click="handleSearch" :loading="loading">
                    <i class="fas fa-search"></i>
                    搜索
                  </a-button>
                  <a-button @click="handleResetFilters">
                    <i class="fas fa-undo"></i>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>

    <!-- Tab Section -->
    <div class="tab-section">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange" class="favorites-tabs">
        <a-tab-pane key="my" tab="我的收藏">
          <template #tab>
            <span>
              <i class="fas fa-heart"></i>
              我的收藏 ({{ myFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="public" tab="公开的收藏">
          <template #tab>
            <span>
              <i class="fas fa-share-alt"></i>
              公开的收藏 ({{ publicFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="all" tab="全部">
          <template #tab>
            <span>
              <i class="fas fa-list"></i>
              全部 ({{ totalFavoritesCount }})
            </span>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions-bar">
      <div class="actions-left"></div>

      <div class="actions-right">
        <a-space>
          <!-- View Mode Toggle -->
          <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
            <a-radio-button value="card">
              <i class="fas fa-th-large"></i>
              卡片
            </a-radio-button>
            <a-radio-button value="list">
              <i class="fas fa-list"></i>
              列表
            </a-radio-button>
          </a-radio-group>
        </a-space>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Card View -->
      <div v-if="viewMode === 'card'" class="card-view">
        <div v-if="displayedBrands.length === 0" class="empty-state">
          <a-empty :description="getEmptyDescription()">
            <template #image>
              <i class="fas fa-star-half-alt" style="font-size: 48px; color: #d9d9d9"></i>
            </template>
                            <a-button type="primary" @click="$router.push('/workspace/brand/index')">
              <i class="fas fa-search"></i>
              去发现品牌
            </a-button>
          </a-empty>
        </div>

        <div v-else class="brand-cards-container">
          <div v-for="brand in displayedBrands" :key="brand.id" class="brand-card-wrapper">
            <a-card class="brand-card" :class="{ selected: selectedBrands.includes(brand.id) }">
              <div class="card-content">
                <!-- Header with logo, name, and actions -->
                <div class="brand-header">
                  <!-- Checkbox for my favorites only -->
                  <a-checkbox 
                    v-if="activeTab === 'my' || (activeTab === 'all' && brand.isMyFavorite)"
                    :checked="selectedBrands.includes(brand.id)"
                    @change="() => handleSelectBrand(brand.id)"
                    class="card-checkbox"
                  />

                  <div class="brand-logo">
                    <img v-if="brand.logoUrl" :src="brand.logoUrl" :alt="brand.brandName" @error="handleLogoError" />
                    <div v-else class="default-logo">
                      <i class="fas fa-star"></i>
                    </div>
                  </div>

                  <div class="brand-name-section">
                    <div class="brand-name">{{ brand.brandName }}</div>
                    <div class="brand-tags">
                      <a-tag v-if="brand.brandType === 'recommended'" color="gold" class="recommended-tag" size="small">
                        <i class="fas fa-crown"></i>
                        推荐品牌
                      </a-tag>
                      <a-tag v-if="brand.isPublic && brand.isMyFavorite" color="green" size="small">
                        <i class="fas fa-share-alt"></i>
                        已公开
                      </a-tag>
                    </div>
                  </div>

                  <div class="header-actions">
                    <!-- 操作菜单 -->
                    <a-dropdown v-if="brand.isMyFavorite" :trigger="['click']" placement="bottomRight">
                      <a-button type="text" size="small" class="action-menu-btn">
                        <MoreOutlined />
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="edit-note" @click="handleAddNote(brand)">
                            <i class="fas fa-edit"></i>
                            编辑备注
                          </a-menu-item>
                          <a-menu-item key="toggle-public" @click="handleTogglePublic(brand)">
                            <i :class="brand.isPublic ? 'fas fa-eye-slash' : 'fas fa-share-alt'"></i>
                            {{ brand.isPublic ? '取消公开' : '设为公开' }}
                          </a-menu-item>
                          <a-menu-item key="remove" @click="handleRemoveFromFavorites(brand)" class="danger-item">
                            <i class="fas fa-heart-broken"></i>
                            取消收藏
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                    
                    <!-- 状态指示器 -->
                    <div class="status-indicators">
                      <a-tooltip v-if="brand.isPublic && brand.isMyFavorite" title="已公开">
                        <i class="fas fa-share-alt status-icon public"></i>
                      </a-tooltip>
                      <a-tooltip v-if="brand.isMyFavorite" title="我的收藏">
                        <i class="fas fa-heart status-icon favorite"></i>
                      </a-tooltip>
                    </div>
                  </div>
                </div>

                <!-- Recent Products -->
                <div class="recent-products">
                  <div class="products-title">最近上架商品</div>
                  <div class="products-grid">
                    <div v-for="product in brand.recentProducts" :key="product.id" class="product-item">
                      <div class="product-image">
                        <img v-if="product.imageUrl" :src="product.imageUrl" :alt="product.name" />
                        <div v-else class="default-product-image">
                          <i class="fas fa-cube"></i>
                        </div>
                      </div>
                      <div class="product-info">
                        <div class="product-name" :title="product.name">{{ product.name }}</div>
                        <div class="product-price">¥{{ product.price }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 详细信息区域 -->
                <div class="brand-details">
                  <div class="detail-row">
                    <span class="detail-label">收藏时间</span>
                    <div class="time-value">{{ formatDate(brand.favoriteTime) }}</div>
                  </div>
                  <div class="detail-row" v-if="!brand.isMyFavorite">
                    <span class="detail-label">收藏人</span>
                    <div class="favoriter-info">
                      {{ formatFavoriters(brand.favoriters) }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">备注</span>
                    <div class="note-content">{{ brand.note || '暂无备注' }}</div>
                  </div>
                </div>

                <!-- Action buttons -->
                <div class="card-actions">
                  <a-button type="primary" @click.stop="handleVisitWebsite(brand)" block>
                    <i class="fas fa-external-link-alt"></i>
                    进入主页
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>
        </div>

        <!-- Card View Pagination -->
        <div class="card-pagination" v-if="displayedBrands.length > 0">
          <a-pagination 
            v-model:current="pagination.current" 
            v-model:page-size="pagination.pageSize" 
            :total="pagination.total" 
            :show-size-changer="true" 
            :show-quick-jumper="true" 
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`" 
            @change="handlePaginationChange" 
          />
        </div>
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="list-view">
        <a-table 
          :columns="getTableColumns()" 
          :data-source="displayedBrands" 
          :pagination="tablePagination" 
          :loading="loading" 
          :row-selection="getRowSelection()" 
          row-key="id" 
          @change="handleTableChange" 
          class="brand-table" 
          size="small" 
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="brand-name-cell">
                <div class="brand-logo-mini">
                  <img v-if="record.logoUrl" :src="record.logoUrl" :alt="record.brandName" />
                  <div v-else class="default-logo-mini">
                    <i class="fas fa-star"></i>
                  </div>
                </div>
                <div class="brand-info">
                  <a @click="handleVisitWebsite(record)" style="color: #f94c30; font-weight: 500;">{{ record.brandName }}</a>
                  <div class="brand-tags-mini">
                    <a-tag v-if="record.brandType === 'recommended'" color="gold" size="small">
                      <i class="fas fa-crown"></i>
                      推荐品牌
                    </a-tag>
                    <a-tag v-if="record.isPublic && record.isMyFavorite" color="green" size="small">
                      <i class="fas fa-share-alt"></i>
                      已公开
                    </a-tag>
                  </div>
                </div>
                <i v-if="record.isMyFavorite" class="fas fa-heart bookmark-icon" title="我的收藏"></i>
              </div>
            </template>

            <template v-if="column.key === 'website'">
              <a :href="record.website" target="_blank" rel="noopener noreferrer">{{ record.website }}</a>
            </template>

            <template v-if="column.key === 'favoriter'">
              <div v-if="!record.isMyFavorite" class="favoriter-cell">
                {{ formatFavoriters(record.favoriters) }}
              </div>
              <span v-else class="self-favorited">我的收藏</span>
            </template>

            <template v-if="column.key === 'recentProducts'">
              <div class="products-mini">
                <div v-for="product in record.recentProducts.slice(0, 2)" :key="product.id" class="product-mini">
                  <span class="product-name-mini">{{ product.name }}</span>
                  <span class="product-price-mini">¥{{ product.price }}</span>
                </div>
                <span v-if="record.recentProducts.length > 2" class="more-products">
                  +{{ record.recentProducts.length - 2 }}个
                </span>
              </div>
            </template>

            <template v-if="column.key === 'favoriteTime'">
              {{ formatDate(record.favoriteTime) }}
            </template>

            <template v-if="column.key === 'note'">
              <div class="note-cell">
                <span v-if="record.note" class="note-text">{{ record.note }}</span>
                <span v-else class="no-note">暂无备注</span>
                <a-button v-if="record.isMyFavorite" type="link" size="small" @click="handleAddNote(record)">
                  <i class="fas fa-edit"></i>
                </a-button>
              </div>
            </template>

            <template v-if="column.key === 'actions'">
              <a-space>
                <template v-if="record.isMyFavorite">
                  <a-button type="link" size="small" @click="handleAddNote(record)">
                    <i class="fas fa-edit"></i>
                    编辑备注
                  </a-button>
                  <a-button type="link" size="small" @click="handleTogglePublic(record)">
                    <i :class="record.isPublic ? 'fas fa-eye-slash' : 'fas fa-share-alt'"></i>
                    {{ record.isPublic ? '取消公开' : '设为公开' }}
                  </a-button>
                  <a-button type="link" size="small" danger @click="handleRemoveFromFavorites(record)">
                    <i class="fas fa-heart-broken"></i>
                    取消收藏
                  </a-button>
                </template>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- Modals -->
    <!-- Note Modal -->
    <a-modal v-model:open="showNoteModal" title="编辑备注" width="500px" @ok="handleSaveNote">
      <a-form layout="vertical">
        <a-form-item label="备注信息">
          <a-textarea v-model:value="noteForm.note" placeholder="请输入备注信息..." :rows="4" show-count :maxlength="200" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { MoreOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// ==================== Reactive Data ====================

// Loading states
const loading = ref(false);

// View mode
const viewMode = ref('card'); // 'list', 'card'
const sortBy = ref('addTime');

// Search and filter form
const searchForm = reactive({
  keyword: '',
  brandType: '',
});

// UI states
const showNoteModal = ref(false);

// Tab state
const activeTab = ref('my');

// Selection states
const selectedBrands = ref([]);
const currentEditBrand = ref(null);

// Form states
const noteForm = reactive({
  note: '',
});

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0,
});

// Table pagination (for list view)
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
}));

// Brand data
const brandList = ref([]);
const myFavoritesCount = ref(0);
const publicFavoritesCount = ref(0);
const totalFavoritesCount = ref(0);

// Mock brand data for favorites - updated with new fields
const mockFavoriteBrandData = [
  {
    id: '1',
    brandName: 'Apple',
    logoUrl: 'https://via.placeholder.com/80x80/1890ff/ffffff?text=Apple',
    website: 'https://www.apple.com',
    category: '电子产品',
    brandType: 'recommended',
    favoriteTime: 1640995200000, // 2022-01-01
    note: '全球知名科技品牌，产品质量优秀',
    isFavorited: true,
    isMyFavorite: true,
    isPublic: false,
    favoriters: [
      { name: '张三', id: '1' },
      { name: '李四', id: '2' }
    ],
    recentProducts: [
      {
        id: 'p1',
        name: 'iPhone 15 Pro',
        price: '7999',
        imageUrl: 'https://via.placeholder.com/100x100/007ACC/ffffff?text=iPhone'
      },
      {
        id: 'p2',
        name: 'MacBook Pro',
        price: '12999',
        imageUrl: 'https://via.placeholder.com/100x100/007ACC/ffffff?text=MacBook'
      },
      {
        id: 'p3',
        name: 'AirPods Pro',
        price: '1899',
        imageUrl: 'https://via.placeholder.com/100x100/007ACC/ffffff?text=AirPods'
      }
    ]
  },
  {
    id: '2',
    brandName: '华为',
    logoUrl: 'https://via.placeholder.com/80x80/52c41a/ffffff?text=华为',
    website: 'https://www.huawei.com',
    category: '通信设备',
    brandType: 'recommended',
    favoriteTime: 1641081600000,
    note: '国产优秀品牌，技术实力强',
    isFavorited: true,
    isMyFavorite: true,
    isPublic: true,
    favoriters: [
      { name: '张三', id: '1' }
    ],
    recentProducts: [
      {
        id: 'p4',
        name: 'Mate 60 Pro',
        price: '6999',
        imageUrl: 'https://via.placeholder.com/100x100/FF6B35/ffffff?text=Mate60'
      },
      {
        id: 'p5',
        name: 'MateBook X',
        price: '7999',
        imageUrl: 'https://via.placeholder.com/100x100/FF6B35/ffffff?text=MateBook'
      },
      {
        id: 'p6',
        name: 'FreeBuds Pro',
        price: '1299',
        imageUrl: 'https://via.placeholder.com/100x100/FF6B35/ffffff?text=FreeBuds'
      }
    ]
  },
  {
    id: '3',
    brandName: 'Tesla',
    logoUrl: '',
    website: 'https://www.tesla.com',
    category: '新能源汽车',
    brandType: 'normal',
    favoriteTime: 1641168000000,
    note: '电动汽车领导品牌',
    isFavorited: true,
    isMyFavorite: false,
    isPublic: true,
    favoriters: [
      { name: '王五', id: '3' },
      { name: '赵六', id: '4' },
      { name: '钱七', id: '5' },
      { name: '孙八', id: '6' },
      { name: '李九', id: '7' },
      { name: '周十', id: '8' },
      { name: '吴十一', id: '9' }
    ],
    recentProducts: [
      {
        id: 'p7',
        name: 'Model Y',
        price: '263900',
        imageUrl: 'https://via.placeholder.com/100x100/E53E3E/ffffff?text=ModelY'
      },
      {
        id: 'p8',
        name: 'Model 3',
        price: '231900',
        imageUrl: 'https://via.placeholder.com/100x100/E53E3E/ffffff?text=Model3'
      },
      {
        id: 'p9',
        name: 'Cybertruck',
        price: '399000',
        imageUrl: 'https://via.placeholder.com/100x100/E53E3E/ffffff?text=Cybertruck'
      }
    ]
  },
  {
    id: '4',
    brandName: 'Samsung',
    logoUrl: 'https://via.placeholder.com/80x80/1428a0/ffffff?text=Samsung',
    website: 'https://www.samsung.com',
    category: '电子产品',
    brandType: 'normal',
    favoriteTime: 1641254400000,
    note: '韩国知名电子品牌',
    isFavorited: true,
    isMyFavorite: false,
    isPublic: false,
    favoriters: [
      { name: '李四', id: '2' },
      { name: '王五', id: '3' }
    ],
    recentProducts: [
      {
        id: 'p10',
        name: 'Galaxy S24',
        price: '5999',
        imageUrl: 'https://via.placeholder.com/100x100/1428a0/ffffff?text=Galaxy'
      },
      {
        id: 'p11',
        name: 'Galaxy Book',
        price: '6999',
        imageUrl: 'https://via.placeholder.com/100x100/1428a0/ffffff?text=Book'
      }
    ]
  }
];

// ==================== Computed Properties ====================

// Row selection for table
const rowSelection = computed(() => ({
  selectedRowKeys: selectedBrands.value,
  onChange: (selectedRowKeys) => {
    selectedBrands.value = selectedRowKeys;
  },
}));

// Get columns based on active tab
const getTableColumns = () => {
  const baseColumns = [
    {
      title: '品牌名称',
      dataIndex: 'brandName',
      key: 'name',
      width: 250,
      fixed: 'left',
    },
    {
      title: '品牌网站',
      dataIndex: 'website',
      key: 'website',
      width: 200,
    },
    {
      title: '品牌类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: '最近商品',
      key: 'recentProducts',
      width: 200,
    },
    {
      title: '收藏时间',
      key: 'favoriteTime',
      width: 120,
      sorter: true,
    }
  ];

  // Add favoriter column for public and all tabs
  if (activeTab.value === 'public' || activeTab.value === 'all') {
    baseColumns.push({
      title: '收藏人',
      key: 'favoriter',
      width: 150,
    });
  }

  baseColumns.push({
    title: '备注',
    key: 'note',
    width: 200,
  });

  baseColumns.push({
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
  });

  return baseColumns;
};

// Get row selection based on active tab
const getRowSelection = () => {
  if (activeTab.value === 'my') {
    return rowSelection.value;
  } else {
    return null; // No row selection for public/all tabs
  }
};

// Get displayed brands based on active tab
const displayedBrands = computed(() => {
  let filteredData = [...mockFavoriteBrandData];

  // Filter by active tab
  if (activeTab.value === 'my') {
    filteredData = filteredData.filter(brand => brand.isMyFavorite);
  } else if (activeTab.value === 'public') {
    filteredData = filteredData.filter(brand => brand.isPublic);
  }
  // For 'all' tab, show all favorited brands

  // Keyword search
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase();
    filteredData = filteredData.filter((brand) => brand.brandName.toLowerCase().includes(keyword));
  }

  // Brand type filter
  if (searchForm.brandType) {
    filteredData = filteredData.filter((brand) => brand.brandType === searchForm.brandType);
  }

  // Sort data
  if (sortBy.value === 'addTime') {
    filteredData.sort((a, b) => b.favoriteTime - a.favoriteTime);
  } else if (sortBy.value === 'brandName') {
    filteredData.sort((a, b) => a.brandName.localeCompare(b.brandName));
  }

  // Update pagination
  pagination.total = filteredData.length;
  const startIndex = (pagination.current - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;

  return filteredData.slice(startIndex, endIndex);
});

// Get empty description based on active tab
const getEmptyDescription = () => {
  if (activeTab.value === 'my') {
    return '暂无我的收藏品牌';
  } else if (activeTab.value === 'public') {
    return '暂无公开的收藏品牌';
  } else {
    return '暂无收藏的品牌';
  }
};

// ==================== Methods ====================

// Handle search
const handleSearch = async () => {
  loading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 500));
    // displayedBrands computed property will handle the filtering
  } catch (error) {
    message.error('搜索失败，请重试');
  } finally {
    loading.value = false;
  }
};

// Handle reset filters
const handleResetFilters = () => {
  searchForm.keyword = '';
  searchForm.brandType = '';
  pagination.current = 1;
  handleSearch();
};

// Handle tab change
const handleTabChange = (key) => {
  activeTab.value = key;
  selectedBrands.value = [];
  pagination.current = 1;
  handleSearch();
};

// Handle pagination change
const handlePaginationChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  handleSearch();
};

// Handle table change
const handleTableChange = (pag, _filters, sorter) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;

  if (sorter.field) {
    if (sorter.field === 'favoriteTime') {
      sortBy.value = 'addTime';
    }
  }

  handleSearch();
};

// Handle brand selection
const handleSelectBrand = (brandId) => {
  const index = selectedBrands.value.indexOf(brandId);
  if (index > -1) {
    selectedBrands.value.splice(index, 1);
  } else {
    selectedBrands.value.push(brandId);
  }
};

// Handle logo error
const handleLogoError = (event) => {
  event.target.style.display = 'none';
  const defaultLogo = event.target.nextElementSibling;
  if (defaultLogo) {
    defaultLogo.style.display = 'flex';
  }
};

// Handle visit website
const handleVisitWebsite = (brand) => {
  window.open(brand.website, '_blank', 'noopener,noreferrer');
};

// Handle contact
const handleContact = (brand) => {
  message.info(`正在联系 ${brand.brandName}...`);
};

// Handle add note
const handleAddNote = (brand) => {
  currentEditBrand.value = brand;
  noteForm.note = brand.note || '';
  showNoteModal.value = true;
};

// Handle save note
const handleSaveNote = () => {
  if (currentEditBrand.value) {
    currentEditBrand.value.note = noteForm.note;
    message.success('备注保存成功');
    showNoteModal.value = false;
  }
};

// Handle remove from favorites
const handleRemoveFromFavorites = (brand) => {
  brand.isFavorited = false;
  brand.isMyFavorite = false;
  brand.isPublic = false;
  message.success(`已将 ${brand.brandName} 从收藏夹移除`);
  
  // Update counts
  myFavoritesCount.value--;
  if (brand.isPublic) {
    publicFavoritesCount.value--;
  }
  totalFavoritesCount.value--;
};

// Handle add to favorites (重新收藏)
const handleAddToFavorites = (brand) => {
  brand.isFavorited = true;
  brand.isMyFavorite = true;
  brand.isPublic = false;
  brand.favoriteTime = Date.now();
  message.success(`已将 ${brand.brandName} 添加到收藏夹`);
  
  // Update counts
  myFavoritesCount.value++;
  totalFavoritesCount.value++;
};

// Handle toggle public
const handleTogglePublic = (brand) => {
  brand.isPublic = !brand.isPublic;
  message.success(`已将 ${brand.brandName} 设为${brand.isPublic ? '公开' : '私密'}`);
  
  // Update public count
  if (brand.isPublic) {
    publicFavoritesCount.value++;
  } else {
    publicFavoritesCount.value--;
  }
};

// Format favoriters display
const formatFavoriters = (favoriters) => {
  if (!favoriters || favoriters.length === 0) return '';
  
  if (favoriters.length === 1) {
    return favoriters[0].name;
  } else if (favoriters.length <= 3) {
    return favoriters.map(f => f.name).join('、');
  } else {
    return `${favoriters[0].name}等${favoriters.length}人`;
  }
};

// Utility functions
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN');
};

// ==================== Lifecycle ====================

onMounted(() => {
  // Initialize counts
  myFavoritesCount.value = mockFavoriteBrandData.filter(brand => brand.isMyFavorite).length;
  publicFavoritesCount.value = mockFavoriteBrandData.filter(brand => brand.isPublic).length;
  totalFavoritesCount.value = mockFavoriteBrandData.filter(brand => brand.isFavorited).length;
});
</script>

<style lang="less" scoped>
.fav-brand-container {
  min-height: 100vh;

  .search-filter-section {
    .search-card {
      background: white;
      border-radius: 8px;

      .filters-panel {
        :deep(.ant-form-item) {
          margin-bottom: 16px;
        }

        :deep(.ant-form-item-label) {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .tab-section {
    margin-bottom: 16px;
    background: white;
    border-radius: 8px;

    .favorites-tabs {
      :deep(.ant-tabs-nav-wrap) {
        margin-bottom: 0;
      }
      :deep(.ant-tabs-nav) {
        margin-bottom: 0;
      }
      :deep(.ant-tabs-tab) {
        font-weight: 500;
        color: #333;
      }
      :deep(.ant-tabs-tab-active) {
        color: #f94c30;
      }
      :deep(.ant-tabs-ink-bar) {
        background-color: #f94c30;
      }
    }
  }

  .quick-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .main-content {
    .empty-state {
      background: white;
      border-radius: 8px;
      padding: 60px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .card-view {
      .brand-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 20px;

        .brand-card-wrapper {
          display: flex;
        }
      }

      .brand-card {
        border-radius: 12px;
        transition: all 0.3s ease;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        width: 100%;
        height: 100%;
        min-height: 420px;

        &:hover {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          transform: translateY(-2px);
          border-color: #d9d9d9;
        }

        &.selected {
          border-color: #f94c30;
          box-shadow: 0 0 0 2px rgba(249, 76, 48, 0.2);
        }

        :deep(.ant-card-body) {
          padding: 20px;
          height: 100%;
        }

        .card-content {
          display: flex;
          flex-direction: column;
          height: 100%;

          .brand-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;

            .card-checkbox {
              margin-top: 4px;
              flex-shrink: 0;
            }

            .brand-logo {
              width: 50px;
              height: 50px;
              flex-shrink: 0;
              border-radius: 8px;
              overflow: hidden;
              border: 1px solid #e8e8e8;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .default-logo {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
                color: white;

                i {
                  font-size: 20px;
                }
              }
            }

            .brand-name-section {
              flex: 1;
              min-width: 0;

              .brand-name {
                font-weight: 600;
                color: #333;
                font-size: 16px;
                line-height: 1.4;
                margin-bottom: 6px;
                word-break: break-word;
              }

              .brand-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;

                .recommended-tag {
                  font-weight: 500;
                  border: none;

                  i {
                    margin-right: 4px;
                  }
                }
              }
            }

            .header-actions {
              flex-shrink: 0;
              display: flex;
              align-items: center;
              gap: 8px;

              .action-menu-btn {
                color: #666;
                transition: all 0.2s ease;
                
                &:hover {
                  color: #f94c30;
                  background-color: #f5f5f5;
                }
              }

              .status-indicators {
                display: flex;
                gap: 4px;
                align-items: center;

                .status-icon {
                  font-size: 12px;
                  padding: 2px;
                  border-radius: 50%;

                  &.favorite {
                    color: #f94c30;
                  }

                  &.public {
                    color: #52c41a;
                  }
                }
              }
            }
          }

          .recent-products {
            margin-bottom: 16px;

            .products-title {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-bottom: 8px;
            }

            .products-grid {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 8px;

              .product-item {
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                overflow: hidden;
                transition: all 0.2s;

                &:hover {
                  border-color: #f94c30;
                  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.15);
                }

                .product-image {
                  aspect-ratio: 1;
                  overflow: hidden;

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }

                  .default-product-image {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f5f5;
                    color: #999;

                    i {
                      font-size: 24px;
                    }
                  }
                }

                .product-info {
                  padding: 6px 8px;

                  .product-name {
                    font-size: 12px;
                    color: #333;
                    line-height: 1.2;
                    margin-bottom: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .product-price {
                    font-size: 12px;
                    color: #f94c30;
                    font-weight: 500;
                  }
                }
              }
            }
          }

          .brand-details {
            margin-bottom: 16px;
            flex: 1;

            .detail-row {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;
              font-size: 13px;

              &:last-child {
                margin-bottom: 0;
              }

              .detail-label {
                color: #666;
                font-weight: 400;
                flex-shrink: 0;
                margin-right: 8px;
              }

              .detail-value,
              .time-value,
              .favoriter-info,
              .note-content {
                color: #333;
                font-weight: 500;
                text-align: right;
                flex: 1;
                word-break: break-word;
              }

              .note-content {
                max-height: 60px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }

          .card-actions {
            margin-top: auto;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;

            .ant-btn-primary {
              border-radius: 8px;
              font-weight: 500;
              height: 36px;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(249, 76, 48, 0.3);
                transition: all 0.2s ease;
              }
            }
          }
        }
      }

      .card-pagination {
        margin-top: 24px;
        text-align: center;
      }
    }

    .list-view {
      .brand-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .brand-name-cell {
          display: flex;
          align-items: center;
          gap: 12px;

          .brand-logo-mini {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid #e8e8e8;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .default-logo-mini {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
              color: white;

              i {
                font-size: 12px;
              }
            }
          }

          .brand-info {
            flex: 1;
            min-width: 0;

            .brand-tags-mini {
              margin-top: 2px;
            }
          }

          .bookmark-icon {
            color: #f94c30;
            font-size: 14px;
            flex-shrink: 0;
          }
        }

        .favoriter-cell {
          font-size: 13px;
          color: #333;
        }

        .self-favorited {
          color: #f94c30;
          font-weight: 500;
        }

        .note-cell {
          display: flex;
          align-items: center;
          gap: 8px;

          .note-text {
            flex: 1;
            font-size: 12px;
            color: #333;
          }

          .no-note {
            flex: 1;
            font-size: 12px;
            color: #999;
            font-style: italic;
          }
        }

        .products-mini {
          font-size: 12px;

          .product-mini {
            display: block;
            margin-bottom: 4px;

            .product-name-mini {
              color: #333;
              margin-right: 8px;
            }

            .product-price-mini {
              color: #f94c30;
              font-weight: 500;
            }
          }

          .more-products {
            color: #999;
            font-style: italic;
          }
        }

        :deep(.ant-table-thead > tr > th) {
          background-color: #fafafa;
          font-weight: 600;
        }
      }
    }
  }

// Dropdown menu styles
:deep(.ant-dropdown-menu) {
  .danger-item {
    color: #ff4d4f;
    
    &:hover {
      background-color: #fff2f0;
      color: #ff4d4f;
    }
  }
}
}

// Responsive design
@media (max-width: 1200px) {
  .fav-brand-container {
    .main-content {
      .card-view {
        .brand-cards-container {
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .fav-brand-container {
    padding: 16px;

    .quick-actions-bar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .main-content {
      .card-view {
        .brand-cards-container {
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .fav-brand-container {
    .search-filter-section {
      .search-card {
        padding: 16px;

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              margin-bottom: 12px;
            }
          }
        }
      }
    }

    .main-content {
      .card-view {
        .brand-cards-container {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .fav-brand-container {
    .search-filter-section {
      .search-card {
        padding: 12px;

        .filters-panel {
          :deep(.ant-row) {
            .ant-col {
              width: 100% !important;
              max-width: 100% !important;
              flex: 0 0 100% !important;
            }
          }
        }
      }
    }

    .main-content {
      .card-view {
        .brand-card {
          .recent-products {
            .products-grid {
              grid-template-columns: repeat(2, 1fr);
            }
          }
        }
      }
    }
  }
}
</style>