<template>
  <a-config-provider
    :locale="locale"
    :theme="{
      token: {
        borderRadius: 2,

        colorPrimary: '#f94c30',
        colorInfo: '#f94c30',
        colorLink: '#F94C30',
        colorLinkActive: '#d4301e',
        colorLinkHover: '#ff7559',
      },
      components: {
        Slider: {
          colorPrimaryBorder: '#f94c30',
          colorPrimaryBorderHover: '#e86b52',
        },
        Menu: {
          colorItemTextHoverHorizontal: '#1677ff',
          controlItemBgActive: '#2b1513',
          colorPrimaryBorder: '#59251c',
          colorErrorBg: '#2c1618',
        },
      },
    }"
  >
    <!-- 独立页面布局 -->
    <div v-if="isStandalonePage" class="standalone-container">
      <router-view></router-view>
    </div>

    <!-- 工作台布局 -->
    <div v-else class="app-container">
      <router-view></router-view>
    </div>
  </a-config-provider>
</template>

<script setup>
import { computed } from 'vue';
import 'ant-design-vue/dist/reset.css';
import { useRoute } from 'vue-router';

// 获取路由实例
const route = useRoute();

// 判断是否为独立页面
const isStandalonePage = computed(() => {
  return route.meta?.standalone === true;
});
</script>

<style>
/* 独立页面容器样式 */
.standalone-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.app-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}
</style>
